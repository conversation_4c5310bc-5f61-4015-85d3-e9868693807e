<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use App\Models\Route as RouteModel;


class SaveRoutesToDatabase extends Command
{
    protected $signature = 'routes:save-to-database';
    protected $description = 'Mevcut tüm rotaları veritabanına kaydeder';

    public function handle()
    {
        $routes = Route::getRoutes();

        foreach ($routes as $route) {
            if ($route->getName()) {
                $category_name = $route->getDesc()->first();

                RouteModel::updateOrCreate(
                    ['route_name' => $route->getName()],
                    [
                        'name' => $this->getRouteDescription($route->getName()),
                        'route_name' => $route->getName(),
                        'category_name' => ucfirst($category_name),
                    ]
                );
            }
        }

        $this->info('Rotalar başarıyla veritabanına kaydedildi!');
    }

    protected function getRouteDescription($routeName)
    {
        $descriptions = [
            'list' => 'Listele',
            'form' => 'Form Görüntüle',
            'save' => 'Kaydet',
            'delete' => 'Sil',
            'detail' => 'Detay Görüntüle',
            'status' => 'Durum Değiştir',
            'excel' => 'Excel Dışa Aktar',
            'pdf' => 'PDF Dışa Aktar',

        ];

        foreach ($descriptions as $key => $desc) {
            if (strpos($routeName, $key) !== false) {
                return $desc;
            }
        }

        return 'Bilinmeyen İşlem';
    }
}
