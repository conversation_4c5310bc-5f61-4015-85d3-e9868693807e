<?php

namespace App\Exports;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;
use Carbon\Carbon;

class ExportOffer
{
    protected $items;
    protected $offer;

    public function __construct($items, $offer = null)
    {
        $this->items = $items;
        $this->offer = $offer;
    }

    // Mevcut export metodu (Excel için)
    public function export(): Spreadsheet
    {
        // Mevcut Excel kodunuz (değişmeden kalıyor)
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Teklif Formu');

        // A4 sayfa ayarları - dikey yönlendirme - tamamının sığması için optimize edildi
        $sheet->getPageSetup()
            ->setOrientation(PageSetup::ORIENTATION_PORTRAIT)
            ->setPaperSize(PageSetup::PAPERSIZE_A4)
            ->setFitToPage(true)
            ->setFitToWidth(1)
            ->setFitToHeight(1); // Yüksekliğe de sığdır

        // Marjinleri minimum seviyeye indir
        $sheet->getPageMargins()
            ->setTop(0.2)
            ->setRight(0.2)
            ->setBottom(0.2)
            ->setLeft(0.2)
            ->setHeader(0.05)
            ->setFooter(0.05);

        $row = 1;

        // TEKLİF BİLGİLERİ ve CARİ BİLGİLERİ BAŞLIKLARI
        $sheet->setCellValue("A{$row}", 'TEKLİF BİLGİLERİ');
        $sheet->mergeCells("A{$row}:D{$row}");
        $sheet->setCellValue("E{$row}", 'CARİ BİLGİLERİ');
        $sheet->mergeCells("E{$row}:I{$row}");
        $sheet->getStyle("A{$row}:I{$row}")->applyFromArray($this->getHeaderStyle());
        $row++;

        // TEKLİF BİLGİLERİ DETAYLARI
        $sheet->setCellValue("A{$row}", 'Teklif No:');
        $sheet->setCellValueExplicit("B{$row}", $this->offer->getOriginal('offer_number') ?? ($this->offer->offer_number ?? '-'), DataType::TYPE_STRING);
        $sheet->mergeCells("B{$row}:D{$row}");
        $current = $this->offer->current;
        $sheet->setCellValue("E{$row}", 'Cari Adı:');
        $sheet->setCellValue("F{$row}", ($current->name ?? '-') . ' ' . ($current->surname ?? ''));
        $sheet->mergeCells("F{$row}:I{$row}");
        $row++;

        $sheet->setCellValue("A{$row}", 'Teklif Tarihi:');
        $sheet->setCellValue("B{$row}", $this->offer->offer_date ? Carbon::parse($this->offer->offer_date)->format('d.m.Y') : '-');
        $sheet->mergeCells("B{$row}:D{$row}");
        $sheet->setCellValue("E{$row}", 'Telefon:');
        $sheet->setCellValue("F{$row}", $current->phone ?? '-');
        $sheet->mergeCells("F{$row}:I{$row}");
        $row++;

        $sheet->setCellValue("A{$row}", 'Depo:');
        $sheet->setCellValue("B{$row}", $this->offer->warehouse->name ?? '-');
        $sheet->mergeCells("B{$row}:D{$row}");
        $sheet->mergeCells('E4:E5');
        $sheet->setCellValue('E4', 'Adres:');
        $sheet->mergeCells('F4:I5');
        $address = $this->offer->shipping_address ?? '';
        if ($current->address) $address .= ', ' . $current->address;
        if ($current->city?->name) $address .= ', ' . $current->city->name;
        if ($current->country?->name) $address .= ', ' . $current->country->name;
        $sheet->setCellValue('F4', trim($address, ', ') ?: '-');
        $sheet->getStyle('F4')->getAlignment()->setWrapText(true);
        $sheet->getStyle('F4:I5')->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => 'FFEEEEEE'],
                ],
            ],
        ]);
        $sheet->mergeCells('A6:I6');
        $row++;
        $sheet->setCellValue("A{$row}", 'Teklif Son Tarihi:');
        $sheet->setCellValue("B{$row}", $this->offer->offer_deadline ? Carbon::parse($this->offer->offer_deadline)->format('d.m.Y') : '-');
        $sheet->mergeCells("B{$row}:D{$row}");
        $row++;
        $row++;

        $sheet->getStyle("A2:D" . ($row - 2))->applyFromArray($this->getInfoTableStyle());
        $sheet->getStyle("E2:I" . ($row - 2))->applyFromArray($this->getInfoTableStyle());
        $sheet->getStyle("A2:A" . ($row - 2))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle("E2:E" . ($row - 2))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);

        $sheet->setCellValue("A{$row}", 'ÜRÜN LİSTESİ');
        $sheet->mergeCells("A{$row}:I{$row}");
        $sheet->getStyle("A{$row}:I{$row}")->applyFromArray($this->getProductHeaderStyle());
        $row++;

        $headers = ['Sıra No', 'Item No', 'Ürün Kodu', 'Ürün Adı', 'Miktar', 'Birim', 'Birim Fiyat', 'KDV', 'Toplam'];
        $sheet->fromArray($headers, null, "A{$row}");
        $sheet->getStyle("A{$row}:I{$row}")->applyFromArray($this->getProductHeaderStyle());
        $row++;

        $i = 1;
        foreach ($this->items as $product) {
            $sheet->setCellValue("A{$row}", $i++);
            $sheet->setCellValueExplicit("B{$row}", $product->item_no ?? '-', DataType::TYPE_STRING);
            $sheet->setCellValue("C{$row}", $product->product_code);
            $sheet->setCellValue("D{$row}", $product->product_name);
            $sheet->setCellValue("E{$row}", $product->quantity);
            $sheet->setCellValue("F{$row}", $product->unit);
            $sheet->setCellValue("G{$row}", (float) $product->unit_price);
            $sheet->setCellValue("H{$row}", '%' . $product->vat_rate . ' (' . ($product->vat_status == 0 ? 'Hariç' : 'Dahil') . ')');
            $sheet->setCellValue("I{$row}", (float) $product->total_price);
            $sheet->getStyle("G{$row}")->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle("I{$row}")->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle("A{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle("E{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle("F{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle("G{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle("H{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $sheet->getStyle("I{$row}")->getAlignment()->setHorizontal(Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle("B{$row}")->getAlignment()->setWrapText(true);
            $sheet->getStyle("D{$row}")->getAlignment()->setWrapText(true); // Ürün adı için wrap text
            $row++;
        }

        $sheet->mergeCells("A{$row}:G{$row}");
        $sheet->setCellValue("H{$row}", 'Ara Toplam:');
        $sheet->setCellValue("I{$row}", (float) $this->offer->total_price);
        $sheet->getStyle("H{$row}:I{$row}")->applyFromArray($this->getTotalRowStyle());
        $sheet->getStyle("I{$row}")->getNumberFormat()->setFormatCode('#,##0.00');
        $row++;

        $sheet->mergeCells("A{$row}:G{$row}");
        $sheet->setCellValue("H{$row}", 'KDV:');
        $sheet->setCellValue("I{$row}", (float) $this->offer->vat_amount);
        $sheet->getStyle("H{$row}:I{$row}")->applyFromArray($this->getTotalRowStyle());
        $sheet->getStyle("I{$row}")->getNumberFormat()->setFormatCode('#,##0.00');
        $row++;

        $sheet->mergeCells("A{$row}:G{$row}");
        $sheet->setCellValue("H{$row}", 'Genel Toplam:');
        $sheet->setCellValue("I{$row}", (float) $this->offer->total_amount);
        $sheet->getStyle("H{$row}:I{$row}")->applyFromArray($this->getTotalRowStyle());
        $sheet->getStyle("I{$row}")->getNumberFormat()->setFormatCode('#,##0.00');

        $lastRow = $row;
        $sheet->getStyle("A1:I{$lastRow}")->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN)->setColor(new Color(Color::COLOR_BLACK));
        $sheet->getStyle("A1:I{$lastRow}")->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle("A1:I{$lastRow}")->getAlignment()->setWrapText(true);
        $sheet->getStyle("A1:I{$lastRow}")->getBorders()->getOutline()->setBorderStyle(Border::BORDER_MEDIUM)->setColor(new Color(Color::COLOR_BLACK));

        // Kolon genişliklerini daha da küçült - A4 dikey sayfaya sığması için
        $sheet->getColumnDimension('A')->setWidth(5);   // Sıra No
        $sheet->getColumnDimension('B')->setWidth(8);   // Item No
        $sheet->getColumnDimension('C')->setWidth(10);  // Ürün Kodu
        $sheet->getColumnDimension('D')->setWidth(18);  // Ürün Adı
        $sheet->getColumnDimension('E')->setWidth(6);   // Miktar
        $sheet->getColumnDimension('F')->setWidth(6);   // Birim
        $sheet->getColumnDimension('G')->setWidth(10);  // Birim Fiyat
        $sheet->getColumnDimension('H')->setWidth(8);   // KDV
        $sheet->getColumnDimension('I')->setWidth(10);  // Toplam

        // Satır yüksekliğini daha da küçült
        $sheet->getDefaultRowDimension()->setRowHeight(10);

        // Tüm hücrelere küçük font boyutu uygula
        $sheet->getStyle('A1:I' . ($row + 10))->getFont()->setSize(8);

        return $spreadsheet;
    }

    public function exportToHtml(): string
    {
        $current = $this->offer->current;
        $address = $this->offer->shipping_address ?? '';
        if ($current->neighborhood?->name) $address .= ', ' . $current->neighborhood->name;
        if ($current->city?->name) $address .= ', ' . $current->city->name;
        if ($current->country?->name) $address .= ', ' . $current->country->name;
        $address = trim($address, ', ') ?: '-';

        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
            <style>
                body { font-family: "DejaVu Sans", sans-serif; font-size: 12px; margin: 0; padding: 0; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #000; padding: 8px; text-align: left; vertical-align: top; }
                th { background-color: #4E4E4E; color: white; text-align: center; }
                .header { background-color: #F8F8F8; font-weight: bold; text-align: center; color: #333333; }
                .info-table td.label { text-align: right; background-color: #F8F8F8; font-weight: bold; color: #333333; width: 15%; }
                .info-table td { background-color: #FFFFFF; color: #333333; }
                .info-table td.address { word-wrap: break-word; word-break: break-word; max-width: 200px; line-height: 1.4; }
                .total-row { font-weight: bold; background-color: #F8F8F8; text-align: right; color: #333333; }
                .center { text-align: center; }
                .right { text-align: right; }
                .product-table { margin-top: 20px; }
            </style>
        </head>
        <body>
            <table>
                <tr>
                    <td colspan="4" class="header">TEKLİF BİLGİLERİ</td>
                    <td colspan="5" class="header">CARİ BİLGİLERİ</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Teklif No:</td>
                    <td colspan="3">' . htmlspecialchars($this->offer->getOriginal('offer_number') ?? ($this->offer->offer_number ?? '-')) . '</td>
                    <td class="label">Cari Adı:</td>
                    <td colspan="4">' . htmlspecialchars(($current->name ?? '-') . ' ' . ($current->surname ?? '')) . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Teklif Tarihi:</td>
                    <td colspan="3">' . ($this->offer->offer_date ? Carbon::parse($this->offer->offer_date)->format('d.m.Y') : '-') . '</td>
                    <td class="label">Telefon:</td>
                    <td colspan="4">' . htmlspecialchars($current->phone ?? '-') . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Depo:</td>
                    <td colspan="3">' . htmlspecialchars($this->offer->warehouse?->name ?? '-') . '</td>
                    <td class="label">Adres:</td>
                    <td colspan="4" class="address">' . htmlspecialchars($address) . '</td>
                </tr>
                <tr class="info-table">
                    <td class="label">Teklif Son Tarihi:</td>
                    <td colspan="3">' . ($this->offer->offer_deadline ? Carbon::parse($this->offer->offer_deadline)->format('d.m.Y') : '-') . '</td>
                    <td colspan="5"></td>
                </tr>
            </table>
            <table class="product-table">
                <tr>
                    <td colspan="9" class="header">ÜRÜN LİSTESİ</td>
                </tr>
                <tr>
                    <th>Sıra No</th>
                    <th>Item No</th>
                    <th>Ürün Kodu</th>
                    <th>Ürün Adı</th>
                    <th>Miktar</th>
                    <th>Birim</th>
                    <th>Birim Fiyat</th>
                    <th>KDV</th>
                    <th>Toplam</th>
                </tr>';

        $i = 1;
        foreach ($this->items as $product) {
            $html .= '
                <tr>
                    <td class="center">' . $i++ . '</td>
                    <td>' . htmlspecialchars($product->item_no ?? '-') . '</td>
                    <td>' . htmlspecialchars($product->product_code ?? '-') . '</td>
                    <td>' . htmlspecialchars($product->product_name ?? '-') . '</td>
                    <td class="center">' . ($product->quantity ?? 0) . '</td>
                    <td class="center">' . htmlspecialchars($product->unit ?? '-') . '</td>
                    <td class="right">' . number_format((float) ($product->unit_price ?? 0), 2, ',', '.') . '</td>
                    <td class="center">' . htmlspecialchars('%' . ($product->vat_rate ?? 0) . ' (' . ($product->vat_status == 0 ? 'Hariç' : 'Dahil') . ')') . '</td>
                    <td class="right">' . number_format((float) ($product->total_price ?? 0), 2, ',', '.') . '</td>
                </tr>';
        }

        $html .= '
                <tr>
                    <td colspan="7"></td>
                    <td class="total-row">Ara Toplam:</td>
                    <td class="total-row right">' . number_format((float) ($this->offer->total_price ?? 0), 2, ',', '.') . '</td>
                </tr>
                <tr>
                    <td colspan="7"></td>
                    <td class="total-row">KDV:</td>
                    <td class="total-row right">' . number_format((float) ($this->offer->vat_amount ?? 0), 2, ',', '.') . '</td>
                </tr>
                <tr>
                    <td colspan="7"></td>
                    <td class="total-row">Genel Toplam:</td>
                    <td class="total-row right">' . number_format((float) ($this->offer->total_amount ?? 0), 2, ',', '.') . '</td>
                </tr>
            </table>
        </body>
        </html>';

        return $html;
    }

    public function getHeaderStyle()
    {
        return [
            'font' => ['bold' => true, 'size' => 8, 'color' => ['argb' => 'FF333333']], // Font boyutu daha da küçültüldü
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFF8F8F8']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['argb' => 'FFEEEEEE']]],
        ];
    }

    public function getInfoTableStyle()
    {
        return [
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['argb' => 'FFEEEEEE']]],
            'alignment' => ['vertical' => Alignment::VERTICAL_CENTER],
        ];
    }

    public function getProductHeaderStyle()
    {
        return [
            'font' => ['bold' => true, 'size' => 8, 'color' => ['argb' => 'FFFFFFFF']], // Font boyutu daha da küçültüldü
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FF4E4E4E']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER, 'vertical' => Alignment::VERTICAL_CENTER],
            'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN, 'color' => ['argb' => 'FF4E4E4E']]],
        ];
    }

    public function getTotalRowStyle()
    {
        return [
            'font' => ['bold' => true],
            'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['argb' => 'FFF8F8F8']],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT],
        ];
    }
}
