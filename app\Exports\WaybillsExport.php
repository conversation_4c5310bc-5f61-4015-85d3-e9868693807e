<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class WaybillsExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $waybills;

    public function __construct($waybills)
    {
        $this->waybills = $waybills;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->waybills;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            '<PERSON>rsaliye No',
            '<PERSON><PERSON>h',
            '<PERSON><PERSON>',
            'Tutar',
            'Tür',
            'Durum',
            '<PERSON><PERSON>ıklama',
            'Oluşturulma Tarihi'
        ];
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->waybill_no,
            $row->waybill_date->format('d.m.Y'),
            $row->current ? $row->current->name : '',
            number_format($row->total_amount, 2, ',', '.') . ' ₺',
            $row->waybill_type == 1 ? 'Satış İrsaliyesi' : 'Alış İrsaliyesi',
            $this->getStatusName($row->status),
            $row->description,
            $row->created_at->format('d.m.Y H:i')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Başlık satırı için stil
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * Durum adını döndür
     */
    private function getStatusName($status)
    {
        $statuses = [
            0 => 'Beklemede',
            1 => 'Onaylandı',
            2 => 'İptal Edildi',
        ];

        return $statuses[$status] ?? 'Bilinmiyor';
    }
}
