<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Http\Requests\Backend\AccountVoucherRequest;
use App\Models\AccountVoucher;
use App\Models\Current;
use App\Models\Balance;
use App\Models\VoucherType;
use App\Helpers\ExcelExport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\InvoiceCounter;
use App\Libraries\Helpers;

class AccountVouchersController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Hesap Fişleri';
        $this->page = 'account_vouchers';
        $this->model = new AccountVoucher();
        $this->relation = ['current', 'voucherType'];

        $this->view = (object)[
            'breadcrumb' => [
                'Cari Hesap Fişleri' => route('backend.account_vouchers_list'),
            ],
        ];

        view()->share('currents', Current::where('is_active', 1)->get());
        view()->share('voucherTypes', VoucherType::where('is_active', 1)->get());

        parent::__construct();
    }

    public function form(Request $request, $unique = NULL)
    {
        view()->share('current', Current::where('is_active', 1)->get());
        return parent::form($request, $unique);
    }

    public function datatableHook($obj)
    {
        return $obj
            ->addColumn('current_name', function ($row) {
                return $row->current ? $row->current->name : '';
            })
            ->addColumn('voucher_date', function ($row) {
                return $row->voucher_date ? $row->voucher_date->format('d.m.Y') : '';
            })
            ->addColumn('voucher_type_name', function ($row) {
                return $row->voucher_type_name;
            })
            ->addColumn('amount_formatted', function ($row) {
                $currency = $row->currency === 'TRY' ? '₺' : $row->currency;
                return number_format($row->amount, 2, ',', '.') . ' ' . $currency;
            })
            ->addColumn('is_active', function ($row) {
                return $row->is_active == 1
                    ? '<span class="badge" style="background-color: #BBF7D0; color: #166534;">Aktif</span>'
                    : '<span class="badge" style="background-color: #FECACA; color: #991B1B;">Pasif</span>';
            })
            ->rawColumns(['is_active']);
    }

    public function list(Request $request)
    {
        if ($request->has('export') && $request->export == 'excel') {
            return $this->exportAccountVouchersToExcel();
        }
        return parent::list($request);
    }

    private function generateVoucherNumber()
    {
        try {
            $currentDate = now();
            $year = $currentDate->format('Y');
            $month = $currentDate->format('m');

            $counter = InvoiceCounter::where('prefix', 'CHF')
                ->where('month', $month)
                ->where('year', $year)
                ->first();

            if ($counter) {
                $counter->last_number = $counter->last_number + 1;
                $counter->save();
            } else {
                $counter = InvoiceCounter::create([
                    'prefix' => 'CHF',
                    'month' => $month,
                    'year' => $year,
                    'last_number' => 1
                ]);
            }

            $voucherNumber = Helpers::accountVoucherNumber($month, $year, $counter->last_number, 5);
            Log::info("Oluşturulan cari hesap fişi numarası: " . $voucherNumber);
            return $voucherNumber;

        } catch (\Exception $e) {
            Log::error("Cari hesap fişi numarası oluşturulurken hata: " . $e->getMessage());
            throw $e;
        }
    }

    public function getNextVoucherNumber()
    {
        try {
            $voucherNo = $this->generateVoucherNumber();
            Log::info("AJAX isteği ile cari hesap fişi numarası oluşturuldu: " . $voucherNo);

            return response()->json([
                'success' => true,
                'voucher_no' => $voucherNo
            ]);
        } catch (\Exception $e) {
            Log::error("Cari hesap fişi numarası oluşturulurken hata: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Cari hesap fişi numarası oluşturulurken hata: ' . $e->getMessage()
            ], 500);
        }
    }

    public function formHook($item)
    {
        if (!$item->id) {
            $item->voucher_no = $this->generateVoucherNumber();
            $item->voucher_date = $item->voucher_date ?? now();
            $item->is_active = $item->is_active ?? 1;
        } else if (empty($item->voucher_no)) {
            $item->voucher_no = $this->generateVoucherNumber();
        }
        return $item;
    }

    public function saveHook(Request $request)
    {
        $data = $request instanceof AccountVoucherRequest ? $request->validated() : $request->all();
        if (empty($data['voucher_no'])) {
            $data['voucher_no'] = $this->generateVoucherNumber();
        }
        return $data;
    }

    public function saveBack($obj)
    {
        try {
            if (!$obj->current_id) {
                return redirect()->route('backend.account_vouchers_list')->with('error', 'Cari seçilmediği için balance güncellenemedi!');
            }

            $balanceExists = DB::table('balances')->where('current_id', $obj->current_id)->exists();

            if ($balanceExists) {
                DB::statement(
                    "UPDATE balances SET debit_balance = GREATEST(0, debit_balance - ?), updated_at = ? WHERE current_id = ?",
                    [floatval($obj->amount), now(), $obj->current_id]
                );
            } else {
                DB::table('balances')->insert([
                    'current_id' => $obj->current_id,
                    'debit_balance' => 0,
                    'credit_balance' => 0,
                    'is_active' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Balance güncellenirken hata: " . $e->getMessage());
            return redirect()->route('backend.account_vouchers_list')->with('error', 'Balance güncellenirken bir hata oluştu: ' . $e->getMessage());
        }

        return redirect()->route('backend.account_vouchers_list')->with('success', 'Cari hesap fişi başarıyla ' . ($obj->wasRecentlyCreated ? 'oluşturuldu' : 'güncellendi'));
    }

    public function deleteBack($obj)
    {
        try {
            if (!$obj->current_id) {
                return response()->json(['success' => false, 'error' => 'Cari bilgisi bulunamadığı için balance güncellenemedi!']);
            }

            $balanceExists = DB::table('balances')->where('current_id', $obj->current_id)->exists();

            if ($balanceExists) {
                DB::statement(
                    "UPDATE balances SET debit_balance = debit_balance + ?, updated_at = ? WHERE current_id = ?",
                    [floatval($obj->amount), now(), $obj->current_id]
                );
            }

            return response()->json(['success' => true, 'message' => 'Cari hesap fişi başarıyla silindi']);
        } catch (\Exception $e) {
            Log::error("Cari hesap fişi silinirken hata: " . $e->getMessage());
            return response()->json(['success' => false, 'error' => 'Cari hesap fişi silinirken bir hata oluştu: ' . $e->getMessage()]);
        }
    }

    public function delete(Request $request)
    {
        try {
            $voucher = AccountVoucher::find($request->id);
            if (!$voucher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cari hesap fişi bulunamadı.'
                ]);
            }
            $voucher->delete();
            return $this->deleteBack($voucher);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cari hesap fişi silinirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    private function exportAccountVouchersToExcel()
    {
        $vouchers = AccountVoucher::with(['current', 'voucherType'])->get();
        $data = [];
        $headers = ['Fiş No', 'Cari Hesap', 'Tarih', 'Fiş Tipi', 'Tutar', 'Para Birimi', 'Açıklama', 'Durum'];

        foreach ($vouchers as $voucher) {
            $data[] = [
                $voucher->voucher_no,
                $voucher->current ? $voucher->current->name : '',
                $voucher->voucher_date ? $voucher->voucher_date->format('d.m.Y') : '',
                $voucher->voucher_type_name,
                number_format($voucher->amount, 2, ',', '.'),
                $voucher->currency === 'TRY' ? '₺' : $voucher->currency,
                $voucher->description,
                $voucher->is_active ? 'Aktif' : 'Pasif'
            ];
        }

        return ExcelExport::exportToExcel($data, $headers, 'Cari_Hesap_Fisleri', 'Cari Hesap Fişleri');
    }
}
