<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Http\Requests\Backend\BalanceRequest;
use App\Models\Balance;
use App\Models\Current;
use App\Models\Role;
use Illuminate\Http\Request;

class BalanceController extends BaseController
{
    use BasePattern;
    public function __construct()
    {
        $this->title = 'Bakiyeler';
        $this->page = 'balance';
        $this->model = new Balance();
        $this->relation = ['current'];
        $this->view = (object)[
            'breadcrumb' => [
                'Bakiyeler' => route('backend.balance_list'),
            ],
        ];

        view()->share('currents', Current::get());
        parent::__construct();
    }

    public function datatableHook($obj)
    {
        return $obj
            ->addColumn('balance_status', function ($item) {
                $debit = (float)$item->debit_balance;
                $credit = (float)$item->credit_balance;
                $total = $debit - $credit;
                return $total;
            });
    }
}
