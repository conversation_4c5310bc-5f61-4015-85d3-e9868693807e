<?php

namespace App\Http\Controllers\Backend;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Unit;
use App\Http\Requests\Backend\BrandRequest;
use Illuminate\Http\Request;
use App\Models\Role;

class BrandController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Marka';
        $this->page = 'brand';
        $this->model = new Brand();
        
        $this->listQuery = Brand::withCount('products')->with('categories')->select('brands.*');
        
        $this->view = (object)array(
            'breadcrumb' => array(
                'Marka Listesi' => route('backend.brand_list'),
            ),
        );

        view()->share('products', Product::active()->get());
        view()->share('units', Unit::active()->get());
        view()->share('roles', Role::get());
        parent::__construct();
    }


    protected function datatableHook($obj)
    {
        return $obj
            ->editColumn('products_count', function ($item) {
                return $item->products_count ?? 0;
            })
            ->editColumn('categories', function ($item) {
                $categories = $item->categories->pluck('name')->toArray();
                return !empty($categories) ? implode(', ', $categories) : '-';
            });
    }

    protected function formHook($item)
    {
        // Many-to-many ilişki için category ID'lerini al
        if ($item->exists) {
            $item->categories = $item->categories->pluck('id')->toArray();
        } else {
            $item->categories = [];
        }
        return $item;
    }
    
    /**
     * Form sayfasını görüntüle
     */
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::with('categories')->find((int)$unique);
            if (method_exists($this, 'formHook')) {
                $item = $this->formHook($item);
            }

            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
        } else {
            $item = new $this->model;
            $item->categories = [];
        }
        
        // Kategorileri al ve grupla
        $mainCategories = Category::with('childrenRecursive')
            ->whereNull('parent_id')
            ->where('is_active', 1)
            ->orderBy('name')
            ->get();
            
        // Role blade gibi gruplu yapı oluştur
        $categoryGroups = [];
        foreach ($mainCategories as $mainCategory) {
            $subCategories = [];
            $this->collectSubCategories($mainCategory, $subCategories);
            $categoryGroups[$mainCategory->name] = $subCategories;
        }

        return view("backend.$this->page.form", compact('item', 'categoryGroups'));
    }
    
    /**
     * Alt kategorileri topla
     */
    private function collectSubCategories($category, &$result, $depth = 0)
    {
        // Ana kategoriyi de ekle
        $result[] = [
            'id' => $category->id,
            'name' => $category->name,
            'parent_id' => $category->parent_id,
            'depth' => $depth,
            'prefix' => str_repeat('—', $depth)
        ];
        
        // Alt kategorileri ekle
        if ($category->childrenRecursive) {
            foreach ($category->childrenRecursive as $child) {
                $this->collectSubCategories($child, $result, $depth + 1);
            }
        }
    }

    public function save(Request $request, $unique = null)
    {
        // Manual validation BrandRequest kurallarını kullanarak
        $brandRequest = new BrandRequest();
        $brandRequest->setContainer($this->container ?? app());
        $brandRequest->setRedirector(app('redirect'));
        
        $validator = \Validator::make($request->all(), $brandRequest->rules(), $brandRequest->messages());
        
        if ($validator->fails()) {
            return $request->ajax()
                ? response()->json(['success' => false, 'errors' => $validator->errors()])
                : redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            $params = $request->except(['categories']);
            $categoryIds = [];
            
            if ($request->has('categories') && is_array($request->categories)) {
                $categoryIds = array_values(array_filter($request->categories, function($value) {
                    return !empty($value);
                }));
            }

            if (!is_null($unique)) {
                $brand = Brand::find($unique);
                if (!$brand) {
                    return $request->ajax()
                        ? response()->json(['success' => false, 'message' => 'Güncellenecek marka bulunamadı.'])
                        : redirect()->back()->with('error', 'Güncellenecek marka bulunamadı.');
                }
                $brand->update($params);
                
                // Kategorileri sync et
                $brand->categories()->sync($categoryIds);
            } else {
                $brand = Brand::create($params);
                
                // Kategorileri attach et
                if (!empty($categoryIds)) {
                    $brand->categories()->attach($categoryIds);
                }
            }

            \Illuminate\Support\Facades\Cache::flush();
            
            if (method_exists($this, 'saveBack')) {
                return $this->saveBack($brand);
            }

            return $request->ajax()
                ? response()->json([
                    'success' => true,
                    'message' => 'Marka başarılı şekilde kaydedildi',
                    'redirect' => route("backend." . $this->page . "_list")
                ])
                : redirect()->route("backend." . $this->page . "_list")->with('success', 'Marka başarılı şekilde kaydedildi');

        } catch (\Exception $e) {
            return $request->ajax()
                ? response()->json([
                    'success' => false,
                    'message' => 'Marka kaydedilirken bir hata oluştu: ' . $e->getMessage()
                ])
                : redirect()->back()->with('error', 'Marka kaydedilirken bir hata oluştu: ' . $e->getMessage())->withInput();
        }
    }

    // Detay sayfası
    public function detail(Request $request, $unique = NULL)
    {
        $item = $this->model::with('categories')->find($unique);
        
        if (is_null($item)) {
            return redirect()->back()->with('error', 'Kayıt bulunamadı');
        }
        
        // Bu markaya ait ürünler için DataTable
        if ($request->has('datatable')) {
            $select = Product::where('brand_id', $unique)
                ->with(['category', 'unit'])
                ->get();
            
            $obj = datatables()->of($select)
                ->editColumn('sku', function ($item) {
                    return $item->sku ?? '-';
                })
                ->editColumn('category', function ($item) {
                    return $item->category->name ?? '-';
                })
                ->editColumn('unit', function ($item) {
                    return $item->unit->name ?? '-';
                })
                ->editColumn('sale_price', function ($item) {
                    return number_format($item->sale_price, 2, ',', '.') . ' ' . ($item->sale_currency_code ?? 'TL');
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active == 1 ? 'Aktif' : 'Pasif';
                })
                ->editColumn('created_at', function ($item) {
                    return !is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-';
                })
                ->addIndexColumn()
                ->make(true);
                
            return $obj;
        }
        
        return view("backend.$this->page.detail", compact('item', 'unique'));
    }


}
