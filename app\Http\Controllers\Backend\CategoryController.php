<?php

namespace App\Http\Controllers\Backend;

use App\Models\Category;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Unit;
use App\Http\Requests\Backend\CategoryRequest;
use Illuminate\Http\Request;
use App\Models\Role;


class CategoryController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Kategori';
        $this->page = 'category';
        $this->model = new Category();
        
        // İlişkiler - parent ve children ilişkilerini yükle
        $this->relation = ['parent', 'children'];
        
        // Category listesi için doğru query - Category modeli için
        $this->listQuery = Category::withCount([
                'children', 
                'products'
                // allSubcategoryProducts kaldırıldı - custom hesaplama yapacağız
            ])
            ->with('parent', 'children')
            ->select('categories.*'); // categories tablosundan seç
        
        $this->view = (object)array(
            'breadcrumb' => array(
                'Kategori Listesi' => route('backend.category_list'),
            ),
        );

        // Üst kategori seçimi için kategorileri view'a paylaş
        view()->share('categories', Category::active()->get());
        view()->share('products', Product::active()->get());
        view()->share('brands', Brand::active()->get());
        view()->share('units', Unit::active()->get());
        view()->share('roles', Role::get());
        parent::__construct();
    }


    // DataTable için özel sütun formatlaması
    protected function datatableHook($obj)
    {
        return $obj
            ->editColumn('products_count', function ($item) {
                $directProducts = $item->products_count ?? 0;
                $allProducts = $item->getAllSubcategoryProductsCount();
                if ($directProducts == $allProducts) {
                    return $directProducts;
                } else {
                    return $directProducts . ' (' . $allProducts . ' toplam)';
                }
            });
    }

    // Form metodu - BaseController'daki form metodunu override ediyoruz
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);
            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
        } else {
            $item = new $this->model;
        }
        
        // Form için mevcut kategoriler (kendisi hariç)
        $categories = Category::active()->with('childrenRecursive')->whereNull('parent_id')->get();
        
        // Eğer düzenleme yapılıyorsa, kendisi ve alt kategorilerini listeden çıkar
        $excludeIds = [];
        if (!is_null($item->id) && $item->exists) {
            $excludeIds = $this->getAllDescendantIds($item);
            $excludeIds[] = $item->id;
        }
        
        // Hiyerarşik kategori listesi oluştur
        $categoryOptions = $this->buildCategoryOptions($categories, $excludeIds);
        
        return view("backend.$this->page.form", compact('item', 'categoryOptions'));
    }
    
    // Bir kategorinin tüm alt kategorilerinin ID'lerini al
    private function getAllDescendantIds($category)
    {
        $ids = [];
        $category->load('childrenRecursive');
        
        foreach ($category->childrenRecursive as $child) {
            $ids[] = $child->id;
            $ids = array_merge($ids, $this->getAllDescendantIds($child));
        }
        
        return $ids;
    }
    
    // Hiyerarşik kategori dropdown oluştur
    private function buildCategoryOptions($categories, $excludeIds = [], $prefix = '', $depth = 0)
    {
        $options = [];
        
        foreach ($categories as $category) {
            if (!in_array($category->id, $excludeIds)) {
                $options[] = [
                    'id' => $category->id,
                    'name' => $prefix . $category->name,
                    'depth' => $depth
                ];
                
                if ($category->childrenRecursive->count() > 0) {
                    $childOptions = $this->buildCategoryOptions(
                        $category->childrenRecursive, 
                        $excludeIds, 
                        $prefix . '— ', 
                        $depth + 1
                    );
                    $options = array_merge($options, $childOptions);
                }
            }
        }
        
        return $options;
    }

    public function save(Request $request, $unique = null)
    {
        // Manual validation CategoryRequest kurallarını kullanarak
        $categoryRequest = new CategoryRequest();
        $categoryRequest->setContainer($this->container ?? app());
        $categoryRequest->setRedirector(app('redirect'));
        
        $validator = \Validator::make($request->all(), $categoryRequest->rules(), $categoryRequest->messages());
        
        if ($validator->fails()) {
            return $request->ajax()
                ? response()->json(['success' => false, 'errors' => $validator->errors()])
                : redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            $params = $request->all();
            
            // parent_id null ise veritabanında null olarak sakla
            if (empty($params['parent_id'])) {
                $params['parent_id'] = null;
            }

            if (!is_null($unique)) {
                $category = Category::find($unique);
                if (!$category) {
                    return $request->ajax()
                        ? response()->json(['success' => false, 'message' => 'Güncellenecek kategori bulunamadı.'])
                        : redirect()->back()->with('error', 'Güncellenecek kategori bulunamadı.');
                }
                $category->update($params);
            } else {
                $category = Category::create($params);
            }

            \Illuminate\Support\Facades\Cache::flush();
            
            if (method_exists($this, 'saveBack')) {
                return $this->saveBack($category);
            }

            return $request->ajax()
                ? response()->json([
                    'success' => true,
                    'message' => 'Kategori başarılı şekilde kaydedildi',
                    'redirect' => route("backend." . $this->page . "_list")
                ])
                : redirect()->route("backend." . $this->page . "_list")->with('success', 'Kategori başarılı şekilde kaydedildi');

        } catch (\Exception $e) {
            return $request->ajax()
                ? response()->json([
                    'success' => false,
                    'message' => 'Kategori kaydedilirken bir hata oluştu: ' . $e->getMessage()
                ])
                : redirect()->back()->with('error', 'Kategori kaydedilirken bir hata oluştu: ' . $e->getMessage())->withInput();
        }
    }

    // Detay sayfası
    public function detail(Request $request, $unique = NULL)
    {
        $item = $this->model::find($unique);
        
        if (is_null($item)) {
            return redirect()->back()->with('error', 'Kayıt bulunamadı');
        }
        
        // Alt kategoriler için DataTable
        if ($request->has('datatable') && $request->datatable == 'subcategories') {
            $select = Category::where('parent_id', $unique)
                ->withCount(['products', 'children'])
                ->get();
            
            $obj = datatables()->of($select)
                ->editColumn('products_count', function ($item) {
                    return $item->products_count ?? 0;
                })
                ->editColumn('children_count', function ($item) {
                    return $item->children_count ?? 0;
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active ? 'Aktif' : 'Pasif';
                })
                ->editColumn('created_at', function ($item) {
                    return !is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-';
                })
                ->addIndexColumn()
                ->make(true);
                
            return $obj;
        }
        
        // Bu kategorideki ürünler için DataTable
        if ($request->has('datatable') && $request->datatable == 'products') {
            // Bu kategoriye ve tüm alt kategorilerine ait ürünleri getir
            $categoryIds = $item->getAllSubcategoryIds();
            
            $select = Product::whereIn('category_id', $categoryIds)
                ->with(['brand', 'unit', 'category'])
                ->get();
            
            $obj = datatables()->of($select)
                ->editColumn('sku', function ($item) {
                    return $item->sku ?? '-';
                })
                ->editColumn('category', function ($item) {
                    return $item->category->name ?? '-';
                })
                ->editColumn('brand', function ($item) {
                    return $item->brand->name ?? '-';
                })
                ->editColumn('unit', function ($item) {
                    return $item->unit->name ?? '-';
                })
                ->editColumn('sale_price', function ($item) {
                    return number_format($item->sale_price, 2, ',', '.') . ' ' . ($item->sale_currency_code ?? 'TL');
                })
                ->editColumn('is_active', function ($item) {
                    return $item->is_active ? 'Aktif' : 'Pasif';
                })
                ->editColumn('created_at', function ($item) {
                    return !is_null($item->created_at) ? $item->created_at->format('d.m.Y H:i') : '-';
                })
                ->addIndexColumn()
                ->make(true);
                
            return $obj;
        }
        
        return view("backend.$this->page.detail", compact('item', 'unique'));
    }
}
