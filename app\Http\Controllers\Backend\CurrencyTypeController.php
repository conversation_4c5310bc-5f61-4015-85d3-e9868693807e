<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\CurrencyType;
use App\Models\ExchangeRate;
use App\Services\ExchangeRateService;

class CurrencyTypeController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Döviz Türleri';
        $this->page = 'currency_type';
        $this->model = new CurrencyType();

        $this->view = (object) [
            'breadcrumb' => [
                'Döviz Türleri' => route('backend.currency_type_list'),
            ],
        ];

        view()->share('currency_types', CurrencyType::get());
        parent::__construct();
    }

    public function saveHook(Request $request)
    {
        $params = $request->all();
        
        if (isset($params['code'])) {
            $params['code'] = strtoupper($params['code']);
        }
        
        if (isset($params['symbol'])) {
            $params['symbol'] = strtoupper($params['symbol']);
        }
        if (isset($params['name'])) {
            $params['name'] = strtoupper($params['name']);
        }
        
        return $params;
    }

    /**
     * List metodunu override et - veri yoksa otomatik çek
     */
    public function list(Request $request)
    {
        // Eğer currency type tablosunda veri yoksa exchange rates'ten çek
        if (CurrencyType::count() == 0) {
            $this->syncFromExchangeRates();
        }

        return parent::list($request);
    }

    /**
     * Exchange rates'ten veri çekme fonksiyonu
     */
    public function syncFromExchangeRates()
    {
        try {
            // Exchange rates tablosundan unique currency verileri al
            $exchangeRates = ExchangeRate::select('code', 'name', 'symbol')
                ->groupBy('code', 'name', 'symbol')
                ->get();

            foreach ($exchangeRates as $rate) {
                // addToCurrencyTypes fonksiyonunun mantığını kullan
                $existsCurrency = CurrencyType::where('code', $rate->code)->first();
                
                if (!$existsCurrency) {
                    CurrencyType::create([
                        'name' => $rate->name,
                        'symbol' => $rate->symbol,
                        'code' => $rate->code,
                        'is_active' => 1,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

}
