<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Http\Requests\Backend\CurrentRequest;
use Illuminate\Http\Request;
use App\Models\City;
use App\Models\Country;
use App\Models\CurrentType;
use App\Models\Current;
use App\Models\District;
use App\Models\InsutationType;
use App\Models\Neighborhood;
use App\Models\TaxOffice;
use Illuminate\Support\Facades\Log;

class CurrentController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Hesap';
        $this->page = 'current';
        $this->model = new Current();
        $this->relation = ['instutaion', 'currentType'];

        $this->view = (object)[
            'breadcrumb' => [
                'Cari Hesaplar' => route('backend.current_list'),
            ],
        ];

        view()->share('cities', City::get());
        view()->share('countrys', Country::get());
        view()->share('districts', District::get());
        view()->share('insutations', InsutationType::get());
        view()->share('taxoffices', TaxOffice::orderBy('code')->get());
        view()->share('currenttypes', CurrentType::get());

        parent::__construct();
    }

    public function saveHook(Request $request)
    {
        if ($request instanceof CurrentRequest) {
            return $request->validated();
        }
        return $request->all();
    }

    public function saveBack($obj)
    {
        return redirect()
            ->route('backend.current_list')
            ->with('success', 'Cari hesap başarıyla ' . ($obj->wasRecentlyCreated ? 'oluşturuldu' : 'güncellendi'));
    }

    public function deleteBack($obj)
    {
        try {
            $obj->delete();
            return response()->json([
                'success' => true,
                'message' => 'Cari hesap başarıyla silindi'
            ]);
        } catch (\Exception $e) {
            Log::error("Cari hesap silinirken hata: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Cari hesap silinirken bir hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

    public function delete(Request $request)
    {
        try {
            $current = Current::find($request->id);
            if (!$current) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cari hesap bulunamadı'
                ], 404);
            }
            return $this->deleteBack($current);
        } catch (\Exception $e) {
            Log::error("Cari hesap silinirken hata: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Cari hesap silinirken bir hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }
}
