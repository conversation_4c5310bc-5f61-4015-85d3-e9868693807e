<?php

namespace App\Http\Controllers\Backend;

use App\Models\AccountVoucher;
use App\Models\Current;
use App\Models\Invoice;
use App\Models\PurchaseInvoice;
use App\Models\VoucherType;
use App\Helpers\ExcelExport;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CurrentTransactionController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Cari Finansal Hareketler';
        $this->page = 'current_transactions';

        $this->view = (object)[
            'breadcrumb' => [
                'Finans' => '#',
                'Cari Finansal Hareketler' => route('backend.current_transactions_list'),
            ],
        ];

        view()->share('currents', Current::where('is_active', 1)->get());
        view()->share('voucherTypes', VoucherType::where('is_active', 1)->get());

        parent::__construct();
    }

    public function stockMovementsList(Request $request)
    {
        if ($request->has('export') && $request->export == 'excel') {
            return $this->exportCurrentStockMovementsToExcel();
        }

        if ($request->datatable) {
            return $this->getStockMovementsDataTable($request);
        }

        view()->share('products', Product::where('is_active', 1)->orderBy('name')->get());

        return view('backend.current_transactions.stock_movements_list', [
            'container' => (object)[
                'title' => 'Cari Stok Hareketleri',
                'page'  => 'current_stock_movements',
            ],
        ]);
    }

    private function getStockMovementsDataTable(Request $request)
    {
        try {
            // Filtre parametrelerini al
            $currentId = $request->current_id;
            $productId = $request->product_id;
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            $transactionType = $request->transaction_type;

            // Satış faturaları sorgusunu oluştur
            $salesQuery = Invoice::with(['current'])
                ->where('is_active', 1);

            // Alış faturaları sorgusunu oluştur
            $purchaseQuery = PurchaseInvoice::with(['current'])
                ->where('is_active', 1);

            // Cari filtresi
            if ($currentId) {
                $salesQuery->where('current_id', $currentId);
                $purchaseQuery->where('current_id', $currentId);
            }

            // Tarih filtresi
            if ($startDate) {
                $salesQuery->whereDate('invoice_date', '>=', $startDate);
                $purchaseQuery->whereDate('invoice_date', '>=', $startDate);
            }

            if ($endDate) {
                $salesQuery->whereDate('invoice_date', '<=', $endDate);
                $purchaseQuery->whereDate('invoice_date', '<=', $endDate);
            }

            // İşlem tipi filtresi
            if ($transactionType) {
                if ($transactionType === 'sales') {
                    $purchaseQuery->whereRaw('1=0'); // Boş sonuç döndür
                } else if ($transactionType === 'purchase') {
                    $salesQuery->whereRaw('1=0'); // Boş sonuç döndür
                }
            }

            // Faturaları getir
            $salesInvoices = $salesQuery->get();
            $purchaseInvoices = $purchaseQuery->get();

            // Ürün bilgilerini getir
            $productIds = [];

            // Satış faturalarından ürün ID'lerini topla
            foreach ($salesInvoices as $invoice) {
                if (isset($invoice->items) && is_array($invoice->items)) {
                    foreach ($invoice->items as $item) {
                        if (isset($item['product_id']) && !empty($item['product_id'])) {
                            $productIds[] = $item['product_id'];
                        }
                    }
                }
            }

            // Alış faturalarından ürün ID'lerini topla
            foreach ($purchaseInvoices as $invoice) {
                if (isset($invoice->items) && is_array($invoice->items)) {
                    foreach ($invoice->items as $item) {
                        if (isset($item['product_id']) && !empty($item['product_id'])) {
                            $productIds[] = $item['product_id'];
                        }
                    }
                }
            }
            $productIds = array_unique($productIds);
            $products = Product::whereIn('id', $productIds)->get()->keyBy('id');

            $salesItems = $salesInvoices->flatMap(function ($invoice) use ($productId, $products) {
                return collect($invoice->items)
                    ->filter(function ($item) use ($productId) {
                        if ($productId) {
                            return ($item['product_id'] ?? null) == $productId;
                        }
                        return !empty($item['product_id']);
                    })
                    ->map(function ($item) use ($invoice, $products) {
                        $productId = $item['product_id'] ?? null;
                        $product = $products->get($productId);

                        return [
                            'invoice_id' => $invoice->id,
                            'invoice_no' => $invoice->invoice_no,
                            'invoice_date' => $invoice->invoice_date->format('d.m.Y'),
                            'current_id' => $invoice->current_id,
                            'current_name' => $invoice->current->name ?? 'Bilinmeyen Cari',
                            'product_id' => $productId,
                            'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                            'quantity' => $item['quantity'] ?? 0,
                            'unit_price' => $item['sale_price'] ?? 0,
                            'total' => $item['total'] ?? 0,
                            'transaction_type' => 'sales',
                            'transaction_type_name' => 'Satış'
                        ];
                    });
            })->values();

            // Alış faturaları üzerinden ürünleri çıkar
            $purchaseItems = $purchaseInvoices->flatMap(function ($invoice) use ($productId, $products) {
                return collect($invoice->items)
                    ->filter(function ($item) use ($productId) {
                        // Ürün filtresi
                        if ($productId) {
                            return ($item['product_id'] ?? null) == $productId;
                        }
                        return !empty($item['product_id']);
                    })
                    ->map(function ($item) use ($invoice, $products) {
                        $productId = $item['product_id'] ?? null;
                        $product = $products->get($productId);

                        return [
                            'invoice_id' => $invoice->id,
                            'invoice_no' => $invoice->invoice_no,
                            'invoice_date' => $invoice->invoice_date->format('d.m.Y'),
                            'current_id' => $invoice->current_id,
                            'current_name' => $invoice->current->name ?? 'Bilinmeyen Cari',
                            'product_id' => $productId,
                            'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                            'quantity' => $item['quantity'] ?? 0,
                            'unit_price' => $item['sale_price'] ?? 0,
                            'total' => $item['total'] ?? 0,
                            'transaction_type' => 'purchase',
                            'transaction_type_name' => 'Alış'
                        ];
                    });
            })->values();

            // Tüm hareketleri birleştir
            $allItems = $salesItems->concat($purchaseItems);

            // Tarihe göre sırala (en yeni en üstte)
            $allItems = $allItems->sortByDesc('invoice_date')->values();

            return datatables()->of($allItems)->make(true);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Stok hareketleri getirilirken bir hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

    private function exportCurrentStockMovementsToExcel()
    {
        // Tüm cari hesaplar için stok hareketlerini getir
        $salesInvoices = Invoice::with(['current'])
            ->where('is_active', 1)
            ->get();

        $purchaseInvoices = PurchaseInvoice::with(['current'])
            ->where('is_active', 1)
            ->get();

        // Ürün ID'lerini topla
        $productIds = [];

        // Satış faturalarından ürün ID'lerini topla
        foreach ($salesInvoices as $invoice) {
            if (isset($invoice->items) && is_array($invoice->items)) {
                foreach ($invoice->items as $item) {
                    if (isset($item['product_id']) && !empty($item['product_id'])) {
                        $productIds[] = $item['product_id'];
                    }
                }
            }
        }

        // Alış faturalarından ürün ID'lerini topla
        foreach ($purchaseInvoices as $invoice) {
            if (isset($invoice->items) && is_array($invoice->items)) {
                foreach ($invoice->items as $item) {
                    if (isset($item['product_id']) && !empty($item['product_id'])) {
                        $productIds[] = $item['product_id'];
                    }
                }
            }
        }

        // Tekrarlanan ID'leri kaldır
        $productIds = array_unique($productIds);

        // Ürün bilgilerini getir
        $products = Product::whereIn('id', $productIds)->get()->keyBy('id');

        // Satış faturaları üzerinden ürünleri çıkar
        $salesItems = $salesInvoices->flatMap(function ($invoice) use ($products) {
            return collect($invoice->items)
                ->filter(function ($item) {
                    return !empty($item['product_id']);
                })
                ->map(function ($item) use ($invoice, $products) {
                    $productId = $item['product_id'] ?? null;
                    $product = $products->get($productId);

                    return [
                        'invoice_id' => $invoice->id,
                        'invoice_no' => $invoice->invoice_no,
                        'invoice_date' => $invoice->invoice_date->format('d.m.Y'),
                        'current_id' => $invoice->current_id,
                        'current_name' => $invoice->current->name ?? 'Bilinmeyen Cari',
                        'product_id' => $productId,
                        'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                        'quantity' => $item['quantity'] ?? 0,
                        'unit_price' => $item['sale_price'] ?? 0,
                        'total' => $item['total'] ?? 0,
                        'type' => 'sales'
                    ];
                });
        })->values();

        // Alış faturaları üzerinden ürünleri çıkar
        $purchaseItems = $purchaseInvoices->flatMap(function ($invoice) use ($products) {
            return collect($invoice->items)
                ->filter(function ($item) {
                    return !empty($item['product_id']);
                })
                ->map(function ($item) use ($invoice, $products) {
                    $productId = $item['product_id'] ?? null;
                    $product = $products->get($productId);

                    return [
                        'invoice_id' => $invoice->id,
                        'invoice_no' => $invoice->invoice_no,
                        'invoice_date' => $invoice->invoice_date->format('d.m.Y'),
                        'current_id' => $invoice->current_id,
                        'current_name' => $invoice->current->name ?? 'Bilinmeyen Cari',
                        'product_id' => $productId,
                        'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                        'quantity' => $item['quantity'] ?? 0,
                        'unit_price' => $item['sale_price'] ?? 0,
                        'total' => $item['total'] ?? 0,
                        'type' => 'purchase'
                    ];
                });
        })->values();

        // Ürün ve cari bazında özet
        $productSummary = [];

        // Satış özeti
        foreach ($salesItems as $item) {
            $productId = $item['product_id'];
            $currentId = $item['current_id'];
            $key = $productId . '-' . $currentId;

            if (!isset($productSummary[$key])) {
                $productSummary[$key] = [
                    'product_id' => $productId,
                    'product_name' => $item['product_name'],
                    'current_id' => $currentId,
                    'current_name' => $item['current_name'],
                    'sales_quantity' => 0,
                    'sales_total' => 0,
                    'purchase_quantity' => 0,
                    'purchase_total' => 0
                ];
            }

            $productSummary[$key]['sales_quantity'] += $item['quantity'];
            $productSummary[$key]['sales_total'] += $item['total'];
        }

        // Alış özeti
        foreach ($purchaseItems as $item) {
            $productId = $item['product_id'];
            $currentId = $item['current_id'];
            $key = $productId . '-' . $currentId;

            if (!isset($productSummary[$key])) {
                $productSummary[$key] = [
                    'product_id' => $productId,
                    'product_name' => $item['product_name'],
                    'current_id' => $currentId,
                    'current_name' => $item['current_name'],
                    'sales_quantity' => 0,
                    'sales_total' => 0,
                    'purchase_quantity' => 0,
                    'purchase_total' => 0
                ];
            }

            $productSummary[$key]['purchase_quantity'] += $item['quantity'];
            $productSummary[$key]['purchase_total'] += $item['total'];
        }

        // Excel için veri hazırla
        $data = [];
        $headers = ['Cari Hesap', 'Ürün', 'Satış Miktarı', 'Satış Tutarı', 'Alış Miktarı', 'Alış Tutarı', 'Miktar Farkı', 'Tutar Farkı'];

        foreach ($productSummary as $item) {
            $quantityDiff = $item['sales_quantity'] - $item['purchase_quantity'];
            $totalDiff = $item['sales_total'] - $item['purchase_total'];

            $data[] = [
                'current_name' => $item['current_name'],
                'product_name' => $item['product_name'],
                'sales_quantity' => number_format($item['sales_quantity'], 2, ',', '.'),
                'sales_total' => number_format($item['sales_total'], 2, ',', '.') . ' ₺ ',
                'purchase_quantity' => number_format($item['purchase_quantity'], 2, ',', '.'),
                'purchase_total' => number_format($item['purchase_total'], 2, ',', '.') . ' ₺ ',
                'quantity_diff' => number_format($quantityDiff, 2, ',', '.'),
                'total_diff' => number_format($totalDiff, 2, ',', '.') . ' ₺ '
            ];
        }

        // Excel dosyasını oluştur ve indir
        return ExcelExport::exportToExcel(
            array_map(function ($item) {
                return [
                    $item['current_name'],
                    $item['product_name'],
                    $item['sales_quantity'],
                    $item['sales_total'],
                    $item['purchase_quantity'],
                    $item['purchase_total'],
                    $item['quantity_diff'],
                    $item['total_diff']
                ];
            }, $data),
            $headers,
            'Cari_Stok_Hareketleri',
            'Cari Stok Hareketleri'
        );
    }

    public function currentStockRelationsList()
    {
        return view('backend.current_transactions.stock_relations', [
            'container' => (object)[
                'title' => 'Cari-Stok İlişkileri',
                'page'  => 'current_stock_relations',
            ],
        ]);
    }

    public function getCurrentStockRelations($currentId, Request $request)
    {
        try {
            $productId = $request->product_id;
            $startDate = $request->start_date;
            $endDate = $request->end_date;

            // Cari hesabı kontrol et (0 ise tüm cariler)
            $current = null;
            if ($currentId && $currentId != '0') {
                $current = Current::findOrFail($currentId);
            }

            // Satış faturaları sorgusunu oluştur
            $salesQuery = Invoice::with(['current'])
                ->where('is_active', 1);

            // Alış faturaları sorgusunu oluştur
            $purchaseQuery = PurchaseInvoice::with(['current'])
                ->where('is_active', 1);

            // Cari filtresi
            if ($current) {
                $salesQuery->where('current_id', $currentId);
                $purchaseQuery->where('current_id', $currentId);
            }

            // Tarih filtresi
            if ($startDate) {
                $salesQuery->whereDate('invoice_date', '>=', $startDate);
                $purchaseQuery->whereDate('invoice_date', '>=', $startDate);
            }

            if ($endDate) {
                $salesQuery->whereDate('invoice_date', '<=', $endDate);
                $purchaseQuery->whereDate('invoice_date', '<=', $endDate);
            }

            // Faturaları getir
            $salesInvoices = $salesQuery->get();
            $purchaseInvoices = $purchaseQuery->get();

            // Ürün ID'lerini topla
            $productIds = [];

            // Satış faturalarından ürün ID'lerini topla
            foreach ($salesInvoices as $invoice) {
                if (isset($invoice->items) && is_array($invoice->items)) {
                    foreach ($invoice->items as $item) {
                        if (isset($item['product_id']) && !empty($item['product_id'])) {
                            $productIds[] = $item['product_id'];
                        }
                    }
                }
            }

            // Alış faturalarından ürün ID'lerini topla
            foreach ($purchaseInvoices as $invoice) {
                if (isset($invoice->items) && is_array($invoice->items)) {
                    foreach ($invoice->items as $item) {
                        if (isset($item['product_id']) && !empty($item['product_id'])) {
                            $productIds[] = $item['product_id'];
                        }
                    }
                }
            }

            // Tekrarlanan ID'leri kaldır
            $productIds = array_unique($productIds);

            // Ürün bilgilerini getir
            $products = Product::whereIn('id', $productIds)->get()->keyBy('id');
            $products = Product::whereIn('id', $productIds)->get()->keyBy('id');

            // Satış faturaları üzerinden ürünleri çıkar
            $salesItems = $salesInvoices->flatMap(function ($invoice) use ($productId, $products) {
                return collect($invoice->items)
                    ->filter(function ($item) use ($productId) {
                        // Ürün filtresi
                        if ($productId) {
                            return ($item['product_id'] ?? null) == $productId;
                        }
                        return !empty($item['product_id']);
                    })
                    ->map(function ($item) use ($invoice, $products) {
                        $productId = $item['product_id'] ?? null;
                        $product = $products->get($productId);

                        return [
                            'invoice_id' => $invoice->id,
                            'invoice_no' => $invoice->invoice_no,
                            'invoice_date' => $invoice->invoice_date->format('d.m.Y'),
                            'current_id' => $invoice->current_id,
                            'current_name' => $invoice->current->name ?? 'Bilinmeyen Cari',
                            'product_id' => $productId,
                            'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                            'quantity' => $item['quantity'] ?? 0,
                            'unit_price' => $item['sale_price'] ?? 0,
                            'total' => $item['total'] ?? 0,
                            'type' => 'sales'
                        ];
                    });
            })->values();

            // Alış faturaları üzerinden ürünleri çıkar
            $purchaseItems = $purchaseInvoices->flatMap(function ($invoice) use ($productId, $products) {
                return collect($invoice->items)
                    ->filter(function ($item) use ($productId) {
                        // Ürün filtresi
                        if ($productId) {
                            return ($item['product_id'] ?? null) == $productId;
                        }
                        return !empty($item['product_id']);
                    })
                    ->map(function ($item) use ($invoice, $products) {
                        $productId = $item['product_id'] ?? null;
                        $product = $products->get($productId);

                        return [
                            'invoice_id' => $invoice->id,
                            'invoice_no' => $invoice->invoice_no,
                            'invoice_date' => $invoice->invoice_date->format('d.m.Y'),
                            'current_id' => $invoice->current_id,
                            'current_name' => $invoice->current->name ?? 'Bilinmeyen Cari',
                            'product_id' => $productId,
                            'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                            'quantity' => $item['quantity'] ?? 0,
                            'unit_price' => $item['sale_price'] ?? 0,
                            'total' => $item['total'] ?? 0,
                            'type' => 'purchase'
                        ];
                    });
            })->values();

            // Ürün ve cari bazında özet
            $productSummary = [];

            // Satış özeti
            foreach ($salesItems as $item) {
                $productId = $item['product_id'];
                $currentId = $item['current_id'];
                $key = $productId . '-' . $currentId;

                if (!isset($productSummary[$key])) {
                    $productSummary[$key] = [
                        'product_id' => $productId,
                        'product_name' => $item['product_name'],
                        'current_id' => $currentId,
                        'current_name' => $item['current_name'],
                        'sales_quantity' => 0,
                        'sales_total' => 0,
                        'purchase_quantity' => 0,
                        'purchase_total' => 0
                    ];
                }

                $productSummary[$key]['sales_quantity'] += $item['quantity'];
                $productSummary[$key]['sales_total'] += $item['total'];
            }

            // Alış özeti
            foreach ($purchaseItems as $item) {
                $productId = $item['product_id'];
                $currentId = $item['current_id'];
                $key = $productId . '-' . $currentId;

                if (!isset($productSummary[$key])) {
                    $productSummary[$key] = [
                        'product_id' => $productId,
                        'product_name' => $item['product_name'],
                        'current_id' => $currentId,
                        'current_name' => $item['current_name'],
                        'sales_quantity' => 0,
                        'sales_total' => 0,
                        'purchase_quantity' => 0,
                        'purchase_total' => 0
                    ];
                }

                $productSummary[$key]['purchase_quantity'] += $item['quantity'];
                $productSummary[$key]['purchase_total'] += $item['total'];
            }

            return response()->json([
                'success' => true,
                'current' => $current ? [
                    'id' => $current->id,
                    'name' => $current->name
                ] : null,
                'sales_items' => $salesItems,
                'purchase_items' => $purchaseItems,
                'product_summary' => array_values($productSummary)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cari-Stok ilişkileri getirilirken bir hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('invoice_date', function ($row) {
                return $row->invoice_date ? Carbon::parse($row->invoice_date)->format('d.m.Y') : '';
            })
            ->addColumn('total_amount_formatted', function ($row) {
                if (!isset($row->total_amount)) {
                    return null;
                }

                $amount = number_format($row->total_amount, 2, ',', '.');
                if (strpos($row->transaction_type, 'account_voucher_') === 0) {
                    return '-' . $amount . ' ₺';
                } else if ($row->transaction_type === 'sales_invoice') {
                    return '+' . $amount . ' ₺';
                } else if ($row->transaction_type === 'purchase_invoice') {
                    return '-' . $amount . ' ₺';
                }
                return $amount . ' ₺';
            })
            ->rawColumns(['details', 'total_amount_formatted', 'status']);
    }

    public function list(Request $request)
    {
        if ($request->has('export') && $request->export == 'excel') {
            return $this->exportCurrentTransactionsToExcel(
                $request->current_id,
                $request->start_date,
                $request->end_date,
                $request->transaction_type
            );
        }

        if ($request->datatable) {
            $salesInvoices = $this->getSalesInvoicesQuery();
            $purchaseInvoices = $this->getPurchaseInvoicesQuery();
            $accountVouchers = $this->getAccountVouchersQuery();

            $this->applyFilters($salesInvoices, $purchaseInvoices, $accountVouchers, $request);

            $union = $salesInvoices->union($purchaseInvoices)->union($accountVouchers);

            $obj = datatables()->of($union);
            if (method_exists($this, 'datatableHook')) {
                $obj = $this->datatableHook($obj);
            }

            return $obj->make(true);
        }

        return view('backend.current_transactions.list');
    }

    private function getSalesInvoicesQuery()
    {
        return Invoice::with(['current'])
            ->select(
                'id',
                'invoice_no',
                'invoice_date',
                'current_id',
                'total_amount',
                'created_at',
                DB::raw("NULL as voucher_type"),
                DB::raw("'sales_invoice' as transaction_type"),
                DB::raw("'Satış Faturası' as transaction_type_name"),
                DB::raw("(SELECT name FROM currents WHERE id = current_id) as current_name"),
                DB::raw("'' as details")
            );
    }

    private function getPurchaseInvoicesQuery()
    {
        return PurchaseInvoice::with(['currents'])
            ->select(
                'id',
                'invoice_no',
                'invoice_date',
                'current_id',
                'total_amount',
                'created_at',
                DB::raw("NULL as voucher_type"),
                DB::raw("'purchase_invoice' as transaction_type"),
                DB::raw("'Alış Faturası' as transaction_type_name"),
                DB::raw("(SELECT name FROM currents WHERE id = current_id) as current_name"),
                DB::raw("'' as details")
            );
    }

    private function getAccountVouchersQuery()
    {
        return AccountVoucher::with(['currents', 'voucherType'])
            ->select(
                'id',
                'voucher_no as invoice_no',
                'voucher_date as invoice_date',
                'current_id',
                'amount as total_amount',
                'created_at',
                'voucher_type',
                DB::raw("CONCAT('account_voucher_', voucher_type) as transaction_type"),
                DB::raw("CASE
                    WHEN voucher_type = 'bank' THEN 'Banka Fişi'
                    WHEN voucher_type = 'check' THEN 'Çek'
                    WHEN voucher_type = 'promissory_note' THEN 'Senet'
                    WHEN voucher_type = 'wire_transfer' THEN 'Havale'
                    WHEN voucher_type = 'eft' THEN 'EFT'
                    ELSE 'Diğer'
                END as transaction_type_name"),
                DB::raw("(SELECT name FROM currents WHERE id = current_id) as current_name"),
                DB::raw("'' as details")
            );
    }

    private function applyFilters($salesInvoices, $purchaseInvoices, $accountVouchers, Request $request)
    {
        if ($request->current_id) {
            $salesInvoices->where('current_id', $request->current_id);
            $purchaseInvoices->where('current_id', $request->current_id);
            $accountVouchers->where('current_id', $request->current_id);
        }

        if ($request->start_date) {
            $salesInvoices->whereDate('invoice_date', '>=', $request->start_date);
            $purchaseInvoices->whereDate('invoice_date', '>=', $request->start_date);
            $accountVouchers->whereDate('voucher_date', '>=', $request->start_date);
        }

        if ($request->end_date) {
            $salesInvoices->whereDate('invoice_date', '<=', $request->end_date);
            $purchaseInvoices->whereDate('invoice_date', '<=', $request->end_date);
            $accountVouchers->whereDate('voucher_date', '<=', $request->end_date);
        }

        if ($request->transaction_type) {
            if ($request->transaction_type === 'sales_invoice') {
                $purchaseInvoices->whereRaw('1=0');
                $accountVouchers->whereRaw('1=0');
            } else if ($request->transaction_type === 'purchase_invoice') {
                $salesInvoices->whereRaw('1=0');
                $accountVouchers->whereRaw('1=0');
            } else if (strpos($request->transaction_type, 'account_voucher_') === 0) {
                $voucherType = str_replace('account_voucher_', '', $request->transaction_type);
                $salesInvoices->whereRaw('1=0');
                $purchaseInvoices->whereRaw('1=0');
                $accountVouchers->where('voucher_type', $voucherType);
            }
        }
    }

    private function exportCurrentTransactionsToExcel($currentId = null, $startDate = null, $endDate = null, $transactionType = null)
    {
        // Satış faturalarını al
        $salesInvoices = Invoice::with(['current'])
            ->select(
                'id',
                'invoice_no',
                'invoice_date',
                'current_id',
                'total_amount',
                'created_at',
                DB::raw("NULL as voucher_type"),
                DB::raw("'sales_invoice' as transaction_type"),
                DB::raw("'Satış Faturası' as transaction_type_name"),
                DB::raw("(SELECT name FROM current WHERE id = current_id) as current_name"),
                DB::raw("'' as details")
            );

        // Alış faturalarını al
        $purchaseInvoices = PurchaseInvoice::with(['current'])
            ->select(
                'id',
                'invoice_no',
                'invoice_date',
                'current_id',
                'total_amount',
                'created_at',
                DB::raw("NULL as voucher_type"),
                DB::raw("'purchase_invoice' as transaction_type"),
                DB::raw("'Alış Faturası' as transaction_type_name"),
                DB::raw("(SELECT name FROM current WHERE id = current_id) as current_name"),
                DB::raw("'' as details")
            );

        // Cari hesap fişlerini al
        $accountVouchers = AccountVoucher::with(['current'])
            ->select(
                'id',
                'voucher_no as invoice_no',
                'voucher_date as invoice_date',
                'current_id',
                'amount as total_amount',
                'created_at',
                'voucher_type',
                DB::raw("CONCAT('account_voucher_', voucher_type) as transaction_type"),
                DB::raw("CASE
                    WHEN voucher_type = 'bank' THEN 'Banka Fişi'
                    WHEN voucher_type = 'check' THEN 'Çek'
                    WHEN voucher_type = 'promissory_note' THEN 'Senet'
                    WHEN voucher_type = 'wire_transfer' THEN 'Havale'
                    WHEN voucher_type = 'eft' THEN 'EFT'
                    ELSE 'Diğer'
                END as transaction_type_name"),
                DB::raw("(SELECT name FROM current WHERE id = current_id) as current_name"),
                DB::raw("'' as details")
            );

        // Filtreler uygula
        if ($currentId) {
            $salesInvoices->where('current_id', $currentId);
            $purchaseInvoices->where('current_id', $currentId);
            $accountVouchers->where('current_id', $currentId);
        }

        if ($startDate) {
            $salesInvoices->whereDate('invoice_date', '>=', $startDate);
            $purchaseInvoices->whereDate('invoice_date', '>=', $startDate);
            $accountVouchers->whereDate('voucher_date', '>=', $startDate);
        }

        if ($endDate) {
            $salesInvoices->whereDate('invoice_date', '<=', $endDate);
            $purchaseInvoices->whereDate('invoice_date', '<=', $endDate);
            $accountVouchers->whereDate('voucher_date', '<=', $endDate);
        }

        if ($transactionType) {
            if ($transactionType === 'sales_invoice') {
                $purchaseInvoices->whereRaw('1=0'); // Boş sonuç döndür
                $accountVouchers->whereRaw('1=0'); // Boş sonuç döndür
            } else if ($transactionType === 'purchase_invoice') {
                $salesInvoices->whereRaw('1=0'); // Boş sonuç döndür
                $accountVouchers->whereRaw('1=0'); // Boş sonuç döndür
            } else if (strpos($transactionType, 'account_voucher_') === 0) {
                $voucherType = str_replace('account_voucher_', '', $transactionType);
                $salesInvoices->whereRaw('1=0'); // Boş sonuç döndür
                $purchaseInvoices->whereRaw('1=0'); // Boş sonuç döndür
                $accountVouchers->where('voucher_type', $voucherType);
            }
        }

        // Tüm sonuçları birleştir
        $union = $salesInvoices->union($purchaseInvoices)->union($accountVouchers)->get();

        // Excel için veri hazırla
        $data = [];
        $headers = ['Belge No', 'Cari Hesap', 'Tarih', 'İşlem Tipi', 'Tutar', 'Durum'];

        foreach ($union as $row) {
            $amount = number_format($row->total_amount, 2, ',', '.');
            $amountFormatted = $amount . ' ₺';

            if (strpos($row->transaction_type, 'account_voucher_') === 0) {
                // Cari hesap fişleri için negatif göster (ödeme olduğu için)
                $amountFormatted = '-' . $amount . ' ₺';
                $status = 'Borç';
            } else if ($row->transaction_type === 'sales_invoice') {
                // Satış faturaları için pozitif göster (alacak olduğu için)
                $amountFormatted = '+' . $amount . ' ₺';
                $status = 'Alacak';
            } else if ($row->transaction_type === 'purchase_invoice') {
                // Alış faturaları için negatif göster (borç olduğu için)
                $amountFormatted = '-' . $amount . ' ₺';
                $status = 'Borç';
            } else {
                $status = '';
            }

            $data[] = [
                'invoice_no' => $row->invoice_no,
                'current_name' => $row->current ? $row->current->name : '',
                'invoice_date' => $row->invoice_date ? Carbon::parse($row->invoice_date)->format('d.m.Y') : '',
                'transaction_type_name' => $row->transaction_type_name,
                'total_amount' => $amountFormatted,
                'status' => $status
            ];
        }

        // Başlık oluştur
        $title = 'Cari Finansal Hareketler';

        // Filtre bilgilerini başlığa ekle
        if ($currentId) {
            $current = Current::find($currentId);
            if ($current) {
                $title .= ' - ' . $current->name;
            }
        }

        if ($startDate && $endDate) {
            $title .= ' (' . Carbon::parse($startDate)->format('d.m.Y') . ' - ' . Carbon::parse($endDate)->format('d.m.Y') . ')';
        } else if ($startDate) {
            $title .= ' (' . Carbon::parse($startDate)->format('d.m.Y') . ' sonrası)';
        } else if ($endDate) {
            $title .= ' (' . Carbon::parse($endDate)->format('d.m.Y') . ' öncesi)';
        }

        // Excel dosyasını oluştur ve indir
        return ExcelExport::exportToExcel(
            array_map(function ($item) {
                return [
                    $item['invoice_no'],
                    $item['current_name'],
                    $item['invoice_date'],
                    $item['transaction_type_name'],
                    $item['total_amount'],
                    $item['status']
                ];
            }, $data),
            $headers,
            'Cari_Finansal_Hareketler',
            $title
        );
    }
}
