<?php
namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\District;
use App\Models\Neighborhood;
use App\Models\TaxOffice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Yajra\DataTables\Facades\DataTables;

class GeneralController extends Controller
{

    public function index(Request $request)
    {

        return view('backend.index');
    }

    public function cacheall()
    {
        Cache::flush();
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('view:clear');
        Artisan::call('route:clear');
        return response()->json(['status' => true]);
    }


    public function getCity($country_id)
    {
        $cities = City::where('country_id', $country_id)->get();
        return response()->json(['cities' => $cities]);
    }
    
    public function getDistrict($city_id)
    {
        $districts = District::where('city_id', $city_id)->get();
        return response()->json(['districts' => $districts]);
    }

    public function getNeighborhood($district_id)
    {
        $neighborhoods = Neighborhood::where('district_id', $district_id)->get();
        return response()->json(['neighborhoods' => $neighborhoods]);
    }
}
