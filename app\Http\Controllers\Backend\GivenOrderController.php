<?php

namespace App\Http\Controllers\Backend;

use App\Http\Requests\Backend\OrderRequest;
use App\Libraries\Helpers;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\OrderType;
use App\Models\PaymentType;
use App\Models\Stock;
use App\Models\Warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class GivenOrderController extends BaseController
{
    use BasePattern;


    public function __construct()
    {
        $this->title = 'Verilen Sipariş';
        $this->page = 'order_given';
        $this->model = new Order();
        $this->relation = [
            'warehouse',
            'exchangeRate',
            'current' => function ($query) {
                $query->withTrashed();
            },
            'paymentType',
            'orderProducts'
        ];
        $this->listQuery = Order::filter(request())->where('order_type_id', 2);
        $this->view = (object)array(
            'breadcrumb' => array(
                'Ayarlar' => '#',
                'Verilen Sipariş' => route('backend.order_given_list'),
            ),
        );

        view()->share('orderProducts', OrderProduct::get());
        // view()->share('branches', Branch::get());
        view()->share('warehouses', Warehouse::get());
        view()->share('exchangeRate', ExchangeRate::where('is_active', 1)->get());
        view()->share('orderType', OrderType::get());
        view()->share('current', Current::with(['country', 'city', 'district'])->where('is_active', 1)->get());
        view()->share('paymentType', PaymentType::get());
        view()->share('orderType', OrderType::get());
        view()->share('stocks', Stock::with(['product', 'variant', 'stockReservations'])->where('is_active', 1)->get());
        parent::__construct();
    }
    public function save(Request $request, $unique = null)
    {
        try {
            $validator = Validator::make($request->all(), (new OrderRequest())->rules(), (new OrderRequest())->messages());
            if ($validator->fails()) {
                return $request->ajax()
                    ? response()->json(['success' => false, 'errors' => $validator->errors()], 422)
                    : redirect()->back()->withErrors($validator)->withInput();
            }
            $params = $request->except(['products']);
            $products = $request->has('products')
                ? (is_string($request->input('products'))
                    ? json_decode($request->input('products'), true) ?? []
                    : $request->input('products'))
                : [];
            if (!empty($products)) {
                $params['total_price'] = Helpers::parseTrNumber($request->input('total_price', 0));
                $params['vat_amount'] = Helpers::parseTrNumber($request->input('vat_amount', 0));
                $params['total_amount'] = Helpers::parseTrNumber($request->input('total_amount', 0));
            }
            if (!is_null($unique)) {
                $order = Order::find($unique);
                $order->update($params);
            } else {
                $order = Order::create($params);
            }
            if ($order && $order->id) {
                OrderProduct::where('order_id', $order->id)->delete();
                if (!empty($products)) {
                    $order->processOrderProducts($products);
                }
            }
            Cache::flush();
            return $request->ajax()
                ? response()->json([
                    'success' => true,
                    'message' => 'Sipariş başarılı şekilde kaydedildi',
                    'redirect' => route("backend." . $this->page . "_list")
                ])
                : redirect()->route("backend." . $this->page . "_list")->with('success', 'Sipariş başarılı şekilde kaydedildi');
        } catch (\Exception $e) {
            return $request->ajax()
                ? response()->json([
                    'success' => false,
                    'message' => 'Sipariş kaydedilirken bir hata oluştu: ' . $e->getMessage()
                ])
                : redirect()->back()->with('error', 'Sipariş kaydedilirken bir hata oluştu: ' . $e->getMessage())->withInput();
        }
    }
    public function detail(Request $request, $unique = null)
    {
        try {
            $order = Order::with([
                // 'branch',
                'warehouse',
                'exchangeRate',
                'current',
                'paymentType',
                'orderType',
                'orderProducts.stock'
            ])->findOrFail($unique ?? $request->input('id'));
            $breadcrumb = [
                'Ayarlar' => '#',
                'Verilen Sipariş' => route('backend.order_given_list'),
                'Verilen Sipariş Detay' => route('backend.order_given_detail', ['unique' => $unique])
            ];
            view()->share('breadcrumb', $breadcrumb);
            return view('backend.order_given.detail', [
                'container' => (object)[
                    'page' => $this->page,
                    'title' => 'Verilen Sipariş'
                ],
                'title' => 'Verilen Sipariş Detay',
                'subTitle' => 'Verilen Sipariş Detay',
                'item' => $order
            ]);
        } catch (\Exception $e) {
            return redirect()->route('backend.order_given_list')->with('error', 'Sipariş detayı görüntülenirken bir hata oluştu: ' . $e->getMessage());
        }
    }
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);
            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
            if ($item && $item->id) {
                $item->orderProducts = OrderProduct::where('order_id', $item->id)
                    ->where('is_active', 1)
                    ->get();
                foreach ($item->orderProducts as $product) {
                    if (empty($product->item_no)) {
                        $product->item_no = OrderProduct::generateItemNo($item->order_number);
                        $product->save();
                    }
                }
                if ($item->branch_id) {
                    view()->share('branchWarehouses', Warehouse::where('branch_id', $item->branch_id)->get());
                }
                if ($item->current_id) {
                    $currentList = Current::where('is_active', 1)->get();
                    $deletedCurrent = Current::withTrashed()->where('id', $item->current_id)->first();

                    if ($deletedCurrent && $deletedCurrent->deleted_at) {
                        $currentWithDeleted = $currentList->toBase();
                        $currentWithDeleted->push($deletedCurrent);
                        view()->share('current', $currentWithDeleted);
                    }
                }
            }
        } else {
            $item = new $this->model;
            $item->order_number = Order::generateOrderNumber(2);
            $sampleItemNos = [];
            for ($i = 1; $i <= 20; $i++) { // 20 örnek item_no oluştur
                $sampleItemNos[] = $item->order_number . '-' . str_pad($i, 2, '0', STR_PAD_LEFT);
            }
            view()->share('sampleItemNos', $sampleItemNos);
        }
        return view("backend.$this->page.form", compact('item'));
    }
    protected function datatableHook($datatable)
    {
        return $datatable
            ->addColumn('currency_type', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->currency_code;
                }
                return 'TRY'; // Varsayılan değer
            })
            ->addColumn('currency_symbol', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->symbol;
                }
                return '₺'; // Varsayılan değer (Türk Lirası)
            })
            ->addColumn('product_statuses', function ($item) {
                $statuses = [];
                if ($item->orderProducts && $item->orderProducts->count() > 0) {
                    foreach ($item->orderProducts as $product) {
                        $statuses[] = [
                            'status' => $product->status,
                            'quantity' => $product->quantity
                        ];
                    }
                }
                return $statuses;
            })
            ->addColumn('current_info', function ($item) {
                if ($item->current) {
                    return [
                        'name' => $item->current->name,
                        'surname' => $item->current->surname,
                        'deleted' => $item->current->deleted_at ? true : false,
                    ];
                }

                return [
                    'name' => '-',
                    'surname' => '-',
                    'deleted' => false,
                ];
            });
    }
    public function updateStatus(Request $request)
    {
        $productIds = $request->input('product_ids', []);
        $newStatus = $request->input('status');
        if (empty($productIds) || !isset($newStatus)) {
            return response()->json([
                'success' => false,
                'message' => "Ürün ID'leri veya durum bilgisi eksik"
            ]);
        }
        try {
            DB::beginTransaction();
            OrderProduct::with('stock')
                ->whereIn('id', $productIds)
                ->get()
                ->filter(fn($orderProduct) => $orderProduct->status != $newStatus)
                ->each(function ($orderProduct) use ($newStatus) {
                    $oldStatus = $orderProduct->status;
                    $orderProduct->status = $newStatus;
                    $orderProduct->save();
                    $stock = $orderProduct->stock;
                    if ($stock) {
                        $quantity = $orderProduct->quantity;
                        match (true) {
                            $oldStatus != 1 && $newStatus == 1 => $stock->increment('reserved_quantity', $quantity),
                            $oldStatus == 1 && $newStatus != 1 => $stock->decrement('reserved_quantity', min($quantity, $stock->reserved_quantity)),
                            default => null,
                        };
                    }
                });
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Ürün durumları başarıyla güncellendi'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Ürün durumları güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }
}
