<?php

namespace App\Http\Controllers\Backend;

use App\Models\Current;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Document;
use App\Helpers\ExcelExport;
use App\Helpers\PdfExport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InvoicesController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Satış Faturaları';
        $this->page = 'invoices';
        $this->upload = 'invoices';
        $this->model = new Invoice();
        $this->relation = ['current', 'documentType'];
        view()->share('current', Current::where('is_active', 1)->get());
        view()->share('products', Product::where('is_active', 1)->get());
        $this->view = (object)[
            'breadcrumb' => [
                'Satış Faturaları' => route('backend.invoices_list'),
            ],
        ];
        parent::__construct();
    }

    

    public function bootHook()
    {
        if (request()->has('export') && request()->export == 'excel') {
            return $this->exportInvoicesToExcel();
        }

        if (request()->has('pdf') && request()->has('id')) {
            return $this->generatePdf(request()->id);
        }

        return null;
    }

    // Excel dışa aktarma için özel route
    public function exportExcel()
    {
        return $this->exportInvoicesToExcel();
    }

    // PDF dışa aktarma için özel route
    public function exportPdf($id)
    {
        try {
            return $this->generatePdf($id);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("PDF oluşturulurken hata: " . $e->getMessage());
            return response()->json(['error' => 'PDF oluşturulurken hata: ' . $e->getMessage()], 500);
        }
    }

    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('invoice_date', function ($item) {
                return $item->invoice_date ? $item->invoice_date->format('d.m.Y') : '-';
            })
            ->editColumn('due_date', function ($item) {
                return $item->due_date ? $item->due_date->format('d.m.Y') : '-';
            })
            ->editColumn('total_amount', function ($item) {
                return number_format($item->total_amount, 2, ',', '.') . ' ₺';
            })
            ->addColumn('current_name', function ($item) {
                return $item->current ? $item->current->name : '-';
            })
            ->addColumn('status_name', function ($item) {
                $status = \App\Models\InvoiceStatus::where('id', $item->status)->first();
                if (!$status) {
                    // Eski durum değerlerini kontrol et
                    switch ($item->status) {
                        case 0:
                            return 'Beklemede';
                        case 1:
                            return 'Onaylandı';
                        case 2:
                            return 'Kısmi Muhasebelendirildi';
                        case 3:
                            return 'Muhasebelendirildi';
                        case 4:
                            return 'İptal Edildi';
                        default:
                            return '-';
                    }
                }
                return $status->name;
            })
            ->addColumn('remaining_amount', function ($item) {
                $remaining = $item->remaining_amount;
                return number_format($remaining, 2, ',', '.') . ' ₺';
            })
            ->addColumn('payment_status', function ($item) {
                $remaining = $item->remaining_amount;
                $total = $item->total_amount;

                if ($remaining <= 0) {
                    return '<span class="badge bg-success-subtle text-success">Ödendi</span>';
                } elseif ($remaining < $total) {
                    return '<span class="badge bg-warning-subtle text-warning">Kısmi Ödendi</span>';
                } else {
                    return '<span class="badge bg-danger-subtle text-danger">Ödenmedi</span>';
                }
            })
            ->editColumn('status', function ($item) {
                $statusName = '-';
                $class = 'bg-secondary-subtle text-secondary'; // Varsayılan renk

                // invoice_status tablosundan durumu al
                $invoiceStatus = \App\Models\InvoiceStatus::find($item->status);

                if ($invoiceStatus) {
                    $statusName = $invoiceStatus->name;

                    // Durum ID'sine göre renk belirle (Örnek eşleşmeler, ihtiyaca göre güncellenebilir)
                    switch ($invoiceStatus->id) {
                        // Örnek: ID 1 ise Onaylı (Success)
                        case 1:
                            $class = 'bg-success-subtle text-success';
                            break;
                        // Örnek: ID 4 ise İptal Edildi (Danger)
                        case 4:
                            $class = 'bg-danger-subtle text-danger';
                            break;
                        // Örnek: ID 0 ise Beklemede (Secondary - zaten varsayılan)
                         case 0:
                             $class = 'bg-secondary-subtle text-secondary';
                             break;
                        // Diğer durumlar için farklı renkler ekleyebilirsiniz
                        // case 5: // Örneğin yeni bir durum ID'si
                        //     $class = 'bg-info-subtle text-info';
                        //     break;
                        default:
                            $class = 'bg-secondary-subtle text-secondary'; // Eşleşmeyen durumlar için
                            break;
                    }
                } else {
                    // Eğer invoice_status kaydı bulunamazsa, eski manuel eşleşmeleri kullan
                     switch ($item->status) {
                         case 0:
                             $statusName = 'Beklemede';
                             $class = 'bg-secondary-subtle text-secondary';
                             break;
                         case 1:
                             $statusName = 'Onaylı';
                             $class = 'bg-success-subtle text-success';
                             break;
                         case 2:
                             $statusName = 'Kısmi Muhasebelendirildi';
                             $class = 'bg-info-subtle text-info';
                             break;
                         case 3:
                             $statusName = 'Muhasebelendirildi';
                             $class = 'bg-primary-subtle text-primary';
                             break;
                         case 4:
                             $statusName = 'İptal Edildi';
                             $class = 'bg-danger-subtle text-danger';
                             break;
                         default:
                             $statusName = '-';
                             $class = 'bg-secondary-subtle text-secondary';
                             break;
                     }
                }

                return '<span class="badge ' . $class . '">' . $statusName . '</span>';
            })
            ->editColumn('is_active', function ($item) {
                return $item->is_active == 1 ?
                    '<span class="badge bg-success">Aktif</span>' :
                    '<span class="badge bg-danger">Pasif</span>';
            })
            ->addColumn('items_data', function ($item) {
                $items = [];

                // Modeldeki cast sayesinde items zaten array olmalı
                $invoiceItems = $item->items ?? []; // Eğer null gelirse boş dizi kullan

                if (!empty($invoiceItems) && is_array($invoiceItems)) {
                    // Ürün ID'lerini topla ve tek sorguda ürünleri çek
                    $productIds = array_filter(array_column($invoiceItems, 'product_id'));
                    $products = [];
                    if (!empty($productIds)) {
                        $productsData = \App\Models\Product::whereIn('id', $productIds)->get()->keyBy('id');
                        $products = $productsData->toArray();
                    }

                    foreach ($invoiceItems as $itemData) {
                        $productId = $itemData['product_id'] ?? null;
                        $product = $productId && isset($products[$productId]) ? $products[$productId] : null;

                        // Gerekli alanları al ve formatla
                        $productName = $product ? $product['name'] : ($itemData['product_name'] ?? 'Bilinmeyen Ürün');
                        $quantity = $itemData['quantity'] ?? 0;
                        $salePrice = $itemData['sale_price'] ?? 0;
                        $vatRate = $itemData['vat_rate'] ?? ($product ? $product['default_vat_rate'] : 0); // Fatura kalemindeki KDV oranını veya üründeki varsayılanı kullan
                        $vatAmount = $itemData['vat_amount'] ?? ($salePrice * $quantity * $vatRate / 100); // Fatura kalemindeki KDV tutarını veya hesaplananı kullan
                        $total = $itemData['total'] ?? ($salePrice * $quantity + $vatAmount); // Fatura kalemindeki toplamı veya hesaplananı kullan

                        $items[] = [
                            'product_name' => $productName,
                            'quantity' => floatval($quantity),
                            'sale_price' => floatval($salePrice),
                            'vat_rate' => floatval($vatRate),
                            'vat_amount' => floatval($vatAmount),
                            'total' => floatval($total)
                        ];
                    }
                }

                // JSON formatında döndür
                return json_encode($items);
            })
            ->addColumn('payment_info', function ($item) {
                $totalPaid = $item->total_paid;
                $remainingAmount = $item->remaining_amount;

                return json_encode([
                    'total_amount' => $item->total_amount,
                    'total_amount_formatted' => number_format($item->total_amount, 2, ',', '.') . ' ' . ($item->currency_code ?? 'TL'),
                    'total_paid' => $totalPaid,
                    'total_paid_formatted' => number_format($totalPaid, 2, ',', '.') . ' ' . ($item->currency_code ?? 'TL'),
                    'remaining_amount' => $remainingAmount,
                    'remaining_amount_formatted' => number_format($remainingAmount, 2, ',', '.') . ' ' . ($item->currency_code ?? 'TL')
                ]);
            })
            ->orderColumn('current_name', function ($query, $order) {
                $query->leftJoin('current', 'invoices.current_id', '=', 'current.id')
                      ->orderBy('current.name', $order);
            })
            ->rawColumns(['payment_status', 'status', 'is_active']);
    }

    private function generateInvoiceNumber($month = null, $year = null): string
    {
        // Bu metot artık Helper tarafından yönetiliyor
        return \App\Libraries\Helpers::generateNextSequenceNumber('FTR', $month, $year);
    }

    public function getNextInvoiceNumber(): JsonResponse
    {
        try {
            $invoiceNo = $this->generateInvoiceNumber();

            return response()->json([
                'success' => true,
                'invoice_no' => $invoiceNo
            ]);
        } catch (\Exception $e) {
            Log::error("Fatura numarası oluşturulurken hata: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Fatura numarası oluşturulurken hata: ' . $e->getMessage()
            ], 500);
        }
    }

    
    public function formHook($item)
    {
        if (!$item->id) {
            $item->invoice_no = \App\Libraries\Helpers::generateNextSequenceNumber('FTR');
            $item->invoice_date = now();
            $item->due_date = now()->addDays(30);
            $item->status = 0;
            $item->is_active = 1;
        } elseif (empty($item->invoice_no)) {
            $item->invoice_no = \App\Libraries\Helpers::generateNextSequenceNumber('FTR');
        }
        return $item;
    }

   
}
