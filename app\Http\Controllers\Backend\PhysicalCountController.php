<?php

namespace App\Http\Controllers\Backend;

use App\Models\PhysicalCount;
use App\Models\PhysicalCountItem;
use App\Models\Product;
use App\Models\Status;
use App\Models\Stock;
use App\Models\StockMovement;
use App\Models\StockMovementReason;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PhysicalCountController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Fiziksel Sayımlar';
        $this->page = 'physical_count';
        $this->model = new PhysicalCount();
        $this->relation = ['items', 'warehouse', 'location', 'status', 'approver'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Stok Yönetimi' => '#',
                'Fiziksel Sayımlar' => route('backend.physical_count_list'),
            ),
        );

        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->get());
        view()->share('statuses', Status::active()->get());
        view()->share('products', Product::active()->get());

        parent::__construct();
    }

    public function status(Request $request)
    {
        $count = PhysicalCount::find($request->id);

        if (!$count) {
            return response()->json(['status' => false, 'message' => 'Sayım bulunamadı']);
        }

        DB::beginTransaction();

        try {
            $previousStatus = $count->status_id;
            $count->status_id = $request->status_id;

            // Sadece status güncelle
            if ($request->status_id == 2 && $previousStatus != 2) {
                $count->approver_id = Auth::user()->id;
                $count->approval_date = now();
            }

            $count->save();

            DB::commit();
            return response()->json(['status' => true, 'message' => 'Durum güncellendi']);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => 'Hata oluştu: ' . $e->getMessage()]);
        }
    }

    private function processStockAdjustments($physicalCount)
    {
        $updatedCount = 0;
        Log::info('processStockAdjustments başladı', [
            'item_count' => $physicalCount->items->count(),
            'physical_count_id' => $physicalCount->id,
            'warehouse_id' => $physicalCount->warehouse_id
        ]);

        foreach ($physicalCount->items as $item) {
            // Her kalemi detaylı loglayalım
            Log::info('Item inceleniyor', [
                'item_id' => $item->id,
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'counted' => $item->counted_quantity,
                'system' => $item->system_quantity,
                'difference' => $item->difference
            ]);

            // Fark yoksa işleme almayalım
            if ($item->difference == 0) {
                Log::info('Fark sıfır, işlem yapılmadı', ['item_id' => $item->id]);
                continue;
            }

            // İlgili stoğu bul - BURADA ÖNEMLİ: ID'leri log'layalım
            $stockQuery = Stock::query()
                ->where('product_id', $item->product_id)
                ->where(function($q) use ($item) {
                    $q->where('variant_id', $item->variant_id)
                      ->orWhereNull('variant_id');
                })
                ->where('warehouse_id', $physicalCount->warehouse_id);

            // Lokasyon varsa ekle
            if ($physicalCount->location_id) {
                $stockQuery->where('warehouse_location_id', $physicalCount->location_id);
            } else {
                $stockQuery->whereNull('warehouse_location_id');
            }

            // SQL sorgusunu logla
            Log::info('Stok sorgu SQL: ' . $stockQuery->toSql(), [
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'warehouse_id' => $physicalCount->warehouse_id,
                'location_id' => $physicalCount->location_id
            ]);

            $stock = $stockQuery->first();

            Log::info('Stok bulma sorgusu sonucu', [
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'warehouse_id' => $physicalCount->warehouse_id,
                'location_id' => $physicalCount->location_id,
                'stok_bulundu' => $stock ? 'Evet' : 'Hayır'
            ]);

            if (!$stock) {
                Log::warning('Stok bulunamadı, yeni oluşturuluyor', [
                    'product_id' => $item->product_id,
                    'variant_id' => $item->variant_id,
                    'warehouse_id' => $physicalCount->warehouse_id,
                    'location_id' => $physicalCount->location_id
                ]);

                // Stok yoksa oluştur
                $stock = new Stock();
                $stock->product_id = $item->product_id;
                $stock->variant_id = $item->variant_id;
                $stock->warehouse_id = $physicalCount->warehouse_id;
                $stock->warehouse_location_id = $physicalCount->location_id;
                $stock->quantity = 0; // Başlangıçta 0
                $stock->is_active = 1;
                $stock->created_by = Auth::user()->id;
                $stock->save();

                Log::info('Yeni stok oluşturuldu', [
                    'stock_id' => $stock->id,
                    'product_id' => $stock->product_id,
                    'variant_id' => $stock->variant_id
                ]);
            }

            // Stok hareketini oluşturmadan önce stok miktarını ve farkı loglayalım
            Log::info('Stok hareketi oluşturulmadan önce durum', [
                'stock_id' => $stock->id,
                'önceki_miktar' => $stock->quantity,
                'sayılan_miktar' => $item->counted_quantity,
                'fark' => $item->difference,
                'fark_abs' => abs($item->difference)
            ]);

            // Stok hareketi oluştur
            $movement = new StockMovement();
            $movement->stock_id = $stock->id;
            $movement->product_id = $item->product_id;
            $movement->variant_id = $item->variant_id;
            $movement->warehouse_id = $physicalCount->warehouse_id;
            $movement->location_id = $physicalCount->location_id;
            $movement->quantity = abs($item->difference);
            $movement->movement_date = now();
            $movement->status_id = 2; // Onaylı
            $movement->starter_id = Auth::user()->id;
            $movement->approver_id = Auth::user()->id;
            $movement->approval_date = now();

            // Hareket nedenini belirle
            if ($item->difference > 0) {
                // Fazla sayıldı, giriş hareketi
                $movement->stock_movement_reason_id = 4; // Fiziksel sayım girişi
                $movement->notes = 'Fiziksel sayım düzeltmesi - Sayım No: ' . $physicalCount->count_code;
            } else {
                // Eksik sayıldı, çıkış hareketi
                $movement->stock_movement_reason_id = 9; // Fiziksel sayım çıkışı
                $movement->notes = 'Fiziksel sayım düzeltmesi - Sayım No: ' . $physicalCount->count_code;
            }

            $movement->save();
            Log::info('Stok hareketi oluşturuldu', [
                'movement_id' => $movement->id,
                'reason_id' => $movement->stock_movement_reason_id,
                'quantity' => $movement->quantity,
                'product_id' => $movement->product_id
            ]);

            // STOK MİKTARINI GÜNCELLE - YENİ YAKLAŞIM
            $oldQuantity = $stock->quantity;  // Eski miktarı kaydet

            // DOĞRUDAN VERİTABANI GÜNCELLEME DENE
            $updated = DB::table('stocks')
                ->where('id', $stock->id)
                ->update(['quantity' => $item->counted_quantity]);

            Log::info('Stok doğrudan DB update', [
                'stock_id' => $stock->id,
                'güncellendi_mi' => $updated ? 'Evet' : 'Hayır',
                'hedef_miktar' => $item->counted_quantity
            ]);

            // Model üzerinden de güncelle (Çift güvenlik)
            $stock->refresh(); // Veritabanından tekrar yükle
            $stock->quantity = $item->counted_quantity;
            $stock->save();

            // Güncelleme sonrası modeli tekrar yükleyerek kontrol et
            $stock->refresh();

            Log::info('Stok güncellendi', [
                'stock_id' => $stock->id,
                'eski_miktar' => $oldQuantity,
                'yeni_miktar' => $stock->quantity,
                'sayılan_miktar' => $item->counted_quantity,
                'başarılı_mı' => ($stock->quantity == $item->counted_quantity) ? 'EVET' : 'HAYIR'
            ]);

            $updatedCount++;
        }

        return $updatedCount;
    }

    public function detail(Request $request, $physical_count_id = null, $unique = null)
    {
        $physicalCount = PhysicalCount::with(['items.product', 'items.variant', 'warehouse', 'location'])->find($physical_count_id);

        if (!$physicalCount) {
            return redirect()->route('backend.physical_count_list')->with('error', 'Sayım bulunamadı');
        }

        // Eğer sayım kalemleri yoksa otomatik oluştur
        if ($physicalCount->items->count() == 0) {
            $this->createCountItems($physicalCount);

            // Kalemleri yeniden yükle
            $physicalCount->load(['items.product', 'items.variant']);
        }

        return view("backend.$this->page.detail", compact('physicalCount'));
    }

    private function createCountItems($physicalCount)
    {
        // Seçilen depo ve lokasyondaki tüm stokları al
        $query = Stock::where('warehouse_id', $physicalCount->warehouse_id)
            ->where('is_active', 1);

        // Eğer lokasyon seçildiyse filtrele, yoksa tüm lokasyonlar
        if ($physicalCount->location_id) {
            $query->where('warehouse_location_id', $physicalCount->location_id);
        }

        // Stokları getir (miktar 0 olanlar dahil)
        $stocks = $query->with(['product', 'variant'])->get();

        if ($stocks->count() == 0) {
            // Stok yoksa, depodaki tüm aktif ürünleri ekleyelim
            $products = Product::where('is_active', 1)->get();

            foreach ($products as $product) {
                PhysicalCountItem::create([
                    'physical_count_id' => $physicalCount->id,
                    'product_id' => $product->id,
                    'variant_id' => null,
                    'system_quantity' => 0,
                    'counted_quantity' => 0,
                    'difference' => 0,
                    'is_active' => 1,
                    'created_by' => Auth::user()->id
                ]);
            }
        } else {
            // Tüm stokları sayım kalemlerine dönüştür
            foreach ($stocks as $stock) {
                PhysicalCountItem::create([
                    'physical_count_id' => $physicalCount->id,
                    'product_id' => $stock->product_id,
                    'variant_id' => $stock->variant_id,
                    'system_quantity' => $stock->quantity,
                    'counted_quantity' => 0, // Başlangıçta 0 olarak ayarla
                    'difference' => -$stock->quantity, // Sistem miktarı - sayılan miktar (0)
                    'is_active' => 1,
                    'created_by' => Auth::user()->id
                ]);
            }
        }
    }

    public function refreshStock($id)
    {
        Log::info('refreshStock metodu çağrıldı', ['id' => $id]);

        // Tüm önbellekleri temizle
        DB::statement('SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED');

        $physicalCount = PhysicalCount::with(['items'])->find($id);

        if (!$physicalCount) {
            Log::error('Sayım bulunamadı', ['id' => $id]);
            return response()->json(['status' => false, 'message' => 'Sayım bulunamadı']);
        }

        if ($physicalCount->status_id != 2) {
            Log::error('Sayım onaylanmamış', ['status_id' => $physicalCount->status_id, 'id' => $id]);
            return response()->json(['status' => false, 'message' => 'Sadece onaylanmış sayımlar için stok güncellemesi yapılabilir']);
        }

        Log::info('Fiziksel Sayım Detayları', [
            'id' => $physicalCount->id,
            'count_code' => $physicalCount->count_code,
            'items_count' => $physicalCount->items->count(),
            'warehouse_id' => $physicalCount->warehouse_id,
            'location_id' => $physicalCount->location_id
        ]);

        $updatedCount = 0;
        $errorCount = 0;
        $errorMessages = [];

        // DB::beginTransaction() kullanmak yerine doğrudan işlem yapalım
        try {
            // Her bir kalem için işlem yapalım
            foreach ($physicalCount->items as $item) {
                if ($item->difference == 0) {
                    Log::info('Fark sıfır, işlem yapılmadı', ['item_id' => $item->id]);
                    continue;
                }

                // ÖNEMLİ DEĞİŞİKLİK: Stok hareketi ve stok güncellemesini tamamen farklı bir yoldan yapalım
                try {
                    // 1. İlk önce ilgili stok kaydını bulalım
                    $stockRow = DB::table('stocks')
                        ->select('id', 'quantity')
                        ->where('product_id', $item->product_id)
                        ->where(function($q) use ($item) {
                            if ($item->variant_id) {
                                $q->where('variant_id', $item->variant_id);
                            } else {
                                $q->whereNull('variant_id');
                            }
                        })
                        ->where('warehouse_id', $physicalCount->warehouse_id)
                        ->where(function($q) use ($physicalCount) {
                            if ($physicalCount->location_id) {
                                $q->where('warehouse_location_id', $physicalCount->location_id);
                            } else {
                                $q->whereNull('warehouse_location_id');
                            }
                        })
                        ->first();

                    Log::info('Stok sorgu sonucu', [
                        'product_id' => $item->product_id,
                        'variant_id' => $item->variant_id ? $item->variant_id : 'NULL',
                        'warehouse_id' => $physicalCount->warehouse_id,
                        'location_id' => $physicalCount->location_id ? $physicalCount->location_id : 'NULL',
                        'stock_bulundu' => $stockRow ? 'EVET' : 'HAYIR'
                    ]);

                    $stockId = null;

                    // Stok yoksa oluştur
                    if (!$stockRow) {
                        Log::warning('Stok bulunamadı, yeni oluşturuluyor', [
                            'product_id' => $item->product_id,
                            'variant_id' => $item->variant_id
                        ]);

                        // Yeni stok kaydı oluştur
                        $stockId = DB::table('stocks')->insertGetId([
                            'product_id' => $item->product_id,
                            'variant_id' => $item->variant_id,
                            'warehouse_id' => $physicalCount->warehouse_id,
                            'warehouse_location_id' => $physicalCount->location_id,
                            'quantity' => $item->counted_quantity,
                            'is_active' => 1,
                            'created_by' => Auth::id(),
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);

                        Log::info('Yeni stok kaydı oluşturuldu', [
                            'stock_id' => $stockId,
                            'miktar' => $item->counted_quantity
                        ]);
                    } else {
                        $stockId = $stockRow->id;

                        // Var olan stok kaydını güncelle
                        $updateResult = DB::statement("
                            UPDATE stocks
                            SET quantity = ?, updated_at = NOW(), updated_by = ?
                            WHERE id = ?
                        ", [
                            $item->counted_quantity,
                            Auth::id(),
                            $stockId
                        ]);

                        Log::info('Stok güncellendi', [
                            'stock_id' => $stockId,
                            'eski_miktar' => $stockRow->quantity,
                            'yeni_miktar' => $item->counted_quantity,
                            'sonuç' => $updateResult ? 'BAŞARILI' : 'BAŞARISIZ'
                        ]);
                    }

                    // 2. Stok hareketi oluştur
                    $reasonId = $item->difference > 0 ? 4 : 9; // 4=Fiziksel sayım girişi, 9=Fiziksel sayım çıkışı

                    $movementId = DB::table('stock_movements')->insertGetId([
                        'stock_id' => $stockId,
                        'product_id' => $item->product_id,
                        'variant_id' => $item->variant_id,
                        'quantity' => abs($item->difference),
                        'warehouse_id' => $physicalCount->warehouse_id,
                        'location_id' => $physicalCount->location_id,
                        'movement_date' => now(),
                        'status_id' => 2, // Onaylı
                        'stock_movement_reason_id' => $reasonId,
                        'starter_id' => Auth::id(),
                        'approver_id' => Auth::id(),
                        'approval_date' => now(),
                        'notes' => 'Fiziksel sayım düzeltmesi - Sayım No: ' . $physicalCount->count_code,
                        'is_active' => 1,
                        'created_by' => Auth::id(),
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);

                    Log::info('Stok hareketi oluşturuldu', [
                        'movement_id' => $movementId,
                        'stock_id' => $stockId,
                        'product_id' => $item->product_id,
                        'miktar' => abs($item->difference),
                        'yön' => $item->difference > 0 ? 'GİRİŞ' : 'ÇIKIŞ'
                    ]);

                    // 3. Son kontrolü yap
                    $finalCheck = DB::table('stocks')->where('id', $stockId)->first();

                    Log::info('Son kontrol', [
                        'stock_id' => $stockId,
                        'güncel_miktar' => $finalCheck->quantity,
                        'hedef_miktar' => $item->counted_quantity,
                        'eşleşiyor_mu' => $finalCheck->quantity == $item->counted_quantity ? 'EVET' : 'HAYIR'
                    ]);

                    $updatedCount++;
                } catch (\Exception $itemError) {
                    $errorCount++;
                    $errorMessages[] = "Ürün ID: {$item->product_id} için hata: " . $itemError->getMessage();
                    Log::error('Stok güncellerken hata', [
                        'product_id' => $item->product_id,
                        'variant_id' => $item->variant_id,
                        'error' => $itemError->getMessage()
                    ]);
                    continue; // Hata alsa bile diğer ürünlere devam et
                }
            }

            // Genel durum özeti
            $message = "İşlem tamamlandı. $updatedCount kalem güncellendi.";
            if ($errorCount > 0) {
                $message .= " $errorCount kalemde hata oluştu.";
            }

            Log::info('Stok güncelleme tamamlandı', [
                'başarılı' => $updatedCount,
                'hatalı' => $errorCount
            ]);

            return response()->json([
                'status' => true,
                'message' => $message,
                'updated_count' => $updatedCount,
                'error_count' => $errorCount,
                'error_details' => $errorMessages
            ]);

        } catch (\Exception $e) {
            Log::error('Stok güncelleme ana hatası', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => 'Hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * İşlem sonrası stok güncellemelerinin doğru şekilde yapıldığını doğrula
     */
    private function verifyStockUpdates($physicalCount)
    {
        // Cache'i temizle
        DB::statement('COMMIT'); // İşlemi kesin olarak tamamla

        foreach ($physicalCount->items as $item) {
            if ($item->difference == 0) continue;

            // Stok durumunu yeniden sorgula - DOĞRUDAN SORGU
            $stockFromDB = DB::table('stocks')
                ->select('id', 'product_id', 'variant_id', 'quantity')
                ->where('product_id', $item->product_id)
                ->where(function($q) use ($item) {
                    if ($item->variant_id) {
                        $q->where('variant_id', $item->variant_id);
                    } else {
                        $q->whereNull('variant_id');
                    }
                })
                ->where('warehouse_id', $physicalCount->warehouse_id)
                ->where(function($q) use ($physicalCount) {
                    if ($physicalCount->location_id) {
                        $q->where('warehouse_location_id', $physicalCount->location_id);
                    } else {
                        $q->whereNull('warehouse_location_id');
                    }
                })
                ->first();

            Log::info('VERİFY: Doğrudan DB sorgusu sonucu', [
                'product_id' => $item->product_id,
                'variant_id' => $item->variant_id,
                'sonuç' => $stockFromDB ? json_encode($stockFromDB) : 'BULUNAMADI',
                'sayılan_miktar' => $item->counted_quantity
            ]);

            if ($stockFromDB) {
                Log::info('VERİFY: İşlem sonrası stok kontrolü', [
                    'product_id' => $item->product_id,
                    'variant_id' => $item->variant_id,
                    'sayılan_miktar' => $item->counted_quantity,
                    'db_miktar' => $stockFromDB->quantity,
                    'eşleşiyor_mu' => ($item->counted_quantity == $stockFromDB->quantity) ? 'Evet' : 'Hayır'
                ]);

                // Eşleşmiyorsa tekrar güncelle - ZORUNLU GÜNCELLEME
                if ($stockFromDB->quantity != $item->counted_quantity) {
                    Log::warning('ZORLA GÜNCELLEME: Stok miktarı eşleşmiyor!', [
                        'stock_id' => $stockFromDB->id,
                        'mevcut_miktar' => $stockFromDB->quantity,
                        'olması_gereken' => $item->counted_quantity
                    ]);

                    DB::statement('UPDATE stocks SET quantity = ? WHERE id = ?', [
                        $item->counted_quantity,
                        $stockFromDB->id
                    ]);

                    // Son kontrol
                    $finalCheck = DB::table('stocks')->where('id', $stockFromDB->id)->first();

                    Log::info('ZORLA GÜNCELLEME SONUÇ', [
                        'stock_id' => $stockFromDB->id,
                        'son_miktar' => $finalCheck->quantity,
                        'başarılı_mı' => ($finalCheck->quantity == $item->counted_quantity) ? 'EVET' : 'HAYIR'
                    ]);
                }
            }
        }
    }

    // Detail sayfasından yapılan kayıtlar için
    public function save(Request $request, $unique = NULL)
    {
        if ($request->ajax()) {
            DB::beginTransaction();
            try {
                // Sayım kalemlerini işle
                if (isset($request->items) && is_array($request->items)) {
                    foreach ($request->items as $itemId => $itemData) {
                        $countItem = PhysicalCountItem::find($itemId);
                        if ($countItem) {
                            // Sayılan miktarı ve notları güncelle
                            $countItem->counted_quantity = $itemData['counted_quantity'] ?? $countItem->counted_quantity;
                            $countItem->notes = $itemData['notes'] ?? $countItem->notes;

                            // Farkı hesapla
                            $countItem->difference = $countItem->counted_quantity - $countItem->system_quantity;
                            $countItem->save();
                        }
                    }
                }

                // Ana sayım kaydını da güncelle
                $count = PhysicalCount::find($request->id);
                if ($count) {
                    if (isset($request->description)) {
                        $count->description = $request->description;
                    }
                    $count->save();
                }

                DB::commit();
                return response()->json(['status' => true, 'message' => 'Sayım başarıyla güncellendi']);
            } catch (\Exception $e) {
                DB::rollback();
                return response()->json(['status' => false, 'message' => 'Hata oluştu: ' . $e->getMessage()]);
            }
        }

        // Normal form gönderimi için default metodu çağır
        return parent::save($request);
    }

    public function saveHook($request)
    {
        $params = $request->all();

        // Sayım kalemlerini işle
        if (isset($params['items']) && is_array($params['items'])) {
            foreach ($params['items'] as $itemId => $itemData) {
                $countItem = PhysicalCountItem::find($itemId);
                if ($countItem) {
                    // Sayılan miktarı ve notları güncelle
                    $countItem->counted_quantity = $itemData['counted_quantity'] ?? $countItem->counted_quantity;
                    $countItem->notes = $itemData['notes'] ?? $countItem->notes;

                    // Farkı hesapla
                    $countItem->difference = $countItem->counted_quantity - $countItem->system_quantity;
                    $countItem->save();
                }
            }

            // items parametresini kaldır, ana sayım kaydı için kullanılmayacak
            unset($params['items']);
        }

        // Sayım kodu oluştur
        if (!isset($params['count_code'])) {
            $params['count_code'] = 'COUNT-' . date('YmdHis') . '-' . rand(1000, 9999);
        }

        // Varsayılan status
        if (!isset($params['status_id'])) {
            $params['status_id'] = 1; // Taslak
        }

        return $params;
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('count_date', function ($item) {
            return $item->count_date->format('d.m.Y H:i');
        });
    }

    // Yeni eklenen metot - Kayıt sonrası işlem
    public function saveBack($obj)
    {
        // Yeni oluşturulan veya düzenlenen sayım için sayım kalemleri oluştur
        if ($obj->items->count() == 0) {
            $this->createCountItems($obj);
        }

        // Detay sayfasına yönlendir
        return redirect()->route("backend.physical_count_detail", $obj->id)->with('success', 'Sayım başarıyla kaydedildi.');
    }
}
