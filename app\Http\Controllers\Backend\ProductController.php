<?php

namespace App\Http\Controllers\Backend;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ProductController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Ürünler';
        $this->page = 'product';
        $this->model = new Product();
        $this->relation = ['category', 'brand', 'unit', 'unitType', 'variants'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Ürün Yönetimi' => '#',
                'Ürünler' => route('backend.product_list'),
            ),
        );

        view()->share('categories', Category::active()->get());
        view()->share('brands', Brand::active()->get());
        view()->share('units', Unit::active()->get());
        view()->share('unitTypes', UnitType::active()->get());
        
        parent::__construct();
    }

    public function detail(Request $request, $product_id = null, $unique = null)
    {
        $product = Product::with([
            'category', 
            'brand', 
            'unit', 
            'unitType', 
            'variants', 
            'stocks' => function($query) {
                $query->with(['warehouse', 'warehouseLocation', 'stockBatch']);
            },
            'stockMovements' => function($query) {
                $query->with(['warehouse', 'location', 'status', 'stockMovementReason'])
                    ->orderBy('movement_date', 'desc')
                    ->limit(10);
            },
            'unitConversions' => function($query) {
                $query->with(['fromUnit', 'toUnit']);
            }
        ])->find($product_id);
        
        if (!$product) {
            return redirect()->route('backend.product_list')->with('error', 'Ürün bulunamadı');
        }

        // Toplam stok hesapla
        $totalStock = $product->stocks->sum('quantity');
        
        // Varyant bazlı stok özeti
        $variantStocks = $product->variants->map(function($variant) {
            return [
                'variant' => $variant,
                'total_stock' => $variant->stocks->sum('quantity'),
                'warehouses' => $variant->stocks->groupBy('warehouse_id')->map(function($stocks) {
                    return [
                        'warehouse' => $stocks->first()->warehouse,
                        'quantity' => $stocks->sum('quantity')
                    ];
                })
            ];
        });

        return view("backend.$this->page.detail", compact('product', 'totalStock', 'variantStocks'));
    }

    public function saveHook($request)
    {
        $params = $request->all();
        
        // SKU otomatik oluştur (yeni kayıt ise)
        if (!isset($params['id']) || !isset($params['sku']) || empty($params['sku'])) {
            $params['sku'] = $this->generateSku($params);
        }
        
        // SKU benzersizlik kontrolü
        $exists = Product::where('sku', $params['sku']);
        
        if (isset($params['id'])) {
            $exists->where('id', '!=', $params['id']);
        }
        
        if ($exists->exists()) {
            // SKU mevcut ise yeni bir tane oluştur
            $params['sku'] = $this->generateSku($params, true);
        }
        
        // Fiyat kontrolü
        if (isset($params['sale_price']) && isset($params['purchase_price'])) {
            if ($params['sale_price'] < $params['purchase_price']) {
                // Uyarı - satış fiyatı alış fiyatından düşük
                $params['notes'] = ($params['notes'] ?? '') . "\nUYARI: Satış fiyatı alış fiyatından düşük!";
            }
        }
        
        return $params;
    }

    private function generateSku($params, $unique = false)
    {
        $sku = '';
        
        // Kategori kodu
        if (isset($params['category_id']) && $params['category_id']) {
            $category = Category::find($params['category_id']);
            if ($category) {
                $sku .= strtoupper(substr($category->name, 0, 3));
            }
        } else {
            $sku .= 'PRD';
        }
        
        // Marka kodu
        if (isset($params['brand_id']) && $params['brand_id']) {
            $brand = Brand::find($params['brand_id']);
            if ($brand) {
                $sku .= '-' . strtoupper(substr($brand->name, 0, 3));
            }
        } else {
            $sku .= '-GEN';
        }
        
        // Ürün adından kod
        if (isset($params['name']) && $params['name']) {
            $namePart = preg_replace('/[^A-Za-z0-9]/', '', $params['name']);
            $sku .= '-' . strtoupper(substr($namePart, 0, 3));
        }
        
        // Benzersiz numara
        if ($unique) {
            $sku .= '-' . date('YmdHis');
        } else {
            $sku .= '-' . str_pad(Product::count() + 1, 4, '0', STR_PAD_LEFT);
        }
        
        return $sku;
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('category_name', function ($item) {
            return $item->category ? $item->category->name : '-';
        })
        ->editColumn('brand_name', function ($item) {
            return $item->brand ? $item->brand->name : '-';
        })
        ->editColumn('unit_name', function ($item) {
            return $item->unit ? $item->unit->name : '-';
        })
        ->editColumn('total_stock', function ($item) {
            return $item->stocks->sum('quantity');
        })
        ->editColumn('variant_count', function ($item) {
            return $item->variants->count();
        });
    }
}
