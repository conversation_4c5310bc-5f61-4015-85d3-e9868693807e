<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductUnitConversion;
use App\Models\ProductVariant;
use App\Models\Unit;
use Illuminate\Http\Request;

class ProductUnitConversionController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Ürün Birim Dönüşümleri';
        $this->page = 'product_unit_conversion';
        $this->model = new ProductUnitConversion();
        $this->relation = ['product', 'variant', 'fromUnit', 'toUnit'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Ürün Yönetimi' => '#',
                'Ürün Birim Dönüşümleri' => route('backend.product_unit_conversion_list'),
            ),
        );

        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('units', Unit::active()->get());
        
        parent::__construct();
    }

    public function saveHook($request)
    {
        $params = $request->all();
        
        // Aynı birim kontrolü
        if ($params['from_unit_id'] == $params['to_unit_id']) {
            throw new \Exception('Kaynak ve hedef birim aynı olamaz');
        }
        
        // Conversion factor kontrolü
        if ($params['conversion_factor'] <= 0) {
            throw new \Exception('Dönüşüm katsayısı 0\'dan büyük olmalıdır');
        }
        
        // Aynı dönüşüm var mı kontrolü
        $exists = ProductUnitConversion::where('product_id', $params['product_id'])
            ->where('variant_id', $params['variant_id'])
            ->where('from_unit_id', $params['from_unit_id'])
            ->where('to_unit_id', $params['to_unit_id']);
            
        if (isset($params['id'])) {
            $exists->where('id', '!=', $params['id']);
        }
        
        if ($exists->exists()) {
            throw new \Exception('Bu ürün için aynı birim dönüşümü zaten mevcut');
        }
        
        return $params;
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('conversion_factor', function ($item) {
            return number_format($item->conversion_factor, 5, ',', '.');
        })
        ->editColumn('conversion_text', function ($item) {
            return '1 ' . $item->fromUnit->symbol . ' = ' . number_format($item->conversion_factor, 5, ',', '.') . ' ' . $item->toUnit->symbol;
        });
    }
}
