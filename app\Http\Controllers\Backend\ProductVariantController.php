<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Http\Request;

class ProductVariantController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = '<PERSON>rü<PERSON> Varyantları';
        $this->page = 'product_variant';
        $this->model = new ProductVariant();
        $this->relation = ['product', 'stocks'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Ürün Yönetimi' => '#',
                'Ürün Varyantları' => route('backend.product_variant_list'),
            ),
        );

        view()->share('products', Product::active()->get());

        parent::__construct();
    }
}
