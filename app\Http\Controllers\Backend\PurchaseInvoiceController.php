<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Http\Requests\Backend\PurchaseInvoiceRequest;
use App\Models\Current;
use App\Models\PurchaseInvoice;
use App\Models\Document;
use App\Helpers\ExcelExport;
use App\Helpers\PdfExport;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PurchaseInvoiceController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Alış Faturaları';
        $this->page = 'purchase_invoices';
        $this->model = new PurchaseInvoice();

        // Join ile sorgu oluşturma
        $this->listQuery = PurchaseInvoice::leftJoin('currents', 'purchase_invoices.current_id', '=', 'currents.id')
            ->select('purchase_invoices.*', 'currents.name as current_name');

        $this->relation = ['current'];

        // DataTables sıralama için özel sütun tanımlamaları
        $this->orderColumns = [
            'current_name' => 'current.name',
            'name' => 'current.name' // 'name' sütunu için 'current.name' sütununu kullan
        ];

        $this->view = (object)[
            'breadcrumb' => [
                'Finans' => '#',
                'Alış Faturaları' => route('backend.purchase_invoices_list'),
            ],
        ];

        // View'lara global veriler gönderiyoruz
        view()->share('products', Product::where('is_active', 1)->get());
        view()->share('currents', Current::where('is_active', 1)->get());
        view()->share('documentType', Document::all());

        parent::__construct();
    }

    /**
     * Hook metodu: list metodundan önce çalışır
     * Excel veya PDF dışa aktarma isteklerini işler
     */
    public function bootHook()
    {
        // Excel dışa aktarma isteği
        if (request()->has('export') && request()->export == 'excel') {
            return $this->exportPurchaseInvoicesToExcel();
        }

        // PDF dışa aktarma isteği
        if (request()->has('pdf') && request()->has('id')) {
            return $this->generatePdf(request()->id);
        }

        return null;
    }

    /**
     * Excel dışa aktarma için özel route
     */
    public function exportExcel()
    {
        return $this->exportPurchaseInvoicesToExcel();
    }

    /**
     * PDF dışa aktarma için özel route
     */
    public function exportPdf($id)
    {
        try {
            return $this->generatePdf($id);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("PDF oluşturulurken hata: " . $e->getMessage());
            return response()->json(['error' => 'PDF oluşturulurken hata: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Hook metodu: Datatable verilerini özelleştirmek için
     */
    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('invoice_date', function ($row) {
                return $row->invoice_date ? Carbon::parse($row->invoice_date)->format('d.m.Y') : '-';
            })
            ->editColumn('due_date', function ($row) {
                return $row->due_date ? Carbon::parse($row->due_date)->format('d.m.Y') : '-';
            })
            ->editColumn('total_amount', function ($row) {
                return number_format($row->total_amount, 2, ',', '.') . ' ₺';
            })
            ->editColumn('current_name', function ($row) {
                return $row->current_name ?? '-';
            })
            ->editColumn('is_active', function ($row) {
                return $row->is_active == 1 ?
                    '<span class="badge bg-success">Aktif</span>' :
                    '<span class="badge bg-danger">Pasif</span>';
            })
            ->addColumn('payment_status', function ($row) {
                $statusClasses = [
                    0 => 'bg-secondary-subtle text-secondary',
                    1 => 'bg-success-subtle text-success',
                    2 => 'bg-info-subtle text-info',
                    3 => 'bg-primary-subtle text-primary',
                    4 => 'bg-danger-subtle text-danger'
                ];

                $class = $statusClasses[$row->invoice_status] ?? 'bg-secondary-subtle text-secondary';
                $statusName = '';
                
                switch ($row->invoice_status) {
                    case 0:
                        $statusName = 'Beklemede';
                        break;
                    case 1:
                        $statusName = 'Onaylandı';
                        break;
                    case 2:
                        $statusName = 'Kısmi Muhasebelendi';
                        break;
                    case 3:
                        $statusName = 'Muhasebelendi';
                        break;
                    case 4:
                        $statusName = 'İptal';
                        break;
                    default:
                        $statusName = 'Bilinmiyor';
                }

                return '<span class="badge ' . $class . '">' . $statusName . '</span>';
            })
            ->addColumn('items_data', function ($row) {
                $items = [];
                if (isset($row->items) && is_array($row->items)) {
                    foreach ($row->items as $item) {
                        $productName = '';
                        if (isset($item['product_id'])) {
                            $product = Product::find($item['product_id']);
                            if ($product) {
                                $productName = $product->name;
                            }
                        }

                        $vatAmount = isset($item['vat_amount']) && is_numeric($item['vat_amount']) ? $item['vat_amount'] : 0;
                        $purchasePrice = isset($item['purchase_price']) ? $item['purchase_price'] : (isset($item['sale_price']) ? $item['sale_price'] : 0);

                        $items[] = [
                            'product_name' => $productName ?: ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                            'quantity' => $item['quantity'] ?? 0,
                            'purchase_price' => $purchasePrice,
                            'sale_price' => $purchasePrice,
                            'vat_rate' => $item['vat_rate'] ?? 0,
                            'vat_amount' => $vatAmount,
                            'total' => $item['total'] ?? 0
                        ];
                    }
                }
                return json_encode($items);
            })
            ->addColumn('payment_info', function ($row) {
                $totalPaid = $row->total_paid;
                $remainingAmount = $row->remaining_amount;

                return json_encode([
                    'total_amount' => $row->total_amount,
                    'total_amount_formatted' => number_format($row->total_amount, 2, ',', '.') . ' ' . ($row->currency_code ?? 'TL'),
                    'total_paid' => $totalPaid,
                    'total_paid_formatted' => number_format($totalPaid, 2, ',', '.') . ' ' . ($row->currency_code ?? 'TL'),
                    'remaining_amount' => $remainingAmount,
                    'remaining_amount_formatted' => number_format($remainingAmount, 2, ',', '.') . ' ' . ($row->currency_code ?? 'TL')
                ]);
            })
            ->rawColumns(['payment_status', 'is_active'])
            ->filterColumn('current_name', function($query, $keyword) {
                $query->where('current.name', 'like', "%{$keyword}%");
            });
    }

    /**
     * Hook metodu: form metodundan önce çalışır
     * Yeni kayıt oluşturulurken veya mevcut kayıt düzenlenirken çalışır
     */
    public function formHook($item)
    {
        if (!$item->id) {
            // Yeni kayıt oluşturulurken
            $invoiceNo = $this->generateInvoiceNumber();
            $item->invoice_no = $invoiceNo;
            $item->invoice_date = now();
            $item->due_date = now()->addDays(30);
            $item->status = 0;
            $item->is_active = 1;
            $item->invoice_status = 1; // Yeni kayıt için invoice_status'u 1 olarak ayarla
        } elseif (empty($item->invoice_no)) {
            // Mevcut kayıtta fatura numarası yoksa
            $invoiceNo = $this->generateInvoiceNumber();
            $item->invoice_no = $invoiceNo;
        }

        return $item;
    }

    /**
     * Hook metodu: save metodundan önce çalışır
     * Form verilerini işlemek için kullanılır
     */
    public function saveHook(Request $request)
    {
        $params = $request->all();

        // Fatura numarası kontrolü
        if (empty($request->id)) {
            // Yeni kayıt oluşturulurken numara al
            $invoiceNo = \App\Libraries\Helpers::generateNextSequenceNumber('ALF');
            $params['invoice_no'] = $invoiceNo;
            // Yeni kayıt için invoice_status'u 1 olarak ayarla
            $params['invoice_status'] = 1;
        } elseif (!empty($request->invoice_no)) {
            // Mevcut kayıtta formdan gelen numarayı kullan
            $params['invoice_no'] = $request->invoice_no;
        } else {
             // Mevcut kayıtta numara yoksa (bu durum olmamalı ama tedbir amaçlı)
             $invoiceNo = \App\Libraries\Helpers::generateNextSequenceNumber('ALF');
             $params['invoice_no'] = $invoiceNo;
        }

        // Alış faturaları tablosunda 'document_type_id' sütunu olmadığı için kaldırılıyor.
        unset($params['document_type_id']);

        // foreign_currency_amount alanını kaldır
        unset($params['foreign_currency_amount']);

        // stock_id alanını kaldır (Alış faturaları tablosunda yok)
        unset($params['stock_id']);

        // saveHook'tan çıkan parametreleri logla (Hata ayıklama)
        \Log::info('saveHook - Processed Params after unset:', ['params' => $params]);

        // Dosya yükleme işlemi (Eğer varsa)
        if ($request->hasFile('invoice_file')) {
            $file = $request->file('invoice_file');
            // Dosya adı ve yolu belirleme (İsterseniz Helpers kullanabilirsiniz)
            $path = $file->store('purchase_invoices', 'public'); // Dizin adını değiştirdim
            $params['invoice_file'] = $path;
        }

        // items array'ini JSON'a dönüştür
        if (isset($params['items']) && is_array($params['items'])) {
            $processedItems = [];
            foreach ($params['items'] as $item) {
                // Sayısal değerleri float'a çevir
                $item['quantity'] = (float)($item['quantity'] ?? 0);
                $item['sale_price'] = (float)($item['sale_price'] ?? 0); // sale_price yerine purchase_price kullanılmalıydı, şimdilik böyle bırakalım ama düzeltilmeli.
                $item['purchase_price'] = (float)($item['purchase_price'] ?? ($item['sale_price'] ?? 0)); // purchase_price yoksa sale_price kullan
                $item['vat_rate'] = (float)($item['vat_rate'] ?? 0);
                $item['vat_amount'] = (float)($item['vat_amount'] ?? 0);
                $item['total'] = (float)($item['total'] ?? 0);

                 // product_id 0 ise veya boşsa product_name'e göre bulmaya çalış
                if (empty($item['product_id']) && !empty($item['product_name'])) {
                    $product = \App\Models\Product::where('name', $item['product_name'])->first();
                    if ($product) {
                        $item['product_id'] = $product->id;
                    } else {
                         // Ürün bulunamazsa product_id'yi null veya 0 olarak bırak
                         $item['product_id'] = null; // veya 0
                    }
                }

                $processedItems[] = $item;
            }
            $params['items'] = json_encode($processedItems); // array'i JSON string'e çevir
        } else {
            $params['items'] = '[]'; // Items boşsa boş JSON array olarak kaydet
             $params['stock_id'] = null; // veya 0
        }

        return $params;
    }

    /**
     * Hook metodu: save metodundan sonra çalışır
     * Kayıt işlemi tamamlandıktan sonra yapılacak işlemler
     */
    public function saveBack($obj)
    {
        try {
            // Balance güncellemesi
            $currentId = $obj->current_id;

            if (!$currentId) {
                Log::error("Current ID boş, balance güncellenemiyor!");
                return redirect()->route('backend.purchase_invoices_list')->with('error', 'Cari seçilmediği için balance güncellenemedi!');
            }

            // Önce balance tablosunda bu cari için kayıt var mı kontrol et
            $balanceExists = DB::table('balances')->where('current_id', $currentId)->exists();

            if ($balanceExists) {
                // Mevcut kaydı güncelle
                DB::statement(
                    "UPDATE balances SET credit_balance = credit_balance + ?, updated_at = ? WHERE current_id = ?",
                    [floatval($obj->total_amount), now(), $currentId]
                );
            } else {
                // Yeni kayıt oluştur
                DB::table('balances')->insert([
                    'current_id' => $currentId,
                    'debit_balance' => 0,
                    'credit_balance' => floatval($obj->total_amount),
                    'is_active' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Balance güncellenirken hata: " . $e->getMessage());
            return redirect()->route('backend.purchase_invoices_list')->with('error', 'Balance güncellenirken bir hata oluştu: ' . $e->getMessage());
        }

        return redirect()->route('backend.purchase_invoices_list')->with('success', 'Alış faturası başarıyla kaydedildi.');
    }

    /**
     * Hook metodu: delete metodundan sonra çalışır
     * Silme işlemi tamamlandıktan sonra yapılacak işlemler
     */
    public function deleteBack($obj)
    {
        try {
            $currentId = $obj->current_id;

            if (!$currentId) {
                Log::error("Current ID boş, balance güncellenemiyor!");
                return response()->json([
                    'status' => false,
                    'message' => 'Cari bilgisi bulunamadığı için balance güncellenemedi!'
                ]);
            }

            // Önce balance tablosunda bu cari için kayıt var mı kontrol et
            $balanceExists = DB::table('balances')->where('current_id', $currentId)->exists();

            if ($balanceExists) {
                // Alış faturası siliniyor -> Alacak (credit_balance) azalt
                $totalAmount = floatval($obj->total_amount);

                // Direkt SQL ile güncelleme yap
                DB::statement(
                    "UPDATE balances SET credit_balance = GREATEST(0, credit_balance - ?), updated_at = ? WHERE current_id = ?",
                    [$totalAmount, now(), $currentId]
                );
            }

            return response()->json(['status' => true, 'message' => 'Fatura başarıyla silindi.']);
        } catch (\Exception $e) {
            Log::error("Balance güncellenirken hata: " . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Balance güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Fatura numarası oluşturma metodu
     *
     * @param string|null $month Ay (01-12 formatında), null ise mevcut ay kullanılır
     * @param string|null $year Yıl (YYYY formatında), null ise mevcut yıl kullanılır
     * @return string
     */

    /**
     * Faturayı PDF olarak oluştur
     *
     * @param int $id Fatura ID
     * @return \Illuminate\Http\Response
     */
    public function generatePdf($id)
    {
        try {
            // Fatura bilgilerini getir
            $invoice = PurchaseInvoice::with(['current'])->find($id);

            if (!$invoice) {
                \Illuminate\Support\Facades\Log::error("Fatura bulunamadı: ID = " . $id);

                // Örnek veri oluştur
                $data = [
                    'invoice_no' => 'ÖRNEK-' . $id,
                    'invoice_date' => date('d.m.Y'),
                    'current_name' => 'Örnek Tedarikçi',
                    'currency_code' => 'TL',
                    'net_amount' => '0,00 TL',
                    'tax_amount' => '0,00 TL',
                    'total_amount' => '0,00 TL',
                    'description' => 'Bu bir örnek faturadır. Gerçek fatura kaydı bulunamadı (ID: ' . $id . ').',
                    'items' => [
                        [
                            'product_name' => 'Örnek Ürün',
                            'quantity' => '1,00',
                            'purchase_price' => '0,00 TL',
                            'sale_price' => '0,00 TL', // Uyumluluk için sale_price'ı da ekle
                            'vat_rate' => 18,
                            'vat_amount' => '0,00 TL',
                            'total' => '0,00 TL'
                        ]
                    ]
                ];

                return PdfExport::generateInvoicePdf($data, 'Örnek Alış Faturası', 'Ornek_Alis_Faturasi_' . $id);
            }

            // Fatura verilerini logla
            \Illuminate\Support\Facades\Log::info("Alış faturası verileri: ", [
                'id' => $invoice->id,
                'invoice_no' => $invoice->invoice_no,
                'invoice_date' => $invoice->invoice_date,
                'current_id' => $invoice->current_id,
                'current_name' => $invoice->current ? $invoice->current->name : 'Cari bulunamadı',
                'items' => $invoice->items,
                'currency_code' => $invoice->currency_code,
                'net_amount' => $invoice->net_amount,
                'tax_amount' => $invoice->tax_amount,
                'total_amount' => $invoice->total_amount
            ]);

            // Fatura verilerini hazırla
            $data = [
                'invoice_no' => $invoice->invoice_no ?? 'Fatura No Yok',
                'invoice_date' => $invoice->invoice_date ? $invoice->invoice_date->format('d.m.Y') : date('d.m.Y'),
                'current_name' => $invoice->current ? $invoice->current->name : 'Cari Hesap Bulunamadı',
                'currency_code' => $invoice->currency_code ?? ($invoice->currency ?? 'TL'),
                'net_amount' => number_format($invoice->net_amount ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                'tax_amount' => number_format($invoice->tax_amount ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                'total_amount' => number_format($invoice->total_amount ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                'description' => $invoice->description ?? 'Açıklama yok',
                'items' => []
            ];

            // Fatura kalemlerini ekle
            if (isset($invoice->items) && is_array($invoice->items) && count($invoice->items) > 0) {
                // Ürün ID'lerini topla
                $productIds = [];
                foreach ($invoice->items as $item) {
                    if (isset($item['product_id']) && !empty($item['product_id'])) {
                        $productIds[] = $item['product_id'];
                    }
                }

                // Ürün bilgilerini getir
                $products = [];
                if (!empty($productIds)) {
                    $productsData = Product::whereIn('id', $productIds)->get();
                    foreach ($productsData as $product) {
                        $products[$product->id] = $product;
                    }
                }

                foreach ($invoice->items as $item) {
                    $productId = $item['product_id'] ?? null;
                    $product = $productId && isset($products[$productId]) ? $products[$productId] : null;

                    $data['items'][] = [
                        'product_name' => $product ? $product->name : ($item['product_name'] ?? 'Bilinmeyen Ürün'),
                        'quantity' => number_format($item['quantity'] ?? 0, 2, ',', '.'),
                        'purchase_price' => number_format($item['purchase_price'] ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                        'sale_price' => number_format($item['purchase_price'] ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                        'vat_rate' => $item['vat_rate'] ?? ($product ? $product->default_vat : 0),
                        'vat_amount' => number_format($item['vat_amount'] ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                        'total' => number_format($item['total'] ?? 0, 2, ',', '.') . ' ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL'))
                    ];
                }
            } else {
                // Fatura kalemi yoksa örnek bir kalem ekle
                $data['items'][] = [
                    'product_name' => 'Fatura kalemi bulunamadı',
                    'quantity' => '0,00',
                    'purchase_price' => '0,00 ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                    'sale_price' => '0,00 ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                    'vat_rate' => 0,
                    'vat_amount' => '0,00 ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL')),
                    'total' => '0,00 ' . ($invoice->currency_code ?? ($invoice->currency ?? 'TL'))
                ];
            }

            $title = 'Alış Faturası';
            $filename = 'Alis_Faturasi_' . ($invoice->invoice_no ?? $id);

            return PdfExport::generateInvoicePdf($data, $title, $filename);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("PDF oluşturulurken hata: " . $e->getMessage());

            // Hata durumunda örnek veri oluştur
            $data = [
                'invoice_no' => 'HATA-' . $id,
                'invoice_date' => date('d.m.Y'),
                'current_name' => 'Hata Oluştu',
                'currency_code' => 'TL',
                'net_amount' => '0,00 TL',
                'tax_amount' => '0,00 TL',
                'total_amount' => '0,00 TL',
                'description' => 'Fatura oluşturulurken bir hata oluştu: ' . $e->getMessage(),
                'items' => [
                    [
                        'product_name' => 'Hata',
                        'quantity' => '0,00',
                        'purchase_price' => '0,00 TL',
                        'sale_price' => '0,00 TL',
                        'vat_rate' => 0,
                        'vat_amount' => '0,00 TL',
                        'total' => '0,00 TL'
                    ]
                ]
            ];

            return PdfExport::generateInvoicePdf($data, 'Hata Raporu', 'Hata_Raporu_' . $id);
        }
    }

    /**
     * Alış faturalarını Excel olarak dışa aktar
     *
     * @return \Illuminate\Http\Response
     */
    private function exportPurchaseInvoicesToExcel()
    {
        try {
            // Tüm alış faturalarını getir
            $invoices = PurchaseInvoice::with(['current'])->get();

            // Excel için veri hazırla
            $data = [];
            $headers = ['Fatura No', 'Cari Hesap', 'Tarih', 'Net Tutar', 'KDV Tutarı', 'Toplam Tutar', 'Para Birimi', 'Durum'];

            foreach ($invoices as $invoice) {
                $data[] = [
                    'invoice_no' => $invoice->invoice_no,
                    'current_name' => $invoice->current ? $invoice->current->name : '',
                    'invoice_date' => $invoice->invoice_date ? $invoice->invoice_date->format('d.m.Y') : '',
                    'net_amount' => number_format($invoice->net_amount, 2, ',', '.'),
                    'tax_amount' => number_format($invoice->tax_amount, 2, ',', '.'),
                    'total_amount' => number_format($invoice->total_amount, 2, ',', '.'),
                    'currency' => $invoice->currency_code ?? 'TL',
                    'is_active' => $invoice->is_active ? 'Aktif' : 'Pasif'
                ];
            }

            // Excel dosyasını oluştur ve indir
            return ExcelExport::exportToExcel(
                array_map(function($item) {
                    return [
                        $item['invoice_no'],
                        $item['current_name'],
                        $item['invoice_date'],
                        $item['net_amount'],
                        $item['tax_amount'],
                        $item['total_amount'],
                        $item['currency'],
                        $item['is_active']
                    ];
                }, $data),
                $headers,
                'Alis_Faturalari',
                'Alış Faturaları'
            );
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error("Excel oluşturulurken hata: " . $e->getMessage());

            // Boş bir Excel dosyası oluştur
            $data = [
                [
                    'HATA: Excel oluşturulurken bir sorun oluştu',
                    $e->getMessage(),
                    date('d.m.Y H:i:s'),
                    '',
                    '',
                    '',
                    '',
                    ''
                ]
            ];

            return ExcelExport::exportToExcel(
                $data,
                $headers,
                'Alis_Faturalari_Hata',
                'Alış Faturaları (Hata)'
            );
        }
    }

    /**
     * Toplu fatura durumu güncelleme işlemi
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function purchase_invoices_bulk_approve(Request $request)
    {
        try {
            $ids = $request->ids;
            $status = $request->status;

            if (empty($ids) || !is_array($ids)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Geçersiz fatura ID\'leri'
                ]);
            }

            if (!in_array($status, [0, 1, 4])) { // 0: Beklemede, 1: Onaylandı, 4: İptal
                return response()->json([
                    'success' => false,
                    'message' => 'Geçersiz durum değeri'
                ]);
            }

            // Faturaları getir
            $invoices = PurchaseInvoice::whereIn('id', $ids)->get();

            if ($invoices->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Güncellenecek fatura bulunamadı'
                ]);
            }

            // Faturaların durumunu güncelle
            foreach ($invoices as $invoice) {
                $oldStatus = $invoice->status;

                // Muhasebelendi veya kısmi muhasebelendi durumundaki faturaların durumu değiştirilemez
                if ($oldStatus == 2 || $oldStatus == 3) {
                    continue;
                }

                $invoice->status = $status;
                $invoice->save();
            }

            $statusMessage = '';
            switch ($status) {
                case 0:
                    $statusMessage = 'beklemeye alındı';
                    break;
                case 1:
                    $statusMessage = 'onaylandı';
                    break;
                case 4:
                    $statusMessage = 'iptal edildi';
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => count($invoices) . ' fatura başarıyla  oluşturuldu' . $statusMessage
            ]);
        } catch (\Exception $e) {
            Log::error("Alış faturaları güncellenirken hata: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Alış faturaları güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    public function save(Request $request, $unique = null)
    {
        try {
            // Log incoming request data
            Log::info('Purchase Invoice Save Request:', [
                'request_data' => $request->all(),
                'unique' => $unique
            ]);

            // Form verilerini işle
            try {
                $params = $this->saveHook($request);
                Log::info('Processed Parameters:', ['params' => $params]);
            } catch (\Exception $e) {
                Log::error('Error in saveHook:', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw $e;
            }

            // Kaydet
            try {
                $obj = $this->model->updateOrCreate(['id' => $unique], $params);
                Log::info('Invoice Saved:', ['invoice' => $obj->toArray()]);
            } catch (\Exception $e) {
                Log::error('Error saving invoice:', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'params' => $params
                ]);
                throw $e;
            }

            // Kayıt sonrası işlemler
            try {
                $this->saveBack($obj);
            } catch (\Exception $e) {
                Log::error('Error in saveBack:', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'invoice' => $obj->toArray()
                ]);
                throw $e;
            }

            // Başarılı yanıt döndür
            return response()->json([
                'success' => true,
                'message' => 'Fatura başarıyla kaydedildi.',
                'redirect' => route('backend.purchase_invoices_list')
            ]);
        } catch (\Exception $e) {
            Log::error('Fatura kaydedilirken hata:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Fatura kaydedilirken bir hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Ajax isteği ile bir sonraki alış faturası numarasını döndürür.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNextInvoiceNumber()
    {
        try {
            $nextNumber = \App\Libraries\Helpers::generateNextSequenceNumber('ALF');
            if ($nextNumber) {
                return response()->json(['success' => true, 'invoice_number' => $nextNumber]);
            } else {
                return response()->json(['success' => false, 'message' => 'Fatura numarası oluşturulamadı.'], 500);
            }
        } catch (\Exception $e) {
            \Log::error('Alış Faturası Numarası Oluşturma Hatası: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Fatura numarası oluşturulurken bir hata oluştu.'], 500);
        }
    }
}
