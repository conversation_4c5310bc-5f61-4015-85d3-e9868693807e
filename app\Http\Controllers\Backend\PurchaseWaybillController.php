<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Http\Controllers\Backend\BasePattern;
use App\Http\Requests\PurchaseWaybillRequest;
use App\Models\Current;
use App\Models\PurchaseInvoice;
use App\Models\PurchaseWaybill;
use App\Models\PurchaseWaybillItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class PurchaseWaybillController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Alış İrsaliyeleri';
        $this->page = 'purchase_waybills';
        $this->model = PurchaseWaybill::class;
        $this->validation = [PurchaseWaybillRequest::class, []];
        $this->relation = ['current'];
        $this->listQuery = PurchaseWaybill::with(['current']);

        parent::__construct();
    }

    /**
     * Hook metodu: Datatable verilerini özelleştirmek için
     */
    public function datatableHook($obj)
    {
        return $obj
            ->addColumn('current_name', function ($row) {
                return $row->current ? $row->current->name : '';
            })
            ->editColumn('waybill_date', function ($row) {
                return $row->waybill_date ? $row->waybill_date->format('d.m.Y') : '';
            })
            ->addColumn('status_badge', function ($row) {
                $statusColors = [
                    'pending' => 'warning',
                    'approved' => 'success',
                    'cancelled' => 'danger'
                ];
                $statusTexts = [
                    'pending' => 'Beklemede',
                    'approved' => 'Onaylandı',
                    'cancelled' => 'İptal Edildi'
                ];
                $color = $statusColors[$row->status] ?? 'secondary';
                $text = $statusTexts[$row->status] ?? 'Bilinmeyen';
                return '<span class="badge bg-' . $color . '">' . $text . '</span>';
            })
            ->addColumn('total_amount_formatted', function ($row) {
                return number_format($row->total_amount, 2, ',', '.') . ' ₺';
            })
            ->addColumn('actions', function ($row) {
                $editUrl = route('backend.purchase_waybills_form', $row->id);
                $pdfUrl = route('backend.purchase_waybills_pdf', $row->id);
                $deleteBtn = '<button type="button" class="remove-item-btn bg-danger-focus text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="' . $row->id . '">
                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                </button>';

                return '<div class="d-flex align-items-center gap-10 justify-content-center">
                    <a href="' . $editUrl . '" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                    </a>
                    <a href="' . $pdfUrl . '" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                        <iconify-icon icon="mdi:file-pdf" class="menu-icon"></iconify-icon>
                    </a>
                    ' . $deleteBtn . '
                </div>';
            })
            ->rawColumns(['status_badge', 'actions']);
    }

    /**
     * Hook metodu: save metodundan önce çalışır
     */
    public function saveHook(Request $request)
    {
        $params = $request->all();

        // Varsayılan değerler
        $params['waybill_type'] = 2; // Alış İrsaliyesi
        $params['created_by'] = Auth::id();

        // İrsaliye no otomatik oluştur (sadece yeni kayıt için)
        // Eğer id yoksa (yani yeni kayıt), numarayı oluştur ve counter'ı güncelle
        if (empty($request->id)) {
            $month = date('m');
            $year = date('Y');
            $prefix = 'AİR';

            // Counter tablosunu kullan ve lock ile güvence altına al
            DB::beginTransaction();
            try {
                $counter = DB::table('invoice_counters')
                    ->where('prefix', $prefix)
                    ->where('month', $month)
                    ->where('year', $year)
                    ->lockForUpdate()
                    ->first();

                if ($counter) {
                    $nextNumber = $counter->last_number + 1;
                    DB::table('invoice_counters')
                        ->where('id', $counter->id)
                        ->update(['last_number' => $nextNumber]);
                } else {
                    $nextNumber = 1;
                    DB::table('invoice_counters')->insert([
                        'prefix' => $prefix,
                        'month' => $month,
                        'year' => $year,
                        'last_number' => $nextNumber
                    ]);
                }
                DB::commit();

                $params['waybill_no'] = sprintf('%s/%s/%s/%07d', $prefix, $month, $year, $nextNumber);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error('Alış İrsaliyesi numara üretme/güncelleme hatası: ' . $e->getMessage());
                // Hata durumunda boş numara döndürebilir veya bir hata mesajı set edilebilir
                $params['waybill_no'] = ''; // veya bir hata kodu/mesajı
            }
        } elseif (empty($params['waybill_no'])) {
             // Düzenleme yapılıyorsa ama irsaliye no boş gelmişse, mevcut numarayı koru veya yeniden oluşturma
             // Şu anki durumda boş gelmesi beklenmez ama defensive programming
             // Bu kısım isteğe bağlı, mevcut numara inputu readonly olduğu için buraya düşmemeli normalde.
        }

        return $params;
    }

    /**
     * Otomatik irsaliye numarası üretici fonksiyon (sadece göstermek için)
     */
    private function getNextWaybillNoForDisplay()
    {
        $month = date('m');
        $year = date('Y');
        $prefix = 'AİR';

        $counter = DB::table('invoice_counters')
            ->where('prefix', $prefix)
            ->where('month', $month)
            ->where('year', $year)
            ->first();

        if ($counter) {
            $nextNumber = $counter->last_number + 1;
        } else {
            $nextNumber = 1;
        }

        return sprintf('%s/%s/%s/%07d', $prefix, $month, $year, $nextNumber);
    }

    public function form(Request $request, $unique = null)
    {
        $item = null;
        if ($unique) {
            $item = PurchaseWaybill::with('items')->findOrFail($unique);
        }

        $currents = Current::where('is_active', 1)->orderBy('name')->get();

        // Faturaları getir
        $invoices = PurchaseInvoice::where('is_active', 1)
            ->where('status', 'approved')
            ->orderBy('invoice_date', 'desc')
            ->get();

        // Otomatik irsaliye numarası oluştur (sadece yeni kayıt formu için gösterim amaçlı)
        $autoWaybillNo = '';
        if (!$item) {
            $autoWaybillNo = $this->getNextWaybillNoForDisplay();
        }

        return view('backend.purchase_waybills.form', compact('item', 'currents', 'invoices', 'autoWaybillNo'));
    }

    /**
     * İrsaliye numarası oluştur
     */
    private function generateWaybillNo()
    {
        $year = date('Y');
        $month = date('m');

        // Bu ay için son irsaliye numarasını bul
        $lastWaybill = PurchaseWaybill::where('waybill_no', 'like', "AİR/{$month}/{$year}/%")
            ->orderBy('waybill_no', 'desc')
            ->first();

        if ($lastWaybill) {
            // Son numarayı al ve 1 artır
            $lastNumber = (int) substr($lastWaybill->waybill_no, -7);
            $newNumber = $lastNumber + 1;
        } else {
            // İlk irsaliye
            $newNumber = 1;
        }

        return sprintf('AİR/%s/%s/%07d', $month, $year, $newNumber);
    }

    /**
     * Hook metodu: save metodundan sonra çalışır
     */
    public function saveBack($obj)
    {
        try {
            DB::beginTransaction();

            // Mevcut kalemleri sil
            if ($obj->id) {
                PurchaseWaybillItem::where('waybill_id', $obj->id)->delete();
            }

            // İrsaliye kalemlerini kaydet
            if (request()->has('items') && is_array(request()->items)) {
                foreach (request()->items as $item) {
                    $waybillItem = new PurchaseWaybillItem();
                    $waybillItem->waybill_id = $obj->id;
                    $waybillItem->product_id = $item['product_id'];
                    $waybillItem->product_name = $item['product_name'];
                    $waybillItem->quantity = $item['quantity'];
                    $waybillItem->price = $item['price'] ?? 0;
                    $waybillItem->vat_rate = $item['vat_rate'] ?? 0;
                    $waybillItem->vat_amount = $item['vat_amount'] ?? 0;
                    $waybillItem->total = $item['total'] ?? 0;
                    $waybillItem->save();
                }
            }

            DB::commit();

            return redirect()->route('backend.purchase_waybills_list')->with('success', 'İrsaliye başarıyla kaydedildi.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('İrsaliye kaydedilirken hata oluştu: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'İrsaliye kaydedilirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * Hook metodu: delete metodundan sonra çalışır
     */
    public function deleteBack($obj)
    {
        try {
            DB::beginTransaction();

            // İrsaliye kalemlerini sil
            PurchaseWaybillItem::where('waybill_id', $obj->id)->delete();

            DB::commit();

            return response()->json(['success' => true, 'status' => true, 'message' => 'İrsaliye başarıyla silindi.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('İrsaliye silinirken hata oluştu: ' . $e->getMessage());
            return response()->json(['success' => false, 'status' => false, 'message' => 'İrsaliye silinirken bir hata oluştu: ' . $e->getMessage()]);
        }
    }

    /**
     * Test form
     */
    public function testForm()
    {
        $currents = Current::all();
        return view('backend.purchase_waybills.form_test', compact('currents'));
    }

    /**
     * Basit test
     */
    public function simpleTest()
    {
        $currents = Current::all();
        return view('test_simple', compact('currents'));
    }

    /**
     * Alış faturalarını getir
     */
    private function getPurchaseInvoices()
    {
        return PurchaseInvoice::with('current')
            ->orderBy('id', 'desc')
            ->limit(50)
            ->get();
    }

    /**
     * PDF oluştur
     */
    public function generatePdf($id)
    {
        try {
            $waybill = PurchaseWaybill::with(['current', 'items'])->find($id);

            if (!$waybill) {
                return response()->json(['error' => 'İrsaliye bulunamadı'], 404);
            }

            $data = [
                'waybill' => $waybill,
                'current' => $waybill->current,
                'items' => $waybill->items,
                'company' => [
                    'name' => 'Şirket Adı',
                    'address' => 'Şirket Adresi',
                    'phone' => 'Telefon',
                    'email' => 'Email'
                ]
            ];

            $pdf = Pdf::loadView('backend.purchase_waybills.pdf', $data);
            return $pdf->stream('irsaliye-' . $waybill->waybill_no . '.pdf');

        } catch (\Exception $e) {
            Log::error('PDF oluşturulurken hata: ' . $e->getMessage());
            return response()->json(['error' => 'PDF oluşturulurken bir hata oluştu'], 500);
        }
    }
}
