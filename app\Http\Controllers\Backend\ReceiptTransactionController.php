<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Backend\BaseController;
use App\Models\Current;
use App\Models\Invoice;
use App\Models\PurchaseInvoice;
use App\Models\PaymentTransaction;
use App\Models\PaymentType;
use App\Models\Balance;
use Illuminate\Http\Request;

class ReceiptTransactionController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Tahsilat İşlemleri';
        $this->page = 'receipt_transaction';
        $this->model = new PaymentTransaction();
        $this->listQuery = PaymentTransaction::filter(request())->where('transaction_type', 1);
        $this->relation = ['current', 'paymentType'];
        $this->view = (object)[
            'breadcrumb' => [
                'Tahsilat İşlemleri' => route('backend.receipt_transaction_list'),
            ],
        ];

        view()->share('currents', Current::get());
        view()->share('salesInvoices',Invoice::active()->whereIn('status', [0, 1, 2])->get());
        view()->share('paymentMethods',PaymentType::active()->pluck('name', 'id')->toArray());
        view()->share('invoiceTypes', [1 => 'Satış Faturası',2 => 'Alış Faturası']);

        parent::__construct();
    }

    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('transaction_date', function ($row) {
                return $row->transaction_date ? $row->transaction_date->format('d.m.Y H:i') : '';
            })
            ->addColumn('invoice_no', function ($row) {
                if ($row->invoice_type == 1) { // Satış Faturası
                    $invoice = Invoice::find($row->invoice_id);
                    return $invoice ? $invoice->invoice_no : '';
                } else { // Alış Faturası
                    $invoice = PurchaseInvoice::find($row->invoice_id);
                    return $invoice ? $invoice->invoice_no : '';
                }
            })
            ->addColumn('amount_formatted', function ($row) {
                return number_format($row->amount, 2, ',', '.') . ' ₺';
            });
    }

    public function saveHook(Request $request)
    {
        $params = $request->all();
        if ($request->invoice_type == 1) { // Satış Faturası
            $invoice = Invoice::find($request->invoice_id);
        } else {
            return redirect()->back()->with('error', 'Geçersiz fatura tipi. Tahsilat işlemleri sadece satış faturaları için yapılabilir.');
        }

        if (!$invoice) {
            return redirect()->back()->with('error', 'Fatura bulunamadı.');
        }

        $remainingAmount = $invoice->remaining_amount;
        if ($request->amount > $remainingAmount) {
            return redirect()->back()->with('error', 'Tahsilat tutarı, faturanın kalan tutarından büyük olamaz. Kalan tutar: ' . number_format($remainingAmount, 2, ',', '.') . ' ' . $invoice->currency);
        }

        $params['transaction_type'] = 1; // Tahsilat

        $params['is_active'] = $request->is_active ?? true;

        return $params;
    }

    public function saveBack($obj)
    {
        $this->updateInvoiceStatus($obj->invoice_id, $obj->invoice_type);
        $this->addToCurrentFinancialMovements($obj);
        return redirect()->route('backend.receipt_transactions_list')->with('success', 'Tahsilat işlemi başarıyla kaydedildi.');
    }

    public function deleteBack($obj)
    {
        try {
            $this->removeFromCurrentFinancialMovements($obj);
            $this->updateInvoiceStatus($obj->invoice_id, $obj->invoice_type);
            return response()->json(['success' => true, 'status' => true, 'message' => 'İşlem başarıyla silindi.']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'status' => false, 'message' => 'İşlem silinirken bir hata oluştu: ' . $e->getMessage()]);
        }
    }

    private function removeFromCurrentFinancialMovements($transaction)
    {
        try {
            $balance = Balance::where('current_id', $transaction->current_id)->first();
            if ($balance) {
                $balance->debit_balance += $transaction->amount;
                $balance->save();
            }
        } catch (\Exception $e) {
           
        }
    }

    private function updateInvoiceStatus($invoiceId, $invoiceType)
    {
        try {
            if ($invoiceType == 1) { // Satış Faturası
                $invoice = Invoice::find($invoiceId);
            } else { // Alış Faturası
                $invoice = PurchaseInvoice::find($invoiceId);
            }

            $totalPaid = $invoice->total_paid;
            $remainingAmount = $invoice->total_amount - $totalPaid;
            if ($remainingAmount <= 0) {
                $invoice->status = 3; // Muhasebelendi
            } else if ($totalPaid > 0) {
                $invoice->status = 2; // Kısmi Muhasebelendi
            } else {
                $invoice->status = 1; // Onaylandı
            }

            $invoice->save();
        } catch (\Exception $e) {
        }
    }

    private function addToCurrentFinancialMovements($transaction)
    {
        try {
            $balance = Balance::where('current_id', $transaction->current_id)->first();

            if ($balance) {
                $balance->debit_balance -= $transaction->amount;
                $balance->save();
            } else {
                Balance::create([
                    'current_id' => $transaction->current_id,
                    'debit_balance' => -$transaction->amount,
                    'credit_balance' => 0
                ]);
            }

        } catch (\Exception $e) {
        }
    }

    public function getInvoiceDetails(Request $request)
    {
        $invoiceId = $request->invoice_id;
        $invoiceType = $request->invoice_type;

        try {
            if ($invoiceType == 1) { // Satış Faturası
                $invoice = Invoice::with('current')->find($invoiceId);
            } else { // Alış Faturası
                $invoice = PurchaseInvoice::with('current')->find($invoiceId);
            }

            if (!$invoice) {
                return response()->json(['success' => false, 'error' => 'Fatura bulunamadı.']);
            }
            $payments = PaymentTransaction::where('invoice_id', $invoiceId)
                ->where('invoice_type', $invoiceType)
                ->where('is_active', 1)
                ->get();

            $totalPaid = $payments->sum('amount');
            $remainingAmount = $invoice->total_amount - $totalPaid;

            return response()->json([
                'success' => true,
                'invoice' => [
                    'id' => $invoice->id,
                    'invoice_no' => $invoice->invoice_no,
                    'invoice_date' => $invoice->invoice_date ? $invoice->invoice_date->format('d.m.Y') : '',
                    'current_id' => $invoice->current_id,
                    'current_name' => $invoice->current ? $invoice->current->name : '',
                    'total_amount' => $invoice->total_amount,
                    'total_amount_formatted' => number_format($invoice->total_amount, 2, ',', '.') . ' ' . $invoice->currency,
                    'total_paid' => $totalPaid,
                    'total_paid_formatted' => number_format($totalPaid, 2, ',', '.') . ' ' . $invoice->currency,
                    'remaining_amount' => $remainingAmount,
                    'remaining_amount_formatted' => number_format($remainingAmount, 2, ',', '.') . ' ' . $invoice->currency,
                    'currency' => $invoice->currency,
                    'status' => $invoice->status,
                    'status_name' => $invoice->status_name
                ],
                'payments' => $payments->map(function($payment) {
                    return [
                        'id' => $payment->id,
                        'transaction_date' => $payment->transaction_date ? $payment->transaction_date->format('d.m.Y') : '',
                        'payment_method' => $payment->payment_method,
                        'payment_method_name' => $payment->payment_method_name,
                        'amount' => $payment->amount,
                        'amount_formatted' => number_format($payment->amount, 2, ',', '.') . ' ' . $payment->currency,
                        'description' => $payment->description
                    ];
                })
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'error' => 'Fatura detayları getirilirken bir hata oluştu.']);
        }
    }
}
