<?php

namespace App\Http\Controllers\Backend;

use App\Exports\ExportOffer;
use App\Http\Requests\Backend\OfferRequest;
use App\Libraries\Helpers;
use App\Models\Offer;
use App\Models\Current;
use App\Models\ExchangeRate;
use App\Models\OfferProduct;
use App\Models\OfferType;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\PaymentType;
use App\Models\Stock;
use App\Models\Warehouse;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReceivedOfferController extends BaseController
{
    use BasePattern;

    private const RECEIVED_OFFER_TYPE_ID = 1;
    private const GIVEN_ORDER_TYPE = 2;
    private const APPROVED_STATUS = 1;
    private const SAMPLE_ITEM_COUNT = 20;
    private const DEFAULT_CURRENCY = 'TRY';
    private const DEFAULT_CURRENCY_SYMBOL = '₺';

    public function __construct()
    {
        $this->title = 'Alınan Teklif';
        $this->page = 'offer_received';
        $this->model = new Offer();
        $this->relation = [
            'warehouse',
            'exchangeRate',
            'current' => function ($query) {
                $query->withTrashed();
            },
            'offerType',
            'paymentType',
            'offerProducts',
        ];
        $this->listQuery = Offer::filter(request())->where('offer_type_id', self::RECEIVED_OFFER_TYPE_ID);
        $this->view = (object)array(
            'breadcrumb' => array(
                'Alınan Teklif' => route('backend.offer_received_list'),
            ),
        );
        view()->share('offerProducts', OfferProduct::get());
        // view()->share('branches', Branch::get());
        view()->share('warehouses', Warehouse::get());
        view()->share('exchangeRate', ExchangeRate::where('is_active', 1)->get());
        view()->share('current', Current::with(['country', 'city', 'district'])->where('is_active', 1)->get());
        view()->share('paymentType', PaymentType::get());
        view()->share('offerType', OfferType::get());
        view()->share('stocks', Stock::with(['product', 'variant', 'stockReservations'])->where('is_active', 1)->get());
        parent::__construct();
    }
    private function handleResponse(Request $request, bool $success, string $message, array $errors = [], ?string $redirect = null): mixed
    {
        if ($request->ajax()) {
            $response = [
                'success' => $success,
                'message' => $message
            ];

            if (!empty($errors)) {
                $response['errors'] = $errors;
            }

            if ($success && $redirect) {
                $response['redirect'] = $redirect;
            }

            return response()->json($response, $success ? 200 : 422);
        }

        if ($success) {
            $redirectTo = $redirect ?? route("backend.{$this->page}_list");
            return redirect($redirectTo)->with('success', $message);
        } else {
            $redirect = redirect()->back()->with('error', $message);
            if (!empty($errors)) {
                $redirect->withErrors($errors);
            }
            return $redirect->withInput();
        }
    }
    public function save(Request $request, $unique = null)
    {
        try {
            $validator = Validator::make($request->all(), (new OfferRequest())->rules(), (new OfferRequest())->messages());
            if ($validator->fails()) {
                return $this->handleResponse($request, false, 'Girilen bilgilerde hata var, lütfen kontrol ediniz.', $validator->errors()->toArray());
            }
            $params = $request->except(['products']);
            $products = $request->has('products')
                ? (is_string($request->input('products'))
                    ? json_decode($request->input('products'), true) ?? []
                    : $request->input('products'))
                : [];
            if (!empty($products)) {
                $params['total_price'] = Helpers::parseTrNumber($request->input('total_price', 0));
                $params['vat_amount'] = Helpers::parseTrNumber($request->input('vat_amount', 0));
                $params['total_amount'] = Helpers::parseTrNumber($request->input('total_amount', 0));
            }
            if (!is_null($unique)) {
                $offer = Offer::find($unique);
                $offer->update($params);
            } else {
                $offer = Offer::create($params);
            }
            if ($offer && $offer->id) {
                OfferProduct::where('offer_id', $offer->id)->delete();
                if (!empty($products)) {
                    $offer->processOfferProducts($products);
                }
            }
            Cache::flush();
            return $this->handleResponse($request, true, 'Teklif başarılı şekilde kaydedildi', [], route("backend." . $this->page . "_list"));
        } catch (\Exception $e) {
            return $this->handleResponse($request, false, 'Teklif kaydedilirken bir hata oluştu: ' . $e->getMessage(), [], null);
        }
    }
    public function detail(Request $request, $unique = null)
    {
        try {
            $offer = Offer::with([
                // 'branch',
                'warehouse',
                'exchangeRate',
                'current',
                'paymentType',
                'offerType',
                'offerProducts.stock'
            ])->findOrFail($unique ?? $request->input('id'));
            $breadcrumb = [
                'Ayarlar' => '#',
                'Alınan Teklif' => route('backend.offer_received_list'),
                'Alınan Teklif Detay' => route('backend.offer_received_detail',  ['unique' => $unique])
            ];
            view()->share('breadcrumb', $breadcrumb);
            return view('backend.offer_received.detail', [
                'container' => (object)[
                    'page' => $this->page,
                    'title' => 'Alınan Teklif'
                ],
                'title' => 'Alınan Teklif Detay',
                'subTitle' => 'Alınan Teklif Detay',
                'item' => $offer
            ]);
        } catch (\Exception $e) {
            return redirect()->route('backend.offer_received_list')->with('error', 'Teklif detayı görüntülenirken bir hata oluştu: ' . $e->getMessage());
        }
    }
    public function form(Request $request, $unique = NULL)
    {
        if (!is_null($unique)) {
            $item = $this->model::find((int)$unique);
            if (is_null($item))
                return redirect()->back()->with('error', 'Kayıt bulunamadı');
            if ($item && $item->id) {
                $item->offerProducts = OfferProduct::where('offer_id', $item->id)
                    ->where('is_active', 1)
                    ->get();
                foreach ($item->offerProducts as $product) {
                    if (empty($product->item_no)) {
                        $product->item_no = OfferProduct::generateItemNo($item->offer_number);
                        $product->save();
                    }
                }
                if ($item->branch_id) {
                    view()->share('branchWarehouses', Warehouse::where('branch_id', $item->branch_id)->get());
                }
                if ($item->current_id) {
                    $currentList = Current::where('is_active', 1)->get();
                    $deletedCurrent = Current::withTrashed()->where('id', $item->current_id)->first();

                    if ($deletedCurrent && $deletedCurrent->deleted_at) {
                        $currentWithDeleted = $currentList->toBase();
                        $currentWithDeleted->push($deletedCurrent);
                        view()->share('current', $currentWithDeleted);
                    }
                }
            }
        } else {
            $item = new $this->model;
            $item->offer_number = Offer::generateOfferNumber(self::RECEIVED_OFFER_TYPE_ID);
            $sampleItemNos = [];
            for ($i = 1; $i <= self::SAMPLE_ITEM_COUNT; $i++) { // 20 örnek item_no oluştur
                $sampleItemNos[] = $item->offer_number . '-' . str_pad($i, 2, '0', STR_PAD_LEFT);
            }
            view()->share('sampleItemNos', $sampleItemNos);
        }
        return view("backend.$this->page.form", compact('item'));
    }
    protected function datatableHook($datatable)
    {
        return $datatable
            ->addColumn('currency_type', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->currency_code;
                }
                return self::DEFAULT_CURRENCY;
            })
            ->addColumn('currency_symbol', function ($item) {
                if ($item->exchangeRate) {
                    return $item->exchangeRate->symbol;
                }
                return self::DEFAULT_CURRENCY_SYMBOL;
            })
            ->addColumn('product_statuses', function ($item) {
                $statuses = [];
                if ($item->offerProducts && $item->offerProducts->count() > 0) {
                    foreach ($item->offerProducts as $product) {
                        $statuses[] = [
                            'status' => $product->status,
                            'quantity' => $product->quantity
                        ];
                    }
                }
                return $statuses;
            })
            ->addColumn('current_info', function ($item) {
                if ($item->current) {
                    return [
                        'name' => $item->current->name,
                        'surname' => $item->current->surname,
                        'deleted' => $item->current->deleted_at ? true : false,
                    ];
                }

                return [
                    'name' => '-',
                    'surname' => '-',
                    'deleted' => false,
                ];
            });
    }
    public function delete(Request $request)
    {
        $offer = $this->model::find((int)$request->post('id'));
        if (!is_null($offer)) {
            if ($offer->hasApprovedProducts()) {
                return response()->json([
                    'status' => false,
                    'message' => 'Onaylanmış ürün içeren teklif silinemez'
                ]);
            }
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Kayıt bulunamadı'
            ]);
        }
        Cache::flush();
        return response()->json(['status' => true]);
    }
    private function canChangeStatus($offerProducts): bool
    {
        return $offerProducts->where('status', self::APPROVED_STATUS)->isEmpty();
    }

    private function updateProductStatuses($offerProducts, int $status): array
    {
        $productsByOffer = [];

        foreach ($offerProducts as $offerProduct) {
            $offerProduct->status = $status;
            $offerProduct->save();

            $offerId = $offerProduct->offer_id;
            $productsByOffer[$offerId] ??= [];
            $productsByOffer[$offerId][] = $offerProduct;
        }

        return $productsByOffer;
    }

    public function updateStatus(Request $request)
    {
        try {
            $productIds = $request->input('product_ids', []);
            $status = $request->input('status');
            $forceNewOrder = ($status == self::APPROVED_STATUS) ? true : false;

            if (empty($productIds) || !isset($status)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ürün ID\'leri veya durum bilgisi eksik'
                ]);
            }
            DB::beginTransaction();
            $offerProducts = OfferProduct::with(['stock', 'offer'])->whereIn('id', $productIds)->get();
            $productsByOffer = $this->updateProductStatuses($offerProducts, $status);
            $message = 'Ürün durumları başarıyla güncellendi';

            if ($status == self::APPROVED_STATUS) {
                $message = $this->processApprovedOffers($productsByOffer, $forceNewOrder);
            }
            DB::commit();
            Cache::flush();

            return response()->json([
                'success' => true,
                'message' => $message
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Ürün durumları güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    private function processApprovedOffers(array $productsByOffer, $forceNewOrder = false): string
    {
        $lastOrder = null;

        foreach ($productsByOffer as $offerId => $products) {
            $offer = $products[0]->offer;
            if (!$offer) {
                continue;
            }

            // Sipariş seçimi
            if ($forceNewOrder) {
                $order = $this->createNewOrder($offer);
            } else {
                $order = $this->findOrCreateOrder($offer);
            }
            $lastOrder = $order;

            $this->createOrderProducts($order, $products);
            $this->updateOrderTotals($order);
        }

        return $this->getSuccessMessage($lastOrder);
    }

    private function createNewOrder($offer)
    {
        return Order::create([
            'current_id' => $offer->current_id,
            'branch_id' => $offer->branch_id,
            'warehouse_id' => $offer->warehouse_id,
            'order_type_id' => self::GIVEN_ORDER_TYPE,
            'order_number' => Order::generateOrderNumber(),
            'order_date' => now()->format('Y-m-d H:i:s'),
            'exchange_rate_id' => $offer->exchange_rate_id,
            'shipping_address' => $offer->shipping_address,
            'payment_type_id' => $offer->payment_type_id,
            'notes' => $offer->notes ?? '',
            'total_price' => 0,
            'vat_amount' => 0,
            'total_amount' => 0,
            'is_active' => 1,
        ]);
    }

    private function findOrCreateOrder($offer)
    {
        return Order::firstOrCreate(
            [
                'current_id' => $offer->current_id,
                'branch_id' => $offer->branch_id,
                'warehouse_id' => $offer->warehouse_id,
                'order_type_id' => self::GIVEN_ORDER_TYPE,
                'delivery_date' => null,
            ],
            [
                'order_number' => Order::generateOrderNumber(),
                'order_date' => now()->format('Y-m-d H:i:s'),
                'exchange_rate_id' => $offer->exchange_rate_id,
                'shipping_address' => $offer->shipping_address,
                'payment_type_id' => $offer->payment_type_id,
                'notes' => $offer->notes ?? '',
                'total_price' => 0,
                'vat_amount' => 0,
                'total_amount' => 0,
                'is_active' => 1,
            ]
        );
    }

    private function updateOrderTotals($order)
    {
        $orderProducts = OrderProduct::where('order_id', $order->id)->get();
        $totalPrice = $orderProducts->sum('total_price');
        $vatAmount = $orderProducts->sum(function($item) {
            return ($item->total_price * ($item->vat_rate ?? 0)) / 100;
        });
        $totalAmount = $totalPrice + $vatAmount;

        $order->total_price = $totalPrice;
        $order->vat_amount = $vatAmount;
        $order->total_amount = $totalAmount;
        $order->save();
    }

    private function getSuccessMessage($lastOrder): string
    {
        if ($lastOrder) {
            return "Teklifiniz onaylandı ve {$lastOrder->order_number} numaralı verilen  siparişe dönüştürüldü.";
        }

        return 'Ürünler onaylandı fakat sipariş oluşturulamadı. Lütfen sistem yöneticisiyle iletişime geçin.';
    }

    private function createOrderProducts($order, array $products): void
    {
        foreach ($products as $offerProduct) {
            if ($offerProduct->status != self::APPROVED_STATUS) {
                continue;
            }

            $itemNo = OrderProduct::generateItemNo($order->order_number);

            OrderProduct::firstOrCreate(
                [
                    'order_id' => $order->id,
                    'stock_id' => $offerProduct->stock_id,
                ],
                [
                    'item_no' => $itemNo,
                    'exchange_rate_id' => $offerProduct->exchange_rate_id,
                    'product_code' => $offerProduct->product_code,
                    'product_name' => $offerProduct->product_name,
                    'quantity' => $offerProduct->quantity,
                    'unit' => $offerProduct->unit,
                    'unit_price' => $offerProduct->unit_price,
                    'status' => 0,
                    'vat_status' => $offerProduct->vat_status,
                    'currency_type' => $offerProduct->currency_type,
                    'exchange_rate' => $offerProduct->exchange_rate,
                    'total_price' => $offerProduct->total_price,
                    'vat_rate' => $offerProduct->vat_rate,
                    'is_active' => 1,
                ]
            );
        }
    }
    public function exportExcel(Request $request, $unique = null)
    {
        $offer = Offer::with(['offerProducts', 'warehouse', 'exchangeRate', 'current.neighborhood', 'current.city', 'current.country'])
            ->findOrFail($unique ?? $request->input('id'));

        $export = new ExportOffer($offer->offerProducts, $offer);
        $spreadsheet = $export->export();

        $writer = new Xlsx($spreadsheet);
        $fileName = 'teklif_' . ($offer->offer_number ?? 'bilinmiyor') . '.xlsx';

        return response()->streamDownload(function () use ($writer) {
            $writer->save('php://output');
        }, $fileName, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]);
    }
    public function pdf(Request $request, $unique = null)
    {
        // $unique ile teklif bulma (mevcut mantığı koruyoruz)
        $offer = Offer::with(['offerProducts', 'warehouse', 'exchangeRate', 'current.neighborhood', 'current.city', 'current.country'])
            ->findOrFail($unique ?? $request->input('id')); // $unique null ise, Request'ten id al

        // Request'ten ek parametreleri al (örneğin, dil veya format seçenekleri)
        $language = $request->input('language', 'tr'); // Varsayılan olarak Türkçe
        $paperSize = $request->input('paper_size', 'a4'); // Varsayılan A4
        $orientation = $request->input('orientation', 'portrait'); // Varsayılan portre

        // ExportOffer sınıfına ek parametreler geçilebilir (örneğin, dil desteği için)
        $export = new ExportOffer($offer->offerProducts, $offer, ['language' => $language]);
        $html = $export->exportToHtml(); // HTML içeriğini al

        // PDF oluştururken Request'ten gelen parametreleri kullan
        $pdf = Pdf::loadHTML($html)
            ->setPaper($paperSize, $orientation)
            ->setOptions([
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'DejaVu Sans', // Türkçe karakter desteği
            ]);

        // Dosya adını dinamik olarak oluştur
        $fileName = 'teklif_' . ($offer->offer_number ?? 'bilinmiyor') . '.pdf';

        // Request'ten download isteği gelip gelmediğini kontrol et
        $attachment = $request->input('download', true); // Varsayılan olarak indirme

        return $pdf->stream($fileName, ['Attachment' => $attachment]);
    }
}
