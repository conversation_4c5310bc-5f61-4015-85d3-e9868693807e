<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class RoleController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Rol';
        $this->page = 'role';
        $this->model = new Role();

        $this->view = (object)array(
            'breadcrumb' => array(
                'Ayarlar' => '#',
                'Roller' => route('backend.role_list'),
            ),
        );

        View::share('routes', Route::all()->groupBy('category_name'));
        parent::__construct();
    }

    public function saveHook(Request $request)
    {
        $params = $request->all();
        if (isset($params['permissions']) && !is_null($params['permissions'])) {
            $params['permissions'] = json_encode($params['permissions']);
        }

        return $params;
    }
}
