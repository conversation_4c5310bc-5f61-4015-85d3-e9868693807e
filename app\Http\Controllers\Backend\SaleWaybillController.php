<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Models\Current;
use App\Models\Invoice;
use App\Models\SalesWaybill;
use App\Models\SalesWaybillItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Barryvdh\DomPDF\Facade\Pdf;

class SaleWaybillController extends Controller
{
    /**
     * İrsaliye listesi
     */
    public function index(Request $request)
    {
        $query = SalesWaybill::with('current')->orderBy('id', 'desc');

        // Arama filtresi
        if ($request->isMethod('post') && $request->has('search')) {
            $search = $request->input('search');
            $query->where(function($q) use ($search) {
                $q->where('waybill_no', 'like', "%{$search}%")
                  ->orWhereHas('current', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $items = $query->paginate(20);

        return view('backend.sales-waybills.index', compact('items'));
    }

    /**
     * İrsaliye formu
     */
    public function form(Request $request, $id = null)
    {
        // İrsaliye bilgilerini getir
        $item = null;

        if ($id) {
            $item = SalesWaybill::with(['items'])->find($id);

            if (!$item) {
                return redirect()->route('backend.sales-waybills.index')
                    ->with('error', 'İrsaliye bulunamadı');
            }
        }

        // Cari hesapları getir
        $currents = Current::orderBy('name')->get();

        // Gerçek faturaları getir
        try {
            // Önce veritabanı bağlantısını kontrol et
            DB::connection()->getPdo();

            // Veritabanı tablolarını kontrol et
            $tables = DB::select('SHOW TABLES');
            $tableNames = [];
            foreach ($tables as $table) {
                $tableNames[] = reset($table);
            }

            // Log ekle
            Log::info('Veritabanı tabloları: ' . implode(', ', $tableNames));

            // Satış faturalarını getir - document_type_id filtresini kaldırdık
            $invoices = DB::table('invoices')
                ->select('invoices.*', 'current.name as current_name')
                ->leftJoin('current', 'invoices.current_id', '=', 'current.id')
                ->orderBy('invoices.id', 'desc')
                ->limit(20)
                ->get();

            // Fatura kalemlerini işle
            foreach ($invoices as $invoice) {
                // items alanını JSON olarak decode et
                if (isset($invoice->items) && !empty($invoice->items)) {
                    try {
                        // JSON içeriğini logla
                        Log::info('Fatura ID: ' . $invoice->id . ', JSON içeriği: ' . $invoice->items);

                        $items = json_decode($invoice->items);

                        // JSON decode sonucunu logla
                        Log::info('JSON decode sonucu: ' . print_r($items, true));

                        // İlk kalemi al
                        if (is_array($items) && count($items) > 0) {
                            $firstItem = $items[0];
                            // Gerekli değerleri ekle
                            $invoice->first_item_vat_rate = $firstItem->vat_rate ?? 18;
                            $invoice->first_item_tax_amount = $firstItem->tax_amount ?? 0;
                            $invoice->first_item_net_amount = $firstItem->net_amount ?? 0;

                            // Değerleri logla
                            Log::info('Array olarak işlendi - Fatura ID: ' . $invoice->id .
                                      ', KDV Oranı: ' . $invoice->first_item_vat_rate .
                                      ', KDV Tutarı: ' . $invoice->first_item_tax_amount .
                                      ', Net Tutar: ' . $invoice->first_item_net_amount);
                        } elseif (is_object($items) && isset($items->vat_rate)) {
                            // Tek bir nesne olarak gelmiş olabilir
                            $invoice->first_item_vat_rate = $items->vat_rate ?? 18;
                            $invoice->first_item_tax_amount = $items->tax_amount ?? 0;
                            $invoice->first_item_net_amount = $items->net_amount ?? 0;

                            // Değerleri logla
                            Log::info('Obje olarak işlendi - Fatura ID: ' . $invoice->id .
                                      ', KDV Oranı: ' . $invoice->first_item_vat_rate .
                                      ', KDV Tutarı: ' . $invoice->first_item_tax_amount .
                                      ', Net Tutar: ' . $invoice->first_item_net_amount);
                        } else {
                            // Varsayılan değerler
                            $invoice->first_item_vat_rate = 18;
                            $invoice->first_item_tax_amount = $invoice->total_amount * 0.15;
                            $invoice->first_item_net_amount = $invoice->total_amount * 0.85;

                            // Değerleri logla
                            Log::info('Varsayılan değerler kullanıldı - Fatura ID: ' . $invoice->id .
                                      ', KDV Oranı: ' . $invoice->first_item_vat_rate .
                                      ', KDV Tutarı: ' . $invoice->first_item_tax_amount .
                                      ', Net Tutar: ' . $invoice->first_item_net_amount);
                        }
                    } catch (\Exception $e) {
                        // Hata durumunda varsayılan değerler
                        Log::error('JSON decode hatası: ' . $e->getMessage() . ' - Fatura ID: ' . $invoice->id);
                        $invoice->first_item_vat_rate = 18;
                        $invoice->first_item_tax_amount = $invoice->total_amount * 0.15;
                        $invoice->first_item_net_amount = $invoice->total_amount * 0.85;
                    }
                } else {
                    // items alanı yoksa varsayılan değerler
                    Log::info('items alanı boş - Fatura ID: ' . $invoice->id);
                    $invoice->first_item_vat_rate = 18;
                    $invoice->first_item_tax_amount = $invoice->total_amount * 0.15;
                    $invoice->first_item_net_amount = $invoice->total_amount * 0.85;
                }
            }

            // Sorgu logla
            DB::enableQueryLog();
            $query = DB::table('invoices')
                ->select('invoices.*', 'current.name as current_name')
                ->leftJoin('currents', 'invoices.current_id', '=', 'current.id')
                ->orderBy('invoices.id', 'desc')
                ->limit(20)
                ->toSql();
            Log::info('SQL sorgusu: ' . $query);

            // Fatura yoksa test verileri ekle
            if ($invoices->isEmpty()) {
                // Log ekle
                Log::info('Veritabanında satış faturası bulunamadı, test verileri kullanılıyor.');

                // Test faturaları oluştur
                $invoices = collect([
                    (object)[
                        'id' => 1,
                        'invoice_no' => 'FTR-2023-001',
                        'invoice_date' => '2023-05-01',
                        'current_name' => 'Test Müşteri 1',
                        'total_amount' => 1000.00
                    ],
                    (object)[
                        'id' => 2,
                        'invoice_no' => 'FTR-2023-002',
                        'invoice_date' => '2023-05-02',
                        'current_name' => 'Test Müşteri 2',
                        'total_amount' => 2000.00
                    ],
                    (object)[
                        'id' => 3,
                        'invoice_no' => 'FTR-2023-003',
                        'invoice_date' => '2023-05-03',
                        'current_name' => 'Test Müşteri 3',
                        'total_amount' => 3000.00
                    ]
                ]);
            } else {
                // Log ekle
                Log::info('Veritabanından ' . $invoices->count() . ' adet satış faturası alındı.');

                // Faturaları logla
                foreach ($invoices as $index => $invoice) {
                    Log::info("Fatura {$index}: " . json_encode($invoice));
                }
            }
        } catch (\Exception $e) {
            // Hata durumunda log ekle
            Log::error('Veritabanı hatası: ' . $e->getMessage());

            // Test faturaları oluştur
            $invoices = collect([
                (object)[
                    'id' => 1,
                    'invoice_no' => 'FTR-2023-001',
                    'invoice_date' => '2023-05-01',
                    'current_name' => 'Test Müşteri 1',
                    'total_amount' => 1000.00
                ],
                (object)[
                    'id' => 2,
                    'invoice_no' => 'FTR-2023-002',
                    'invoice_date' => '2023-05-02',
                    'current_name' => 'Test Müşteri 2',
                    'total_amount' => 2000.00
                ],
                (object)[
                    'id' => 3,
                    'invoice_no' => 'FTR-2023-003',
                    'invoice_date' => '2023-05-03',
                    'current_name' => 'Test Müşteri 3',
                    'total_amount' => 3000.00
                ],
                (object)[
                    'id' => 4,
                    'invoice_no' => 'FTR-2023-004',
                    'invoice_date' => '2023-05-04',
                    'current_name' => 'Test Müşteri 4',
                    'total_amount' => 4000.00
                ],
                (object)[
                    'id' => 5,
                    'invoice_no' => 'FTR-2023-005',
                    'invoice_date' => '2023-05-05',
                    'current_name' => 'Test Müşteri 5',
                    'total_amount' => 5000.00
                ]
            ]);
        }

        if (!$id) {
            $autoWaybillNo = $this->generateWaybillNo();
        } else {
            $autoWaybillNo = $item ? $item->waybill_no : '';
        }

        return view('backend.sales-waybills.form', compact('item', 'currents', 'invoices', 'autoWaybillNo'));
    }

    /**
     * İrsaliye kaydet
     */
    public function save(Request $request, $id = null)
    {
        // Validasyon
        $request->validate([
            'waybill_no' => 'required|string|max:50',
            'waybill_date' => 'required|date',
            'current_id' => 'required|exists:currents,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required',
            'items.*.quantity' => 'required|numeric|min:0.01',
        ]);

        try {
            DB::beginTransaction();

            // İrsaliye kaydı
            $waybill = $id ? SalesWaybill::find($id) : new SalesWaybill();

            if (!$waybill) {
                return redirect()->route('backend.sales-waybills.index')
                    ->with('error', 'İrsaliye bulunamadı');
            }

            // Eğer yeni kayıt ve waybill_no boşsa otomatik üret
            if (!$id && !$request->input('waybill_no')) {
                $waybill->waybill_no = $this->generateWaybillNo();
            } else {
                $waybill->waybill_no = $request->input('waybill_no');
            }
            $waybill->waybill_date = $request->input('waybill_date');
            $waybill->current_id = $request->input('current_id');
            $waybill->invoice_id = $request->input('invoice_id');
            $waybill->invoice_no = $request->input('invoice_no');
            $waybill->note = $request->input('note');
            $waybill->total_amount = $request->input('total_amount', 0);
            $waybill->waybill_type = 1; // Satış İrsaliyesi
            $waybill->status = $request->input('status', 'pending');
            $waybill->created_by = Auth::id();
            $waybill->save();

            // Mevcut kalemleri sil
            if ($id) {
                SalesWaybillItem::where('waybill_id', $waybill->id)->delete();
            }

            // İrsaliye kalemlerini kaydet
            foreach ($request->input('items') as $item) {
                $waybillItem = new SalesWaybillItem();
                $waybillItem->waybill_id = $waybill->id;
                $waybillItem->product_id = $item['product_id'];
                $waybillItem->product_name = $item['product_name'];
                $waybillItem->quantity = $item['quantity'];
                $waybillItem->price = $item['price'] ?? 0;
                $waybillItem->vat_rate = $item['vat_rate'] ?? 0;
                $waybillItem->vat_amount = $item['vat_amount'] ?? 0;
                $waybillItem->total = $item['total'] ?? 0;
                $waybillItem->save();
            }

            DB::commit();

            return redirect()->route('backend.sales-waybills.index')
                ->with('success', 'İrsaliye başarıyla kaydedildi');

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('İrsaliye kaydedilirken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'İrsaliye kaydedilirken bir hata oluştu: ' . $e->getMessage());
        }
    }

    /**
     * İrsaliye sil
     */
    public function delete(Request $request)
    {
        $id = $request->input('id');

        if (!$id) {
            return response()->json([
                'success' => false,
                'message' => 'İrsaliye ID\'si belirtilmedi'
            ]);
        }

        try {
            DB::beginTransaction();

            // İrsaliye kalemlerini sil
            SalesWaybillItem::where('waybill_id', $id)->delete();

            // İrsaliyeyi sil
            SalesWaybill::destroy($id);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'İrsaliye başarıyla silindi'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('İrsaliye silinirken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'İrsaliye silinirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Fatura listesini getir (AJAX)
     */
    public function getInvoiceList(Request $request)
    {
        $search = $request->input('search', '');

        // Başlangıç log kaydı
        Log::info('SaleWaybillController::getInvoiceList çağrıldı', [
            'search' => $search,
            'request' => $request->all(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'headers' => $request->headers->all()
        ]);

        // Veritabanı bağlantısını kontrol et
        DB::connection()->getPdo();
        Log::info('Veritabanı bağlantısı başarılı');

        // Veritabanı yapısını kontrol et ve log'a yaz
        $hasInvoicesTable = Schema::hasTable('invoices');
        Log::info('Invoices tablosu var mı: ' . ($hasInvoicesTable ? 'Evet' : 'Hayır'));

        if (!$hasInvoicesTable) {
            return response()->json([
                'success' => false,
                'error' => 'Invoices tablosu bulunamadı',
                'debug_info' => [
                    'tables' => Schema::getAllTables()
                ]
            ], 500);
        }

        $columns = Schema::getColumnListing('invoices');
        Log::info('Invoices tablosu sütunları', [
            'has_document_type_id' => in_array('document_type_id', $columns),
            'columns' => $columns
        ]);

        // Invoices tablosunda kayıt var mı kontrol et
        $invoiceCount = Invoice::count();
        Log::info('Invoices tablosunda kayıt sayısı: ' . $invoiceCount);

        // Gerçek verileri almayı dene
        $query = Invoice::with('current')->where('is_active', true);

        // Arama filtresi
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('invoice_no', 'like', "%{$search}%")
                  ->orWhereHas('current', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Faturaları getir
        $invoices = $query->orderBy('id', 'desc')->limit(50)->get();

        Log::info('Gerçek faturalar alındı', [
            'count' => $invoices->count(),
            'first_invoice' => $invoices->first() ? $invoices->first()->toArray() : null
        ]);

        // Gerçek veriler varsa döndür
        if ($invoices->count() > 0) {
            return response()->json([
                'success' => true,
                'data' => $invoices,
                'source' => 'database'
            ]);
        }

        // Test verisi döndür
        $testInvoices = [
            [
                'id' => 1,
                'invoice_no' => 'TEST-001',
                'invoice_date' => '2023-05-07',
                'current_id' => 1,
                'total_amount' => 1000.00,
                'current' => [
                    'id' => 1,
                    'name' => 'Test Müşteri'
                ]
            ],
            [
                'id' => 2,
                'invoice_no' => 'TEST-002',
                'invoice_date' => '2023-05-08',
                'current_id' => 2,
                'total_amount' => 2000.00,
                'current' => [
                    'id' => 2,
                    'name' => 'Test Müşteri 2'
                ]
            ]
        ];

        Log::info('Test faturaları döndürülüyor', [
            'count' => count($testInvoices)
        ]);

        return response()->json([
            'success' => true,
            'data' => $testInvoices,
            'source' => 'test_data'
        ]);
    }

    /**
     * Fatura bilgilerini getir (AJAX)
     */
    public function getInvoice($id)
    {
        try {
            // Faturayı getir
            $invoice = Invoice::with('current')->find($id);

            if (!$invoice) {
                return response()->json([
                    'success' => false,
                    'error' => 'Fatura bulunamadı'
                ], 404);
            }

            // Fatura kalemlerini JSON'dan çıkar
            $items = [];
            if ($invoice->items) {
                $items = json_decode($invoice->items, true) ?: [];
            }

            return response()->json([
                'success' => true,
                'invoice' => $invoice,
                'items' => $items
            ]);

        } catch (\Exception $e) {
            Log::error('Fatura bilgileri alınırken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Fatura bilgileri alınırken bir hata oluştu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toplu işlem uygula
     */
    public function bulkAction(Request $request)
    {
        $ids = $request->input('ids', []);
        $action = $request->input('action');

        if (empty($ids) || !$action) {
            return response()->json([
                'success' => false,
                'message' => 'Geçersiz istek'
            ]);
        }

        try {
            DB::beginTransaction();

            // Durum güncelleme
            $status = null;
            if ($action == 'approve') {
                $status = 'approved';
            } elseif ($action == 'cancel') {
                $status = 'cancelled';
            } elseif ($action == 'pending') {
                $status = 'pending';
            }

            if ($status) {
                SalesWaybill::whereIn('id', $ids)->update(['status' => $status]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'İrsaliyeler başarıyla güncellendi'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('İrsaliyeler güncellenirken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'İrsaliyeler güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Tekil irsaliye durumu güncelleme
     */
    public function updateStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');

        if (!$id || !$status) {
            return response()->json([
                'success' => false,
                'message' => 'Geçersiz istek'
            ]);
        }

        try {
            DB::beginTransaction();

            // İrsaliyeyi bul
            $waybill = SalesWaybill::find($id);

            if (!$waybill) {
                return response()->json([
                    'success' => false,
                    'message' => 'İrsaliye bulunamadı'
                ]);
            }

            // Durumu güncelle
            $waybill->status = $status;
            $waybill->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'İrsaliye durumu başarıyla güncellendi'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('İrsaliye durumu güncellenirken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'İrsaliye durumu güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * İrsaliye PDF'ini oluştur
     */
    public function pdf($id)
    {
        try {
            // İrsaliye bilgilerini getir
            $waybill = SalesWaybill::with(['items', 'current'])->find($id);

            if (!$waybill) {
                return redirect()->route('backend.sales-waybills.index')
                    ->with('error', 'İrsaliye bulunamadı');
            }

            // PDF oluştur
            $pdf = Pdf::loadView('backend.sales-waybills.pdf', compact('waybill'));

            // PDF'i indir
            return $pdf->download('irsaliye-' . $waybill->waybill_no . '.pdf');
        } catch (\Exception $e) {
            Log::error('İrsaliye PDF oluşturulurken hata oluştu', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('backend.sales-waybills.index')
                ->with('error', 'İrsaliye PDF oluşturulurken bir hata oluştu: ' . $e->getMessage());
        }
    }

    private function generateWaybillNo($month = null, $year = null)
    {
        $month = $month ?: date('m');
        $year = $year ?: date('Y');
        $prefix = 'SIR';
        $waybillNoPrefix = $prefix . '/' . $month . '/' . $year . '/';

        // Counter tablosunu kullan
        $counter = DB::table('invoice_counters')
            ->where('prefix', $prefix)
            ->where('month', $month)
            ->where('year', $year)
            ->first();

        if ($counter) {
            $nextNumber = $counter->last_number + 1;
            DB::table('invoice_counters')
                ->where('id', $counter->id)
                ->update(['last_number' => $nextNumber]);
        } else {
            $nextNumber = 1;
            DB::table('invoice_counters')->insert([
                'prefix' => $prefix,
                'month' => $month,
                'year' => $year,
                'last_number' => $nextNumber,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
        return $waybillNoPrefix . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);
    }
}
