<?php

namespace App\Http\Controllers\Backend;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Stock;
use App\Models\StockBatch;
use App\Models\StockSerialNumber;
use App\Models\StockReservation;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Stok Durumu';
        $this->page = 'stock';
        $this->model = new Stock();
        $this->relation = ['product', 'variant', 'warehouse', 'warehouseLocation', 'stockBatch'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Stok Yönetimi' => '#',
                'Stok Durumu' => route('backend.stock_list'),
            ),
        );

        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->get());
        view()->share('batches', StockBatch::active()->get());
        
        // Seri numarası durumları
        view()->share('serialStatuses', [
            1 => 'Stokta',
            2 => 'Satıldı',
            3 => 'Hasarlı',
            4 => 'Kayıp'
        ]);
        
        parent::__construct();
    }

    public function list(Request $request)
    {
        if ($request->has('datatable')) {
            $list = $this->model::with($this->relation)->get();

            // datatable hook çağrısı
            $datatable = datatables()->of($list);
            if (method_exists($this, 'datatableHook')) {
                $datatable = $this->datatableHook($datatable);
            }

            // Seri numaraları için batch bilgileri sağlanmışsa
            if ($request->ajax() && $request->has('batch_id') && $request->has('stock_id')) {
                $batchId = $request->input('batch_id');
                $stockId = $request->input('stock_id');
                
                $batch = StockBatch::find($batchId);
                $stock = Stock::with(['product', 'warehouse', 'warehouseLocation'])->find($stockId);
                
                if (!$batch || !$stock) {
                    return response()->json(['status' => false, 'message' => 'Batch veya stok kaydı bulunamadı']);
                }
                
                $serials = StockSerialNumber::where('stock_batch_id', $batchId)
                    ->where('warehouse_id', $stock->warehouse_id)
                    ->where('warehouse_location_id', $stock->warehouse_location_id)
                    ->with(['stockMovement'])
                    ->get();
                
                $html = view('backend.stock.partials.serials_modal', compact('serials', 'batch', 'stock', 'serialStatuses'))->render();
                
                return response()->json([
                    'status' => true,
                    'html' => $html,
                    'batch' => $batch,
                    'stock' => $stock
                ]);
            }

            // Standart datatable çıktısı
            return $datatable->make(true);
        }

        // Batch bilgileri için ajax isteği varsa
        if ($request->ajax() && $request->has('get_batch')) {
            $batchId = $request->input('batch_id');
            $productId = $request->input('product_id');
            $variantId = $request->input('variant_id');
            
            // Yeni batch için batch numarası oluştur
            if ($batchId === 'new') {
                // Ürün bilgilerini al
                $product = Product::find($productId);
                if (!$product) {
                    return response()->json(['status' => false, 'message' => 'Ürün bulunamadı']);
                }
                
                // Batch kodu oluştur (P-{product_id}-{YYYYMMDD}-{random_number})
                $batchCode = 'B-' . $productId . '-' . date('Ymd') . '-' . rand(1000, 9999);
                
                // Lot numarası oluştur (L-{date}-{random_number})
                $lotNumber = 'L-' . date('Ymd') . '-' . rand(100, 999);
                
                return response()->json([
                    'status' => true,
                    'batch' => [
                        'id' => 'new',
                        'batch_number' => $batchCode,
                        'lot_number' => $lotNumber,
                        'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                        'product_id' => $productId,
                        'variant_id' => $variantId,
                    ]
                ]);
            }
            
            $batch = StockBatch::find($batchId);
            if (!$batch) {
                return response()->json(['status' => false, 'message' => 'Batch bulunamadı']);
            }
            
            return response()->json(['status' => true, 'batch' => $batch]);
        }
        
        // Seri numaraları için ajax isteği varsa
        if ($request->ajax() && $request->has('get_serials')) {
            $batchId = $request->input('batch_id');
            
            if (!$batchId || $batchId === 'new') {
                return response()->json(['status' => true, 'serials' => []]);
            }
            
            $serials = StockSerialNumber::where('stock_batch_id', $batchId)
                ->where('status', 1) // Stokta olanlar
                ->where('is_active', 1)
                ->get();
                
            return response()->json(['status' => true, 'serials' => $serials]);
        }

        $container = (object) array(
            'title' => $this->title,
            'page' => $this->page,
        );

        return view('backend.' . $this->page . '.list', compact('container'));
    }

    public function form(Request $request, $unique = NULL)
    {
        // StockController'da direkt stok ekleme yerine StockMovement kullanılmalı
        if (is_null($unique)) {
            return redirect()->route('backend.stock_movement_form')
                ->with('info', 'Yeni stok eklemek için stok hareketi oluşturmalısınız');
        }

        return parent::form($request, $unique);
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('available_quantity', function ($item) {
            return $this->calculateAvailableQuantity($item);
        })
        ->editColumn('reserved_quantity', function ($item) {
            return $this->calculateReservedQuantity($item);
        })
        ->editColumn('expiry_date', function ($item) {
            if ($item->stockBatch && $item->stockBatch->expiry_date) {
                return $item->stockBatch->expiry_date->format('d.m.Y');
            }
            return '-';
        })
        ->editColumn('batch_info', function ($item) {
            if ($item->stockBatch) {
                $info = 'Lot: ' . $item->stockBatch->lot_number;
                if ($item->stockBatch->batch_number) {
                    $info .= ' / Batch: ' . $item->stockBatch->batch_number;
                }
                
                // Seri numaralarını kontrol et
                $serialCount = StockSerialNumber::where('stock_batch_id', $item->stockBatch->id)
                    ->where('status', 1) // Stokta olanlar
                    ->where('is_active', 1)
                    ->count();
                    
                if ($serialCount > 0) {
                    $info .= ' <span class="badge bg-info">SN: ' . $serialCount . '</span>';
                }
                
                return $info;
            }
            return '-';
        })
        ->addColumn('actions', function ($item) {
            $actions = '<div class="d-flex align-items-center gap-10 justify-content-center">';
            
            $actions .= '<a href="' . route('backend.' . $this->page . '_form', $item->id) . '" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle"><iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon></a>';
            
            if ($item->stockBatch) {
                $actions .= '<a href="#" class="view-serials bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" data-stock-id="' . $item->id . '" data-batch-id="' . $item->stockBatch->id . '"><iconify-icon icon="mdi:barcode-scan" class="menu-icon"></iconify-icon></a>';
            }
            
            $actions .= '<button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="' . $item->id . '"><iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon></button>';
            
            $actions .= '</div>';
            
            return $actions;
        });
    }

    private function calculateAvailableQuantity($stock)
    {
        $reservedQuantity = $this->calculateReservedQuantity($stock);
        return $stock->quantity - $reservedQuantity;
    }

    private function calculateReservedQuantity($stock)
    {
        return StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity');
    }

    public function saveHook($request)
    {
        $params = $request->all();
        
        // Negatif stok kontrolü
        if (isset($params['quantity']) && $params['quantity'] < 0) {
            throw new \Exception('Stok miktarı negatif olamaz');
        }
        
        // Aynı ürün için aynı lokasyonda stok var mı kontrolü
        $exists = Stock::where('product_id', $params['product_id'])
            ->where('variant_id', $params['variant_id'])
            ->where('warehouse_id', $params['warehouse_id'])
            ->where('warehouse_location_id', $params['warehouse_location_id'])
            ->where('stock_batch_id', $params['stock_batch_id']);
            
        if (isset($params['id'])) {
            $exists->where('id', '!=', $params['id']);
        }
        
        if ($exists->exists()) {
            throw new \Exception('Bu kombinasyon için stok kaydı zaten mevcut');
        }
        
        return $params;
    }
}
