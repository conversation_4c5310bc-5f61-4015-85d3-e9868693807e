<?php

namespace App\Http\Controllers\Backend;

use App\Models\Current;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ProductUnitConversion;
use App\Models\Status;
use App\Models\Stock;
use App\Models\StockBatch;
use App\Models\StockMovement;
use App\Models\StockMovementReason;
use App\Models\StockMovementType;
use App\Models\StockReservation;
use App\Models\StockSerialNumber;
use App\Models\Unit;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class StockMovementController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Stok Hareketleri';
        $this->page = 'stock_movement';
        $this->model = new StockMovement();
        $this->relation = ['stock', 'product', 'variant', 'warehouse', 'location', 'targetWarehouse', 'targetLocation', 'current', 'status', 'stockMovementReason'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Stok Yönetimi' => '#',
                'Stok Hareketleri' => route('backend.stock_movement_list'),
            ),
        );

        view()->share('stocks', Stock::with(['product', 'variant', 'warehouse', 'warehouseLocation'])->active()->get());
        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('warehouses', Warehouse::active()->get());
        view()->share('locations', WarehouseLocation::active()->get());
        view()->share('currents', Current::active()->get());
        view()->share('statuses', Status::active()->get());
        view()->share('movementReasons', StockMovementReason::active()->get());
        view()->share('movementTypes', StockMovementType::active()->get());
        view()->share('units', Unit::active()->get());
        view()->share('productUnitConversions', ProductUnitConversion::with(['fromUnit', 'toUnit'])->active()->get());
        view()->share('batches', StockBatch::active()->get());

        // Seri numarası durumları
        view()->share('serialStatuses', [
            1 => 'Stokta',
            2 => 'Satıldı',
            3 => 'Hasarlı',
            4 => 'Kayıp'
        ]);

        parent::__construct();
    }

    public function list(Request $request)
    {
        // Batch ve seri numarası işlemleri için Ajax istekleri
        if ($request->ajax()) {
            // Batch bilgileri için istek
            if ($request->has('get_batch')) {
                $batchId = $request->input('batch_id');
                $productId = $request->input('product_id');
                $variantId = $request->input('variant_id');

                // Yeni batch için batch numarası oluştur
                if ($batchId === 'new') {
                    // Ürün bilgilerini al
                    $product = Product::find($productId);
                    if (!$product) {
                        return response()->json(['status' => false, 'message' => 'Ürün bulunamadı']);
                    }

                    // Batch kodu oluştur (B-{product_id}-{YYYYMMDD}-{random_number})
                    $batchCode = 'B-' . $productId . '-' . date('Ymd') . '-' . rand(1000, 9999);

                    // Lot numarası oluştur (L-{date}-{random_number})
                    $lotNumber = 'L-' . date('Ymd') . '-' . rand(100, 999);

                    return response()->json([
                        'status' => true,
                        'batch' => [
                            'id' => 'new',
                            'batch_number' => $batchCode,
                            'lot_number' => $lotNumber,
                            'expiry_date' => date('Y-m-d', strtotime('+1 year')),
                            'product_id' => $productId,
                            'variant_id' => $variantId,
                        ]
                    ]);
                }

                $batch = StockBatch::find($batchId);
                if (!$batch) {
                    return response()->json(['status' => false, 'message' => 'Batch bulunamadı']);
                }

                return response()->json(['status' => true, 'batch' => $batch]);
            }

            // Seri numaraları için istek
            if ($request->has('get_serials')) {
                $batchId = $request->input('batch_id');

                if (!$batchId || $batchId === 'new') {
                    return response()->json(['status' => true, 'serials' => []]);
                }

                $serials = StockSerialNumber::where('stock_batch_id', $batchId)
                    ->where('status', 1) // Stokta olanlar
                    ->where('is_active', 1)
                    ->get();

                return response()->json(['status' => true, 'serials' => $serials]);
            }
        }

        return parent::list($request);
    }

    public function status(Request $request)
    {
        $movement = StockMovement::with(['stock', 'product', 'variant', 'stockMovementReason'])->find($request->id);

        if (!$movement) {
            return response()->json(['status' => false, 'message' => 'Hareket bulunamadı']);
        }

        DB::beginTransaction();

        try {
            $previousStatus = $movement->status_id;
            $newStatus = $request->status;

            // Status değişimini kontrol et
            if ($previousStatus == $newStatus) {
                return response()->json(['status' => false, 'message' => 'Status zaten aynı']);
            }

            // Status güncelle
            $movement->status_id = $newStatus;

            // Status 1'den 2'ye (Beklemeden Onaylıya)
            if ($previousStatus == 1 && $newStatus == 2) {
                $movement->approver_id = Auth::user()->id;
                $movement->approval_date = now();

                // Ürün bilgilerinden birim bilgisini al
                if ($movement->product && $movement->product->unit_id) {
                    $movement->unit_id = $movement->product->unit_id;

                    // Mevcut base_quantity değeri yoksa veya 0 ise, quantity değerini ata
                    if (!$movement->base_quantity || $movement->base_quantity == 0) {
                        $movement->base_quantity = $movement->quantity;
                    }
                }

                // Stock batch id işlemleri
                if ($request->has('batch_data') && !empty($request->batch_data)) {
                    $batchData = json_decode($request->batch_data, true);

                    // Yeni batch oluştur veya mevcut batch'i kullan
                    if (isset($batchData['id']) && $batchData['id'] === 'new') {
                        // Yeni batch oluştur
                        $batch = StockBatch::create([
                            'product_id' => $movement->product_id,
                            'variant_id' => $movement->variant_id,
                            'batch_number' => $batchData['batch_number'],
                            'lot_number' => $batchData['lot_number'],
                            'expiry_date' => $batchData['expiry_date'],
                            'current_quantity' => $movement->base_quantity ?: $movement->quantity,
                            'current_id' => $movement->current_id,
                            'is_active' => 1,
                            'created_by' => Auth::user()->id
                        ]);

                        $movement->stock_batch_id = $batch->id;
                    } else if (isset($batchData['id']) && is_numeric($batchData['id'])) {
                        // Mevcut batch'i kullan
                        $batch = StockBatch::find($batchData['id']);
                        if ($batch) {
                            $batch->current_quantity += ($movement->base_quantity ?: $movement->quantity);
                            $batch->save();

                            $movement->stock_batch_id = $batch->id;
                        }
                    }
                }

                // Seri numarası işlemleri
                if ($request->has('serial_numbers') && !empty($request->serial_numbers)) {
                    $serialNumbers = json_decode($request->serial_numbers, true);

                    if (is_array($serialNumbers) && count($serialNumbers) > 0) {
                        foreach ($serialNumbers as $serialNumber) {
                            // Seri numarasını oluştur
                            StockSerialNumber::create([
                                'product_id' => $movement->product_id,
                                'variant_id' => $movement->variant_id,
                                'stock_batch_id' => $movement->stock_batch_id,
                                'serial_number' => $serialNumber,
                                'warehouse_id' => $movement->warehouse_id ?: $movement->target_warehouse_id,
                                'warehouse_location_id' => $movement->location_id ?: $movement->target_location_id,
                                'stock_movement_id' => $movement->id,
                                'status' => 1, // Stokta
                                'is_active' => 1,
                                'created_by' => Auth::user()->id
                            ]);
                        }
                    }
                }

                // Stok işlemlerini yap
                $this->processStockMovement($movement);
            }

            // Status 2'den 3'e (Onaylıdan İptale)
            elseif ($previousStatus == 2 && $newStatus == 3) {
                // Seri numaralarını iptal et
                if ($movement->stock_batch_id) {
                    StockSerialNumber::where('stock_movement_id', $movement->id)
                        ->update([
                            'status' => 4, // Kayıp
                            'notes' => 'İptal edilen hareket: ' . $movement->id,
                            'updated_by' => Auth::user()->id
                        ]);
                }

                // Stok işlemlerini geri al
                $this->reverseStockMovement($movement);
            }

            // Status 1'den 3'e (Beklemeden İptale)
            elseif ($previousStatus == 1 && $newStatus == 3) {
                // Herhangi bir stok işlemi yapma
            }

            $movement->save();

            DB::commit();
            return response()->json(['status' => true, 'message' => 'Durum güncellendi']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => 'Hata oluştu: ' . $e->getMessage()]);
        }
    }

    private function processStockMovement($movement)
    {
        // Hareket türü kontrolü
        $movementType = $movement->stockMovementReason->movement_type_id;

        switch ($movementType) {
            case 1: // Giriş
                $this->processStockIn($movement);
                break;

            case 2: // Çıkış
                $this->processStockOut($movement);
                break;

            case 3: // Transfer
                $this->processStockTransfer($movement);
                break;

            default:
                throw new \Exception('Geçersiz hareket türü');
        }
    }

    private function processStockIn($movement)
    {
        // Giriş işleminde mevcut stok kontrolü
        $stock = null;

        // Batch kullanılıyorsa batch için ayrı stok oluştur
        if ($movement->stock_batch_id) {
            // Batch için mevcut stok var mı?
            $existingBatchStock = Stock::where('product_id', $movement->product_id)
                ->where('warehouse_id', $movement->warehouse_id)
                ->where('warehouse_location_id', $movement->location_id)
                ->where('variant_id', $movement->variant_id)
                ->where('stock_batch_id', $movement->stock_batch_id)
                ->first();

            if ($existingBatchStock) {
                $stock = $existingBatchStock;
            } else {
                // Yeni batch stok kaydı oluştur
                $stock = Stock::create([
                    'product_id' => $movement->product_id,
                    'variant_id' => $movement->variant_id,
                    'warehouse_id' => $movement->warehouse_id,
                    'warehouse_location_id' => $movement->location_id,
                    'stock_batch_id' => $movement->stock_batch_id,
                    'quantity' => 0,
                    'is_active' => 1,
                    'created_by' => Auth::user()->id
                ]);
            }
        }
        // Batch kullanılmıyorsa normal stok oluştur
        else {
            // Eğer movement'ta stock_id varsa önce kontrol et
            if ($movement->stock_id) {
                // Mevcut stoğu kontrol et
                $existingStock = Stock::where('product_id', $movement->product_id)
                    ->where('warehouse_id', $movement->warehouse_id)
                    ->where('warehouse_location_id', $movement->location_id)
                    ->where('variant_id', $movement->variant_id)
                    ->whereNull('stock_batch_id') // Batch olmayan stok
                    ->first();

                if ($existingStock) {
                    $stock = $existingStock;
                }
            }

            // Eğer stock bulunamadıysa yeni oluştur
            if (!$stock) {
                // Yeni stock kaydı oluştur
                $stock = Stock::create([
                    'product_id' => $movement->product_id,
                    'variant_id' => $movement->variant_id,
                    'warehouse_id' => $movement->warehouse_id,
                    'warehouse_location_id' => $movement->location_id,
                    'stock_batch_id' => null,
                    'quantity' => 0,
                    'is_active' => 1,
                    'created_by' => Auth::user()->id
                ]);
            }
        }

        // Movement'a doğru stock_id'yi ata
        $movement->stock_id = $stock->id;

        // Eğer stock batch id varsa, movement'a da ekle
        if ($stock->stock_batch_id && !$movement->stock_batch_id) {
            $movement->stock_batch_id = $stock->stock_batch_id;
        }

        // Stok miktarını artır (base_quantity kullan)
        $quantityToAdd = $movement->base_quantity ?: $movement->quantity;
        $stock->quantity += $quantityToAdd;
        $stock->save();
    }

    private function processStockOut($movement)
    {
        // Base quantity'yi kullan
        $quantityToDeduct = $movement->base_quantity ?: $movement->quantity;

        // Seri numarası belirtilmişse kontrol et
        if (isset($movement->serial_numbers) && !empty($movement->serial_numbers)) {
            $serialNumbers = json_decode($movement->serial_numbers, true);

            if (is_array($serialNumbers) && count($serialNumbers) > 0) {
                foreach ($serialNumbers as $serialNumber) {
                    // Seri numarasını bul
                    $serial = StockSerialNumber::where('serial_number', $serialNumber)
                        ->where('status', 1) // Stokta
                        ->where('is_active', 1)
                        ->first();

                    if ($serial) {
                        // Seri numarasını güncelle
                        $serial->status = 2; // Satıldı
                        $serial->stock_movement_id = $movement->id;
                        $serial->notes = 'Çıkış yapıldı: ' . $movement->id;
                        $serial->updated_by = Auth::user()->id;
                        $serial->save();
                    }
                }
            }
        }

        // Belirli bir stock_id varsa ve geçerli bir stok ise direkt kullan
        if ($movement->stock_id) {
            $stock = Stock::find($movement->stock_id);

            if ($stock) {
                // Kullanılabilir stoğu hesapla
                $availableQuantity = $this->calculateAvailableQuantity($stock);

                if ($availableQuantity < $quantityToDeduct) {
                    // Yetersiz stok, FIFO'ya geç
                    $this->processStockOutWithFIFO($movement);
                } else {
                    // Yeterli stok var, direkt çıkış yap
                    $stock->quantity -= $quantityToDeduct;
                    $stock->save();

                    // Seri numarası takibi varsa stok batch ID'yi kullan
                    if ($stock->stock_batch_id && !$movement->stock_batch_id) {
                        $movement->stock_batch_id = $stock->stock_batch_id;
                    }
                }
            } else {
                // Stock bulunamadı, FIFO'ya geç
                $this->processStockOutWithFIFO($movement);
            }
        }
        // stock_id yoksa FIFO mantığıyla otomatik seçim yap
        else {
            $this->processStockOutWithFIFO($movement);
        }
    }

    private function processStockOutWithFIFO($movement)
    {
        // Base quantity'yi kullan
        $quantityToDeduct = $movement->base_quantity ?: $movement->quantity;

        // Debug için hareket bilgilerini loglayalım
        Log::info('FIFO işlemi başlatıldı:', [
            'product_id' => $movement->product_id,
            'variant_id' => $movement->variant_id,
            'warehouse_id' => $movement->warehouse_id,
            'location_id' => $movement->location_id,
            'quantity' => $quantityToDeduct
        ]);

        // FIFO mantığıyla uygun stokları bul
        $query = Stock::where('product_id', $movement->product_id)
            ->where('warehouse_id', $movement->warehouse_id)
            ->where('quantity', '>', 0)
            ->where('is_active', 1);

        // Lokasyon kontrolü
        if ($movement->location_id) {
            $query->where('warehouse_location_id', $movement->location_id);
        }

        // Varyant kontrolü
        if ($movement->variant_id) {
            $query->where('variant_id', $movement->variant_id);
        } else {
            $query->whereNull('variant_id');
        }

        // Eğer belirli bir batch ID varsa, onu kullan
        if ($movement->stock_batch_id) {
            $query->where('stock_batch_id', $movement->stock_batch_id);
        }

        $stocks = $query->with(['stockBatch'])
            ->orderBy('created_at', 'asc') // Önce oluşturulma tarihine göre
            ->get();

        Log::info('Bulunan stok sayısı: ' . $stocks->count());

        // Eğer hiç stok bulunamazsa hata ver
        if ($stocks->isEmpty()) {
            throw new \Exception('Seçilen kriterlere uygun stok bulunamadı. Ürün: ' . $movement->product_id . ', Depo: ' . $movement->warehouse_id . ', Lokasyon: ' . ($movement->location_id ?: 'Tümü'));
        }

        // Stock batch varsa son kullanma tarihine göre sırala
        $stocks = $stocks->sortBy(function($stock) {
            if ($stock->stockBatch && $stock->stockBatch->expiry_date) {
                return $stock->stockBatch->expiry_date;
            }
            return $stock->created_at;
        });

        $remainingQuantity = $quantityToDeduct;
        $processedStocks = [];

        foreach ($stocks as $stock) {
            $availableQuantity = $this->calculateAvailableQuantity($stock);

            if ($availableQuantity <= 0) {
                continue;
            }

            $quantityToTake = min($availableQuantity, $remainingQuantity);

            // Bu stoktan çıkış yap
            $stock->quantity -= $quantityToTake;
            $stock->save();

            // İşlenen stokları kaydet
            $processedStocks[] = [
                'stock_id' => $stock->id,
                'quantity' => $quantityToTake,
                'batch_id' => $stock->stock_batch_id
            ];

            $remainingQuantity -= $quantityToTake;

            if ($remainingQuantity <= 0) {
                break;
            }
        }

        if ($remainingQuantity > 0) {
            // İşlem başarısız oldu, geri al
            foreach ($processedStocks as $processed) {
                $stock = Stock::find($processed['stock_id']);
                $stock->quantity += $processed['quantity'];
                $stock->save();
            }

            throw new \Exception('Yetersiz kullanılabilir stok. Eksik miktar: ' . $remainingQuantity);
        }

        // Eğer hiç stok işlenmemişse
        if (empty($processedStocks)) {
            throw new \Exception('Hiçbir stok işlenemiyor. Tüm stoklar rezerve olabilir.');
        }

        // Birden fazla stoktan çıkış yapıldıysa bunu kaydet
        $movement->notes = $movement->notes . "\nFIFO çıkışı yapıldı. İşlenen stoklar: " . json_encode($processedStocks);

        // İlk stock_id'yi ana hareket için kaydet
        $movement->stock_id = $processedStocks[0]['stock_id'];

        // İlk stock'un batch id'sini movement'a ekle
        if (!empty($processedStocks[0]['batch_id']) && !$movement->stock_batch_id) {
            $movement->stock_batch_id = $processedStocks[0]['batch_id'];
        }
    }

    private function calculateAvailableQuantity($stock)
    {
        // Rezerve edilmiş miktarları hesapla
        $reservedQuantity = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            })
            ->sum('quantity');

        // Kullanılabilir stok = Toplam stok - Rezerve edilmiş stok
        return $stock->quantity - $reservedQuantity;
    }

    private function processStockTransfer($movement)
    {
        // Base quantity'yi kullan
        $quantityToTransfer = $movement->base_quantity ?: $movement->quantity;

        // Seri numarası belirtilmişse kontrol et
        if (isset($movement->serial_numbers) && !empty($movement->serial_numbers)) {
            $serialNumbers = json_decode($movement->serial_numbers, true);

            if (is_array($serialNumbers) && count($serialNumbers) > 0) {
                foreach ($serialNumbers as $serialNumber) {
                    // Seri numarasını bul
                    $serial = StockSerialNumber::where('serial_number', $serialNumber)
                        ->where('status', 1) // Stokta
                        ->where('is_active', 1)
                        ->first();

                    if ($serial) {
                        // Seri numarasını güncelle - lokasyon değişikliği
                        $serial->warehouse_id = $movement->target_warehouse_id;
                        $serial->warehouse_location_id = $movement->target_location_id;
                        $serial->stock_movement_id = $movement->id;
                        $serial->notes = 'Transfer yapıldı: ' . $movement->id;
                        $serial->updated_by = Auth::user()->id;
                        $serial->save();
                    }
                }
            }
        }

        // Transfer işleminde FIFO veya belirli stock_id kullan
        if (!$movement->stock_id) {
            // FIFO ile kaynak stok bul
            $this->processStockOutWithFIFO($movement);

            // Movement'a atanan stock_id'yi kullan
            $sourceStock = Stock::find($movement->stock_id);
        } else {
            $sourceStock = Stock::find($movement->stock_id);

            if (!$sourceStock) {
                throw new \Exception('Transfer yapılacak stok bulunamadı');
            }

            $availableQuantity = $this->calculateAvailableQuantity($sourceStock);

            if ($availableQuantity < $quantityToTransfer) {
                throw new \Exception('Yetersiz kullanılabilir stok. Mevcut: ' . $availableQuantity . ', İstenen: ' . $quantityToTransfer);
            }

            // Kaynak depodan çıkış
            $sourceStock->quantity -= $quantityToTransfer;
            $sourceStock->save();
        }

        // Hedef depoda yeni stok kaydı oluştur
        $targetStock = Stock::create([
            'product_id' => $sourceStock->product_id,
            'variant_id' => $sourceStock->variant_id,
            'warehouse_id' => $movement->target_warehouse_id,
            'warehouse_location_id' => $movement->target_location_id,
            'stock_batch_id' => $sourceStock->stock_batch_id,
            'quantity' => $quantityToTransfer,
            'is_active' => 1,
            'created_by' => Auth::user()->id
        ]);
    }

    private function reverseStockMovement($movement)
    {
        // Hareket türü kontrolü
        $movementType = $movement->stockMovementReason->movement_type_id;

        switch ($movementType) {
            case 1: // Giriş geri al
                $this->reverseStockIn($movement);
                break;

            case 2: // Çıkış geri al
                $this->reverseStockOut($movement);
                break;

            case 3: // Transfer geri al
                $this->reverseStockTransfer($movement);
                break;
        }
    }

    private function reverseStockIn($movement)
    {
        if (!$movement->stock_id) {
            return;
        }

        $stock = Stock::find($movement->stock_id);
        if (!$stock) {
            return;
        }

        // Seri numaralarını iptal et
        if ($movement->stock_batch_id) {
            StockSerialNumber::where('stock_batch_id', $movement->stock_batch_id)
                ->where('warehouse_id', $stock->warehouse_id)
                ->where('warehouse_location_id', $stock->warehouse_location_id)
                ->where('status', 1) // Stokta
                ->update([
                    'status' => 4, // Kayıp
                    'notes' => 'İptal edilen giriş hareketi: ' . $movement->id,
                    'updated_by' => Auth::user()->id
                ]);
        }

        // Girişi geri al (stoktan çıkar) - base_quantity kullan
        $quantityToReverse = $movement->base_quantity ?: $movement->quantity;
        $stock->quantity -= $quantityToReverse;
        $stock->save();

        // Eğer batch varsa, batch'in miktarını da azalt
        if ($movement->stock_batch_id) {
            $batch = StockBatch::find($movement->stock_batch_id);
            if ($batch) {
                $batch->current_quantity -= $quantityToReverse;
                $batch->save();
            }
        }
    }

    private function reverseStockOut($movement)
    {
        // Base quantity'yi kullan
        $quantityToReverse = $movement->base_quantity ?: $movement->quantity;

        // Seri numaralarını geri al
        StockSerialNumber::where('stock_movement_id', $movement->id)
            ->where('status', 2) // Satıldı
            ->update([
                'status' => 1, // Stokta geri al
                'notes' => 'İptal edilen çıkış hareketi: ' . $movement->id,
                'updated_by' => Auth::user()->id
            ]);

        // FIFO çıkışı yapılmışsa notes'tan bilgi al
        if (strpos($movement->notes, 'FIFO çıkışı yapıldı') !== false) {
            preg_match('/.+İşlenen stoklar: (.+)/', $movement->notes, $matches);
            if (isset($matches[1])) {
                $processedStocks = json_decode($matches[1], true);
                foreach ($processedStocks as $processed) {
                    $stock = Stock::find($processed['stock_id']);
                    if ($stock) {
                        $stock->quantity += $processed['quantity'];
                        $stock->save();
                    }
                }
            }
        } else {
            // Normal stock_id ile çıkış
            if (!$movement->stock_id) {
                return;
            }

            $stock = Stock::find($movement->stock_id);
            if (!$stock) {
                return;
            }

            // Çıkışı geri al (stoğa ekle)
            $stock->quantity += $quantityToReverse;
            $stock->save();
        }

        // Eğer batch varsa, batch'in miktarını da güncelle
        if ($movement->stock_batch_id) {
            $batch = StockBatch::find($movement->stock_batch_id);
            if ($batch) {
                $batch->current_quantity += $quantityToReverse;
                $batch->save();
            }
        }
    }

    private function reverseStockTransfer($movement)
    {
        // Seri numaralarını eski lokasyona geri al
        StockSerialNumber::where('stock_movement_id', $movement->id)
            ->update([
                'warehouse_id' => $movement->warehouse_id,
                'warehouse_location_id' => $movement->location_id,
                'notes' => 'İptal edilen transfer hareketi: ' . $movement->id,
                'updated_by' => Auth::user()->id
            ]);

        // FIFO ile çıkış yapılmışsa önce onu geri al
        $this->reverseStockOut($movement);

        // Base quantity'yi kullan
        $quantityToReverse = $movement->base_quantity ?: $movement->quantity;

        // Hedef depoda oluşturulan stok kaydını bul ve miktarı düş
        if ($movement->stock_id) {
            $sourceStock = Stock::find($movement->stock_id);
            if ($sourceStock) {
                $targetStock = Stock::where('product_id', $sourceStock->product_id)
                    ->where('variant_id', $sourceStock->variant_id)
                    ->where('warehouse_id', $movement->target_warehouse_id)
                    ->where('warehouse_location_id', $movement->target_location_id)
                    ->where('stock_batch_id', $sourceStock->stock_batch_id)
                    ->where('created_at', '>=', $movement->approval_date)
                    ->first();

                if ($targetStock) {
                    $targetStock->quantity -= $quantityToReverse;
                    $targetStock->save();
                }
            }
        }
    }

    public function saveHook($request)
    {
        $params = $request->all();

        // Hareket tarihi yoksa bugünün tarihini ata
        if (!isset($params['movement_date'])) {
            $params['movement_date'] = now();
        }

        // Başlatan kişiyi ata
        $params['starter_id'] = Auth::user()->id;

        // Varsayılan status (beklemede)
        if (!isset($params['status_id'])) {
            $params['status_id'] = 1; // Beklemede
        }

        // Seri numaralarını ve batch bilgilerini işle
        if (isset($params['serial_numbers']) && !empty($params['serial_numbers'])) {
            $serialNumbers = json_decode($params['serial_numbers'], true);
            $params['serial_numbers'] = json_encode($serialNumbers);
        }

        if (isset($params['batch_data']) && !empty($params['batch_data'])) {
            $batchData = json_decode($params['batch_data'], true);
            $params['batch_data'] = json_encode($batchData);
        }

        // Stock_id kontrolü - FIFO seçildiyse ve stock_id boşsa ilk uygun stoğu bul
        if (!isset($params['stock_id']) || empty($params['stock_id'])) {
            if (isset($params['stock_movement_reason_id'])) {
                $reason = StockMovementReason::find($params['stock_movement_reason_id']);

                // Giriş hareketi için
                if ($reason && $reason->movement_type_id == 1) {
                    // Yeni girişler için mevcut stoğu bul veya yeni oluşturulacak
                    $existingStock = Stock::where('product_id', $params['product_id'])
                        ->where('warehouse_id', $params['warehouse_id'])
                        ->where('warehouse_location_id', $params['location_id'])
                        ->where('variant_id', $params['variant_id'])
                        ->whereNull('stock_batch_id') // Batch olmayan stok
                        ->first();

                    if ($existingStock) {
                        $params['stock_id'] = $existingStock->id;
                    } else {
                        // Geçici olarak ilk stoğu kullan, onay sırasında yeni oluşturulacak
                        $firstStock = Stock::first();
                        $params['stock_id'] = $firstStock ? $firstStock->id : 1;
                    }
                }
                // Çıkış veya transfer hareketi için
                elseif ($reason && in_array($reason->movement_type_id, [2, 3])) {
                    // FIFO için ilk uygun stoğu bul
                    $firstStock = Stock::where('product_id', $params['product_id'])
                        ->where('warehouse_id', $params['warehouse_id'])
                        ->when(isset($params['location_id']), function($query) use ($params) {
                            return $query->where('warehouse_location_id', $params['location_id']);
                        })
                        ->when(isset($params['variant_id']), function($query) use ($params) {
                            return $query->where('variant_id', $params['variant_id']);
                        }, function($query) {
                            return $query->whereNull('variant_id');
                        })
                        ->where('quantity', '>', 0)
                        ->where('is_active', 1)
                        ->orderBy('created_at', 'asc')
                        ->first();

                    if ($firstStock) {
                        $params['stock_id'] = $firstStock->id;
                    } else {
                        throw new \Exception('Seçilen kriterlere uygun stok bulunamadı');
                    }
                }
            }
        }

        // Ürün seçildiyse unit_id ve base_quantity'yi ayarla
        if (isset($params['product_id'])) {
            $product = Product::find($params['product_id']);
            if ($product) {
                // Eğer bir birim seçilmişse, bu birimi kullan
                if (!isset($params['unit_id']) || !$params['unit_id']) {
                    $params['unit_id'] = $product->unit_id;
                }

                // Base quantity hesaplaması
                if (isset($params['unit_id']) && $params['unit_id'] && isset($params['quantity'])) {
                    // Seçilen birim ürünün ana birimi mi?
                    if ($params['unit_id'] == $product->unit_id) {
                        // Ana birimde, dönüşüm gerekmiyor
                        $params['base_quantity'] = $params['quantity'];
                    } else {
                        // Dönüşüm gerekiyor, product_unit_conversions tablosundan bul
                        $conversion = ProductUnitConversion::where('product_id', $params['product_id'])
                            ->where(function($query) use ($params) {
                                $query->whereNull('variant_id')
                                    ->orWhere('variant_id', $params['variant_id'] ?? null);
                            })
                            ->where(function($query) use ($params, $product) {
                                // From unit_id = seçilen birim ve to_unit_id = ürünün ana birimi
                                $query->where('from_unit_id', $params['unit_id'])
                                    ->where('to_unit_id', $product->unit_id);
                            })
                            ->first();

                        if ($conversion) {
                            // Dönüşüm bulundu, base quantity'yi hesapla
                            $params['base_quantity'] = $params['quantity'] * $conversion->conversion_factor;
                        } else {
                            // Ters dönüşümü kontrol et
                            $reverseConversion = ProductUnitConversion::where('product_id', $params['product_id'])
                                ->where(function($query) use ($params) {
                                    $query->whereNull('variant_id')
                                        ->orWhere('variant_id', $params['variant_id'] ?? null);
                                })
                                ->where(function($query) use ($params, $product) {
                                    // From unit_id = ürünün ana birimi ve to_unit_id = seçilen birim
                                    $query->where('from_unit_id', $product->unit_id)
                                        ->where('to_unit_id', $params['unit_id']);
                                })
                                ->first();

                            if ($reverseConversion) {
                                // Ters dönüşüm bulundu, base quantity'yi hesapla
                                $params['base_quantity'] = $params['quantity'] / $reverseConversion->conversion_factor;
                            } else {
                                // Dönüşüm bulunamadı, quantity'yi kullan
                                $params['base_quantity'] = $params['quantity'];
                            }
                        }
                    }
                }
            }
        }

        return $params;
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('stock_id', function ($item) {
            if ($item->stock) {
                $stockInfo = $item->stock->product->name;
                if ($item->stock->variant) {
                    $stockInfo .= ' - ' . $item->stock->variant->name;
                }
                if ($item->stock->stockBatch) {
                    $stockInfo .= ' (Lot: ' . $item->stock->stockBatch->lot_number . ')';
                }
                return $stockInfo;
            } else if ($item->product) {
                $stockInfo = $item->product->name;
                if ($item->variant) {
                    $stockInfo .= ' - ' . $item->variant->name;
                }
                return $stockInfo;
            }
            return '-';
        })
        ->editColumn('movement_date', function ($item) {
            return (!is_null($item->movement_date) ? $item->movement_date->format('d.m.Y H:i') : '-');
        })
        ->editColumn('batch_info', function ($item) {
            if ($item->stockBatch) {
                $info = 'Lot: ' . $item->stockBatch->lot_number;
                if ($item->stockBatch->batch_number) {
                    $info .= ' / Batch: ' . $item->stockBatch->batch_number;
                }

                // Seri numaralarını kontrol et
                $serialCount = StockSerialNumber::where('stock_batch_id', $item->stockBatch->id)
                    ->where('status', 1) // Stokta olanlar
                    ->where('is_active', 1)
                    ->count();

                if ($serialCount > 0) {
                    $info .= ' <span class="badge bg-info">SN: ' . $serialCount . '</span>';
                }

                return $info;
            }
            return '-';
        });
    }
}
