<?php

namespace App\Http\Controllers\Backend;

use App\Models\Current;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Stock;
use App\Models\StockReservation;
use App\Models\StockReservationReason;
use App\Models\StockReservationType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockReservationController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Stok Rezervasyonları';
        $this->page = 'stock_reservation';
        $this->model = new StockReservation();
        $this->relation = ['stock', 'product', 'variant', 'current', 'reservationReason', 'reservationType'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Stok Yönetimi' => '#',
                'Stok Rezervasyonları' => route('backend.stock_reservation_list'),
            ),
        );

        view()->share('stocks', Stock::with(['product', 'variant'])->active()->get());
        view()->share('products', Product::active()->get());
        view()->share('variants', ProductVariant::active()->get());
        view()->share('currents', Current::active()->get());
        view()->share('reservationReasons', StockReservationReason::active()->get());
        view()->share('reservationTypes', StockReservationType::active()->get());

        parent::__construct();
    }

    public function saveHook($request)
    {
        $params = $request->all();

        // Start date yoksa bugünü ata
        if (!isset($params['start_date'])) {
            $params['start_date'] = now();
        }

        // Rezervasyon yapılacak stok kontrolü
        if (isset($params['stock_id'])) {
            $stock = Stock::find($params['stock_id']);

            if (!$stock) {
                throw new \Exception('Stok kaydı bulunamadı');
            }

            $availableQuantity = $this->calculateAvailableQuantity($stock, $params['id'] ?? null);

            if ($availableQuantity < $params['quantity']) {
                throw new \Exception('Yetersiz kullanılabilir stok. Mevcut: ' . $availableQuantity . ', İstenen: ' . $params['quantity']);
            }

            // Product ve variant bilgilerini otomatik doldur
            $params['product_id'] = $stock->product_id;
            $params['variant_id'] = $stock->variant_id;
        }

        return $params;
    }

    private function calculateAvailableQuantity($stock, $excludeReservationId = null)
    {
        // Rezerve edilmiş miktarları hesapla
        $query = StockReservation::where('stock_id', $stock->id)
            ->where('is_active', 1)
            ->where(function($query) {
                $query->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });

        // Güncelleme durumunda mevcut rezervasyonu hariç tut
        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }

        $reservedQuantity = $query->sum('quantity');

        // Kullanılabilir stok = Toplam stok - Rezerve edilmiş stok
        return $stock->quantity - $reservedQuantity;
    }

    public function status(Request $request)
    {
        $reservation = StockReservation::find($request->id);

        if (!$reservation) {
            return response()->json(['status' => false, 'message' => 'Rezervasyon bulunamadı']);
        }

        DB::beginTransaction();

        try {
            // Eğer status 0 yapılıyorsa (iptal ediliyorsa)
            if ($request->status == 0) {
                $reservation->is_active = 0;
                $reservation->end_date = now();
                $reservation->notes = $reservation->notes . "\nRezervasyon iptal edildi: " . now();
            }
            // Eğer status 1 yapılıyorsa (aktif ediliyorsa)
            else if ($request->status == 1) {
                // Kullanılabilir stok kontrolü yap
                $availableQuantity = $this->calculateAvailableQuantity($reservation->stock, $reservation->id);

                if ($availableQuantity < $reservation->quantity) {
                    throw new \Exception('Yetersiz kullanılabilir stok. Mevcut: ' . $availableQuantity . ', İstenen: ' . $reservation->quantity);
                }

                $reservation->is_active = 1;
                $reservation->end_date = null;
                $reservation->notes = $reservation->notes . "\nRezervasyon tekrar aktif edildi: " . now();
            }

            $reservation->save();

            DB::commit();
            return response()->json(['status' => true, 'message' => 'Rezervasyon durumu güncellendi']);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => 'Hata oluştu: ' . $e->getMessage()]);
        }
    }

    public function datatableHook($obj)
    {
        return $obj->editColumn('available_quantity', function ($item) {
            if ($item->stock) {
                return $this->calculateAvailableQuantity($item->stock);
            }
            return '-';
        });
    }
}
