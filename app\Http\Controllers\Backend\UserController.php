<?php

namespace App\Http\Controllers\Backend;

use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Kullanıcı';
        $this->page = 'user';
        $this->model = new User();
        $this->relation = ['role'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Ayarlar' => '#',
                'Kullanıcılar' => route('backend.user_list'),
            ),
        );
        view()->share('roles', Role::get());
        parent::__construct();
    }

    public function save(Request $request, $unique = NULL)
    {
        Validator::make(
            $request->all(),
            [
                'email' => ['required', 'email', 'min:10', 'max:191', 'regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix'],
                'title' => 'required|min:3',
                'name' => 'required|min:2|max:80',
                'surname' => 'required|min:2|max:80',
                'phone' => 'required|max:15|min:15',
                'is_active' => 'in:0,1',
                'role_id' => 'required|exists:roles,id',
            ],
            [
                'email.required' => 'Email adresi girilmesi gerekiyor',
                'email.email'    => 'Email formatında girmeniz gerekiyor.',
                'email.min'    => 'Email 10 karakterden fazla olmalıdır.',
                'email.regex'    => 'Email formatında girmeniz gerekiyor.',
                'name.required' => 'Ad adresi girilmesi gerekiyor',
                'name.min' => 'Ad 2 karakterden fazla olmalıdır.',
                'name.max' => 'Ad maximum 80 karakter olmalıdır.',
                'surname.required' => 'Soyad adresi girilmesi gerekiyor',
                'surname.min' => 'Soyad 2 karakterden fazla olmaldır.',
                'surname.max' => 'Soyad maximum 80 karakter olmaldır.',
                'title.required' => 'Ünvan adresi girilmesi gerekiyor',
                'title.min' => 'Ünvan 3 karakterden fazla olmalıdır.',
                'phone.min' => 'Telefon numarası hatalı',
                'phone.max' => 'Telefon numarası hatalı',
                'is_active' => 'Aktif veya Pasif Seçiniz.',
                'role_id.exists' => 'Rolünüzü Seçiniz.',

            ]
        )->validate();

        $params = $request->all();

        if (is_null($unique)) {
            // Süper Admin rolü atama kontrolü - Sadece süper adminler (rol_id=1) süper admin rolü atayabilir
            if ($request->role_id == 1 && Auth::user()->role_id != 1) {
                return back()->with('warning', 'Super Admin rolü atanamaz')->withInput();
            }

            Validator::make(
                $request->all(),
                [
                    'password' => 'required|min:6|max:12',
                    'email' => 'required|email|min:10|max:50|unique:users,email',
                ],
                [
                    'email.required' => 'Email zorunludur.',
                    'email.unique' => 'Bu email adresi zaten kayıtlı',
                    'email.email' => 'Email adresinizi kontrol ediniz',
                    'email.min'    => 'Email 10 karakterden fazla olmalıdır.',
                    'email.max' => 'Email maximum 50 karakter olabilir.',
                    'password.required' => 'Şifre girmelisiniz',
                    'password.min' => 'Şifre en az 6 karakter olabilir.',
                    'password.max' => 'Şifre en fazla 12 karakter girmelisiniz',

                ]
            )->validate();
            $params['password'] = Hash::make($params['password']);
            $obj = $this->model::create($params);
        } else {

            $obj = $this->model::find((int)$unique);

            // Düzenleme için kontrol - Süper Admin kullanıcısı sadece kendisini düzenleyebilir
            if ($obj->role_id == 1 && $obj->id != Auth::id()) {
                return back()->with('warning', 'Bu kullanıcı düzenlenemez')->withInput();
            }

            // Kullanıcı kendisine süper admin rolünü vermeye çalışıyorsa engelle
            if ($obj->id == Auth::id() && $request->role_id == 1 && $obj->role_id != 1) {
                return back()->with('warning', 'Kendinize Super Admin rolü veremezsiniz')->withInput();
            }

            $check_email = User::where('email', $request->email)->where('id', '<>', (int)$unique)->first();
            if (!is_null($check_email)) {
                return back()->with('warning', 'Bu eposta hesabı zaten kayıtlı')->withInput();
            }

            if (!is_null($params['password']) && $params['password'] != '') {
                $params['password'] = Hash::make($params['password']);
            } else {
                unset($params['password']);
            }

            $obj->update($params);
        }
        $message = is_null($unique) ? 'Kayıt başarılı şekilde eklendi' : 'Kayıt başarılı şekilde güncellendi';
        return redirect()->route("backend.user_list")->with('success', $message);
    }

    public function profile(Request $request)
    {
        $item = User::find(Auth::user()->id);
        return view('backend.user.profile', compact('item'));
    }

    public function profile_save(Request $request)
    {
        $user = User::find(Auth::user()->id);
        $params = $request->all();
        $request->validate(
            [
                'name' => 'required|min:2|max:80',
                'surname' => 'required|min:2|max:80',
                'email' => 'required|email|min:10|max:191|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix',
                'phone' => 'required',
            ],
            [
                'name.required' => 'Ad boş geçilemez',
                'name.min' => 'Ad minimum 2 karakter olmalıdır.',
                'name.max' => 'Ad maksimum 80 karakter olmalıdır.',
                'surname.required' => 'Soyad boş geçilemez',
                'surname.min' => 'Soyad minimum 2 karakter olmalıdır.',
                'surname.max' => 'Soyad maksimum 80 karakter olmalıdır.',
                'email.required' => 'E-mail boş geçilemez',
                'email.min' => 'E-mail minimum 10 karakter olmalıdır.',
                'email.max' => 'E-mail maksimum 191 karakter olmalıdır.',
                'email.email' => 'E-mail adresini kontrol ediniz.',
                'phone.required' => 'Telefon boş geçilemez'
            ]
        );
        $user->update($params);

        return redirect()->route("backend.profile")->with('success', 'Profil bilgileriniz kaydedildi');
    }

    public function delete(Request $request)
    {
        $userId = (int) $request->post('id');
        $currentUserId = Auth::id();

        // Kullanıcı kendisini silmeye çalışıyorsa
        if ($userId === $currentUserId) {
            return response()->json(['status' => false, 'message' => 'Kendinizi silemezsiniz.']);
        }

        $user = $this->model::find($userId);

        if (!is_null($user) && $user->role_id == 1) {
            return response()->json(['status' => false, 'message' => 'Bu kayıtlar silinemez.']);
        }

        if (!is_null($user)) {
            $obj = $user;
            $obj->delete();
        } else {
            return response()->json(['status' => false, 'message' => 'Kayıt bulunamadı']);
        }

        return response()->json(['status' => true]);
    }

    public function password(Request $request)
    {
        $user = User::find(Auth::user()->id);
        if (is_null($user)) {
            return redirect()->back()->with('warning', 'Kayıt bulunamadı');
        }

        $request->validate([
            'current_password' => 'required',
            'password' => 'required|min:6|max:12',
            'confirm_password' => 'required|min:6|max:12|same:password',
        ]);

        // Mevcut şifre kontrolü
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->with('error', 'Mevcut şifreniz yanlış');
        }

        $user->password = Hash::make($request->password);
        $user->save();

        return redirect()->route("backend.profile")->with('success', 'Şifre başarılı şekilde güncellendi');
    }
}
