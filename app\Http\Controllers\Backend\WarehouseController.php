<?php

namespace App\Http\Controllers\Backend;

use App\Models\Warehouse;
use Illuminate\Http\Request;

class WarehouseController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Depolar';
        $this->page = 'warehouse';
        $this->model = new Warehouse();
        $this->relation = ['locations'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Depolar' => route('backend.warehouse_list'),
            ),
        );
        
        parent::__construct();
    }

    public function detail(Request $request, $warehouse_id = null, $unique = null)
    {
        $warehouse = Warehouse::with(['locations', 'stocks'])->find($warehouse_id);
        
        if (!$warehouse) {
            return redirect()->route('backend.warehouse_list')->with('error', 'Depo bulunamadı');
        }

        return view("backend.$this->page.detail", compact('warehouse'));
    }
}
