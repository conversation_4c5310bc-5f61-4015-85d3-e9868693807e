<?php

namespace App\Http\Controllers\Backend;

use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Http\Request;

class WarehouseLocationController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Depo Lokasyonları';
        $this->page = 'warehouse_location';
        $this->model = new WarehouseLocation();
        $this->relation = ['warehouse', 'parent'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Depo Lokasyonları' => route('backend.warehouse_location_list'),
            ),
        );

        view()->share('warehouses', Warehouse::active()->get());
        
        parent::__construct();
    }

    public function detail(Request $request, $warehouse_location_id = null, $unique = null)
    {
        $location = WarehouseLocation::with(['warehouse', 'stocks'])->find($warehouse_location_id);
        
        if (!$location) {
            return redirect()->route('backend.warehouse_location_list')->with('error', 'Depo lokasyonu bulunamadı');
        }

        return view("backend.$this->page.detail", compact('location'));
    }
}
