<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class AccountVoucherRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'voucher_date' => 'required|date',
            'current_id' => 'required|exists:current,id',
            'voucher_type' => 'required|string',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string',
            'exchange_rate' => 'required|numeric',
            'description' => 'nullable|string',
            'reference_no' => 'nullable|string',
            'due_date' => 'nullable|date',
            'bank_name' => 'nullable|string',
            'bank_branch' => 'nullable|string',
            'account_no' => 'nullable|string',
            'is_active' => 'nullable|in:0,1',
            'document_file' => 'nullable|file|max:10240',
        ];
    }

    public function attributes()
    {
        return [
            'voucher_date' => 'Fiş Tarihi',
            'current_id' => 'Cari <PERSON>',
            'voucher_type' => 'Fiş Tipi',
            'amount' => 'Tutar',
            'currency' => 'Para Birimi',
            'exchange_rate' => 'Döviz Kuru',
            'description' => 'Açıklama',
            'reference_no' => 'Referans No',
            'due_date' => 'Vade Tarihi',
            'bank_name' => 'Banka Adı',
            'bank_branch' => 'Banka Şubesi',
            'account_no' => 'Hesap No',
            'is_active' => 'Durum',
            'document_file' => 'Belge Dosyası',
        ];
    }

    public function messages()
    {
        return [
            'voucher_date.required' => 'Fiş tarihi alanı zorunludur.',
            'voucher_date.date' => 'Fiş tarihi geçerli bir tarih olmalıdır.',
            'current_id.required' => 'Cari hesap seçilmelidir.',
            'current_id.exists' => 'Geçersiz cari hesap.',
            'voucher_type.required' => 'Fiş tipi seçilmelidir.',
            'amount.required' => 'Tutar alanı zorunludur.',
            'amount.numeric' => 'Tutar sayısal bir değer olmalıdır.',
            'amount.min' => 'Tutar en az 0.01 olmalıdır.',
            'currency.required' => 'Para birimi seçilmelidir.',
            'exchange_rate.required' => 'Döviz kuru alanı zorunludur.',
            'exchange_rate.numeric' => 'Döviz kuru sayısal bir değer olmalıdır.',
            'due_date.date' => 'Vade tarihi geçerli bir tarih olmalıdır.',
            'is_active.in' => 'Durum değeri geçersiz.',
            'document_file.file' => 'Belge dosyası geçerli bir dosya olmalıdır.',
            'document_file.max' => 'Belge dosyası en fazla 10MB olabilir.',
        ];
    }
}
