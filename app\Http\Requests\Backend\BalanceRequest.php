<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class BalanceRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'current_id' => 'required|integer|exists:currents,id',
            'debit_balance' => 'required|numeric|min:0',
            'credit_balance' => 'required|numeric|min:0',
            'is_active' => 'required|in:0,1',
        ];
    }

    public function attributes()
    {
        return [
            'current_id' => 'Cari',
            'debit_balance' => 'Borç Bakiyesi',
            'credit_balance' => 'Alacak Bakiyesi',
            'is_active' => 'Durum',
        ];
    }

    public function messages()
    {
        return [
            'current_id.required' => 'Lütfen bir cari seçiniz.',
            'current_id.exists' => 'Seçilen cari bulunamadı.',
            'debit_balance.required' => 'Borç bakiyesi alanı zorunludur.',
            'debit_balance.numeric' => '<PERSON>rç bakiyesi sayısal bir değer olmalıdır.',
            'debit_balance.min' => 'Borç bakiyesi negatif olamaz.',
            'credit_balance.required' => 'Alacak bakiyesi alanı zorunludur.',
            'credit_balance.numeric' => 'Alacak bakiyesi sayısal bir değer olmalıdır.',
            'credit_balance.min' => 'Alacak bakiyesi negatif olamaz.',
            'is_active.required' => 'Durum alanı zorunludur.',
            'is_active.in' => 'Durum değeri geçersiz.',
        ];
    }
}
