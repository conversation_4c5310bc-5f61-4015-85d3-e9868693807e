<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class BranchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company_id' => 'required|exists:companies,id',
            'title' => 'required|string|min:3|max:255',
            'is_active' => 'required|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'company_id.required' => 'Şirket seçilmedi.',
            'title.required' => 'Şube adı girilmedi.',
            'title.min' => 'Şube adı en az 3 karakter olmalıdır.',
            'title.max' => 'Şube adı en fazla 255 karakter olmalıdır.',
            'is_active.required' => 'Durum seçilmedi.',
        ];
    }
}
