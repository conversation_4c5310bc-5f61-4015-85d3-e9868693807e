<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class BrandRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|min:3|max:255',
            'description' => 'nullable|string|max:255',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:categories,id',
            'is_active' => 'required|boolean',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Marka adı zorunludur.',
            'name.max' => 'Marka adı en fazla 255 karakter olabilir.',
            'name.min' => 'Marka adı en az 3 karakter olmalıdır.',
            'description.max' => 'Açıklama en fazla 255 karakter olabilir.',
            'description.string' => 'Açıklama metin formatında olmalıdır.',
            'categories.array' => 'Kategoriler dizi formatında olmalıdır.',
            'categories.*.exists' => 'Seçilen kategori geçerli değil.',
            'is_active.required' => 'Durum seçimi zorunludur.',
            'is_active.boolean' => 'Durum değeri geçersiz.',
        ];
    }
}
