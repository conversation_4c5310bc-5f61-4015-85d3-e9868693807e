<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class CompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|min:2|max:255',
            'email' => 'required|email|unique:companies,email',
            'phone' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ];
    }

    public function messages(): array
    {
        return [
            'title.required' => 'Şirket adı zorunludur.',
            'title.string' => 'Şirket adı metin olmalıdır.',
            'title.min' => 'Şirket adı en az 2 karakter olmalıdır.',
            'title.max' => 'Şirket adı en fazla 255 karakter olmalıdır.',
            'email.required' => 'E-posta adresi zorunludur.',
            'email.email' => 'Geçersiz e-posta adresi.',
            'email.unique' => 'Bu e-posta adresi zaten kullanılıyor.',
            'phone.required' => 'Telefon numarası zorunludur.',
            'phone.string' => 'Telefon numarası metin olmalıdır.',
            'phone.max' => 'Telefon numarası en fazla 255 karakter olmalıdır.',
            'address.required' => 'Adres zorunludur.',
            'address.string' => 'Adres metin olmalıdır.',
            'address.max' => 'Adres en fazla 255 karakter olmalıdır.',
            'image.image' => 'Resim bir görsel olmalıdır.',
            'image.mimes' => 'Resim formatı geçersiz.',
            'image.max' => 'Resim en fazla 2MB olmalıdır.',
        ];
    }
}
