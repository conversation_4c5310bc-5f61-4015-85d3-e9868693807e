<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class CurrentRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        // Temel validasyon kuralları
        $rules = [
            'name' => 'required|string|max:255',
            'surname' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'instutation_id' => 'required|integer|exists:insutationtype,id',
            'current_type_id' => 'required|integer|exists:CurrentType,id',
            'tax_number' => 'nullable|string|max:20',
            'is_active' => 'required|in:0,1',
            'country_id' => 'nullable|integer|exists:countries,id', // Düzeltildi: countries tablosu
            'city_id' => 'nullable|integer|exists:cities,id',
            'district_id' => 'nullable|integer|exists:districts,id',
            'neighborhood_id' => 'nullable|integer|exists:neighborhoods,id',
            'tax_offices_id' => 'nullable|integer|exists:taxoffices,id',
            'chance_limits' => 'nullable|numeric|min:0',
            'bonds_limits' => 'nullable|numeric|min:0',
            'order_limits' => 'nullable|numeric|min:0',
            'balance_limits' => 'nullable|numeric|min:0',
        ];

        // Düzenleme işlemi için ek kurallar
        if ($this->isMethod('PUT') || $this->route('unique')) {
            // Düzenleme işleminde bazı alanlar opsiyonel olabilir
            // Örneğin, şifre değiştirilmiyorsa boş bırakılabilir
        }

        return $rules;
    }

    public function attributes()
    {
        return [
            'name' => 'Ad',
            'surname' => 'Soyad',
            'email' => 'E-posta',
            'phone' => 'Telefon',
            'instutation_id' => 'Kurum Tipi',
            'current_type_id' => 'Cari Tipi',
            'tax_number' => 'Vergi Numarası',
            'is_active' => 'Durum',
            'country_id' => 'Ülke',
            'city_id' => 'Şehir',
            'district_id' => 'İlçe',
            'neighborhood_id' => 'Mahalle',
            'tax_offices_id' => 'Vergi Dairesi',
            'chance_limits' => 'Risk Limiti',
            'bonds_limits' => 'Çek Limiti',
            'order_limits' => 'Sipariş Limiti',
            'balance_limits' => 'Borç Limiti',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'Ad alanı zorunludur.',
            'instutation_id.required' => 'Lütfen bir kurum tipi seçiniz.',
            'instutation_id.exists' => 'Seçilen kurum tipi bulunamadı.',
            'current_type_id.required' => 'Lütfen bir cari tipi seçiniz.',
            'current_type_id.exists' => 'Seçilen cari tipi bulunamadı.',
            'email.email' => 'Geçerli bir e-posta adresi giriniz.',
            'tax_number.max' => 'Vergi numarası en fazla 20 karakter olabilir.',
            'is_active.required' => 'Durum alanı zorunludur.',
            'is_active.in' => 'Durum değeri geçersiz.',
            'country_id.exists' => 'Seçilen ülke bulunamadı.',
            'city_id.exists' => 'Seçilen şehir bulunamadı.',
            'district_id.exists' => 'Seçilen ilçe bulunamadı.',
            'neighborhood_id.exists' => 'Seçilen mahalle bulunamadı.',
            'tax_offices_id.exists' => 'Seçilen vergi dairesi bulunamadı.',
            'chance_limits.numeric' => 'Risk limiti sayısal bir değer olmalıdır.',
            'chance_limits.min' => 'Risk limiti negatif olamaz.',
            'bonds_limits.numeric' => 'Çek limiti sayısal bir değer olmalıdır.',
            'bonds_limits.min' => 'Çek limiti negatif olamaz.',
            'order_limits.numeric' => 'Sipariş limiti sayısal bir değer olmalıdır.',
            'order_limits.min' => 'Sipariş limiti negatif olamaz.',
            'balance_limits.numeric' => 'Borç limiti sayısal bir değer olmalıdır.',
            'balance_limits.min' => 'Borç limiti negatif olamaz.',
        ];
    }
}
