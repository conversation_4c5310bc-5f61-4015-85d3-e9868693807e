<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class CurrentTransactionRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'current_id' => 'nullable|exists:current,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'transaction_type' => 'nullable|string',
        ];
    }

    public function attributes()
    {
        return [
            'current_id' => 'Cari Hesap',
            'start_date' => 'Başlangıç Tarihi',
            'end_date' => 'Bitiş Tarihi',
            'transaction_type' => 'İşlem Tipi',
        ];
    }

    public function messages()
    {
        return [
            'current_id.exists' => 'Geçersiz cari hesap.',
            'start_date.date' => 'Başlangıç tarihi geçerli bir tarih olmalıdır.',
            'end_date.date' => 'Bitiş tarihi geçerli bir tarih olmalıdır.',
        ];
    }
}
