<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ExpenseVoucherRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'voucher_no' => 'nullable|string|max:50',
            'voucher_date' => 'required|date',
            'current_id' => 'required|exists:current,id',
            'expense_type_id' => 'required|exists:expense_types,id',
            'payment_type_id' => 'required|exists:payment_types,id',
            'description' => 'nullable|string|max:1000',
            'subtotal' => 'required|numeric|min:0',
            'vat_total' => 'nullable|numeric|min:0',
            'grand_total' => 'required|numeric|min:0',
            'attachment' => 'nullable|file|mimes:jpeg,png,jpg,pdf|max:5120',
            'auto_accounting' => 'nullable|boolean',
            'status' => 'nullable|string|in:pending,approved,rejected',
            'is_active' => 'nullable|boolean',

            'items' => 'nullable|array',
            'items.*.expense_code' => 'nullable|string|max:50',
            'items.*.description' => 'nullable|string|max:500',
            'items.*.quantity' => 'nullable|numeric|min:0.01',
            'items.*.amount' => 'nullable|numeric|min:0',
            'items.*.total' => 'nullable|numeric|min:0',
            'items.*.vat_rate' => 'nullable|numeric|min:0|max:100',
        ];
    }

    public function messages(): array
    {
        return [
            'voucher_no.required' => 'Fiş numarası zorunludur.',
            'voucher_date.required' => 'Fiş tarihi zorunludur.',
            'current_id.required' => 'Cari hesap seçimi zorunludur.',
            'expense_type_id.required' => 'Masraf türü seçimi zorunludur.',
            'payment_type_id.required' => 'Ödeme türü seçimi zorunludur.',
            'subtotal.required' => 'Ara toplam zorunludur.',
            'grand_total.required' => 'Genel toplam zorunludur.',
            'items.required' => 'En az bir kalem eklemelisiniz.',
            'items.min' => 'En az bir kalem eklemelisiniz.',
        ];
    }
}
