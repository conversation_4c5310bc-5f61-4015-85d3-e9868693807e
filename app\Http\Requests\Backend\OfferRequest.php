<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class OfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // 'branch_id'         => 'required|integer|exists:branches,id',
            'warehouse_id'      => 'required|integer|exists:warehouses,id',
            'offer_date'        => 'required|date|after_or_equal:today',
            'offer_deadline'    => 'required|date|after_or_equal:offer_date',
            'exchange_rate_id'  => 'required|integer|exists:exchange_rates,id',
            'current_id'        => 'required|integer|exists:currents,id',
            'shipping_address' => 'nullable|string|max:255',
            'payment_type_id'  => 'required|integer|exists:payment_types,id',
            'offer_type_id'     => 'nullable|integer|exists:offer_types,id',
            'notes'             => 'nullable|string',
            'products'          => 'required|array|min:1',
        ];
    }
    public function messages(): array
    {
        return [
            // 'branch_id.required'        => 'Lütfen şube seçiniz',
            'warehouse_id.required'     => 'Lütfen depo seçiniz',
            'offer_date.required'       => 'Lütfen teklif tarihi giriniz',
            'offer_date.date'           => 'Lütfen geçerli bir tarih giriniz',
            'offer_date.after_or_equal' => 'Teklif tarihi bugünden sonra olmalıdır',
            'offer_deadline.required'   => 'Lütfen teklif son tarihi giriniz',
            'offer_deadline.date'       => 'Lütfen geçerli bir tarih giriniz',
            'offer_deadline.after_or_equal' => 'Teklif son tarihi teklif tarihinden sonra olmalıdır',
            'exchange_rate_id.required' => 'Lütfen döviz seçiniz',
            'current_id.required'       => 'Lütfen cari seçiniz',
            'shipping_address.max'      => 'Sevkiyat adresi maksimum 255 karakter olmalıdır',
            'payment_type_id.required' => 'Lütfen ödeme planı seçiniz',
            'notes.string'              => 'Notlar metin formatında olmalıdır',
            'products.required'         => 'Lütfen en az bir ürün ekleyiniz.',
            'products.array'            => 'Ürünler hatalı gönderildi.',
            'products.min'              => 'Lütfen en az bir ürün ekleyiniz.',
        ];
    }
}
