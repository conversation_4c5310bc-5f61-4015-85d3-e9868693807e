<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class PurchaseInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'invoice_date' => 'required|date',
            'due_date' => 'nullable|date',
            'current_id' => 'required|exists:current,id',
            'currency_code' => 'required|string|max:3',
            'exchange_rate' => 'required|numeric',
            'net_amount' => 'required|numeric',
            'tax_amount' => 'required|numeric',
            'total_amount' => 'required|numeric',
            'foreign_currency_amount' => 'nullable|numeric',
            'items' => 'required|array',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|numeric|min:0.01',
            'items.*.sale_price' => 'required|numeric|min:0',
            'items.*.total' => 'required|numeric|min:0',
            'is_active' => 'nullable|boolean',
            'status' => 'nullable|integer|in:0,1,2,3,4',
            'description' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'invoice_date.required' => 'Fatura tarihi zorunludur.',
            'invoice_date.date' => 'Fatura tarihi geçerli bir tarih olmalıdır.',
            'due_date.date' => 'Vade tarihi geçerli bir tarih olmalıdır.',
            'current_id.required' => 'Cari hesap seçimi zorunludur.',
            'current_id.exists' => 'Seçilen cari hesap geçerli değil.',
            'currency_code.required' => 'Para birimi zorunludur.',
            'currency_code.max' => 'Para birimi en fazla 3 karakter olmalıdır.',
            'exchange_rate.required' => 'Döviz kuru zorunludur.',
            'exchange_rate.numeric' => 'Döviz kuru sayısal bir değer olmalıdır.',
            'net_amount.required' => 'Net tutar zorunludur.',
            'net_amount.numeric' => 'Net tutar sayısal bir değer olmalıdır.',
            'tax_amount.required' => 'KDV tutarı zorunludur.',
            'tax_amount.numeric' => 'KDV tutarı sayısal bir değer olmalıdır.',
            'total_amount.required' => 'Toplam tutar zorunludur.',
            'total_amount.numeric' => 'Toplam tutar sayısal bir değer olmalıdır.',
            'foreign_currency_amount.numeric' => 'Döviz tutarı sayısal bir değer olmalıdır.',
            'items.required' => 'En az bir ürün eklemelisiniz.',
            'items.array' => 'Fatura kalemleri bir dizi olmalıdır.',
            'items.*.product_id.required' => 'Ürün seçimi zorunludur.',
            'items.*.product_id.exists' => 'Seçilen ürün geçerli değil.',
            'items.*.quantity.required' => 'Miktar zorunludur.',
            'items.*.quantity.numeric' => 'Miktar sayısal bir değer olmalıdır.',
            'items.*.quantity.min' => 'Miktar en az 0.01 olmalıdır.',
            'items.*.sale_price.required' => 'Birim fiyat zorunludur.',
            'items.*.sale_price.numeric' => 'Birim fiyat sayısal bir değer olmalıdır.',
            'items.*.sale_price.min' => 'Birim fiyat en az 0 olmalıdır.',
            'items.*.total.required' => 'Toplam tutar zorunludur.',
            'items.*.total.numeric' => 'Toplam tutar sayısal bir değer olmalıdır.',
            'items.*.total.min' => 'Toplam tutar en az 0 olmalıdır.',
            'status.in' => 'Geçersiz durum değeri.',
        ];
    }
}
