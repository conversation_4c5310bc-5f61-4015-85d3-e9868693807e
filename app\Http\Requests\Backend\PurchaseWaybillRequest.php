<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PurchaseWaybillRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'waybill_date' => 'required|date',
            'current_id' => 'required|exists:current,id',
            'total_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'waybill_date' => 'İrsaliye Tarihi',
            'current_id' => 'Cari Hesap',
            'total_amount' => 'Toplam Tutar',
            'notes' => 'Notlar',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'waybill_date.required' => 'İrsaliye tarihi alanı zorunludur.',
            'current_id.required' => 'Cari hesap seçimi zorunludur.',
            'current_id.exists' => 'Seçilen cari hesap geçerli değil.',
            'total_amount.numeric' => 'Toplam tutar sayısal bir değer olmalıdır.',
            'total_amount.min' => 'Toplam tutar 0\'dan küçük olamaz.',
        ];
    }
}
