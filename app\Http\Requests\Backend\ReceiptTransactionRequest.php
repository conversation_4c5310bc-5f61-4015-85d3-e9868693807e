<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReceiptTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'transaction_date' => 'required|date',
            'invoice_id' => 'required|integer',
            'invoice_type' => 'required|integer',
            'current_id' => 'required|exists:current,id',
            'payment_method' => 'required|integer',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|max:3',
            'exchange_rate' => 'required|numeric',
            'document_no' => 'nullable|string|max:50',
            'bank_account' => 'nullable|string|max:100',
            'due_date' => 'nullable|date',
            'description' => 'nullable|string|max:500',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'transaction_date' => 'İşlem Tarihi',
            'invoice_id' => 'Fatura',
            'invoice_type' => 'Fatura Tipi',
            'current_id' => 'Cari Hesap',
            'payment_method' => 'Ödeme Yöntemi',
            'amount' => 'Tutar',
            'currency' => 'Para Birimi',
            'exchange_rate' => 'Döviz Kuru',
            'document_no' => 'Belge No',
            'bank_account' => 'Banka Hesabı',
            'due_date' => 'Vade Tarihi',
            'description' => 'Açıklama',
            'is_active' => 'Aktif/Pasif',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'transaction_date.required' => 'İşlem tarihi alanı zorunludur.',
            'invoice_id.required' => 'Fatura seçimi zorunludur.',
            'invoice_type.required' => 'Fatura tipi seçimi zorunludur.',
            'current_id.required' => 'Cari hesap seçimi zorunludur.',
            'current_id.exists' => 'Seçilen cari hesap geçerli değil.',
            'payment_method.required' => 'Ödeme yöntemi seçimi zorunludur.',
            'amount.required' => 'Tutar alanı zorunludur.',
            'amount.numeric' => 'Tutar alanı sayısal bir değer olmalıdır.',
            'amount.min' => 'Tutar alanı en az 0.01 olmalıdır.',
            'currency.required' => 'Para birimi alanı zorunludur.',
            'exchange_rate.required' => 'Döviz kuru alanı zorunludur.',
            'exchange_rate.numeric' => 'Döviz kuru alanı sayısal bir değer olmalıdır.',
        ];
    }
}
