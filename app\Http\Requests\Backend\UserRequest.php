<?php

namespace App\Http\Requests\Backend;

use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|min:2|max:255',
            'surname' => 'required|string|min:2|max:255',
            'title' => 'required|string|min:3|max:255',
            'email' => 'required|email|unique:users,email,' . $this->id,
            'password' => 'required|string|min:8',
            'phone' => 'required|string|max:255',
            'role_id' => 'required|exists:roles,id',
            'is_active' => 'required|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => 'İsim alanı zorunludur.',
            'name.string' => 'İsim alanı metin olmalıdır.',
            'name.min' => 'İsim alanı en az 2 karakter olmalıdır.',
            'name.max' => 'İsim alanı en fazla 255 karakter olmalıdır.',
            'surname.required' => 'Soyisim alanı zorunludur.',
            'surname.string' => 'Soyisim alanı metin olmalıdır.',
            'surname.min' => 'Soyisim alanı en az 2 karakter olmalıdır.',
            'surname.max' => 'Soyisim alanı en fazla 255 karakter olmalıdır.',
            'title.required' => 'Ünvan alanı zorunludur.',
            'title.string' => 'Ünvan alanı metin olmalıdır.',
            'title.min' => 'Ünvan alanı en az 3 karakter olmalıdır.',
            'title.max' => 'Ünvan alanı en fazla 255 karakter olmalıdır.',
            'email.required' => 'E-posta alanı zorunludur.',
            'email.email' => 'E-posta alanı geçerli bir e-posta adresi olmalıdır.',
            'email.unique' => 'Bu e-posta adresi zaten kullanılıyor.',
            'password.required' => 'Şifre alanı zorunludur.',
            'password.string' => 'Şifre alanı metin olmalıdır.',
            'password.min' => 'Şifre alanı en az 8 karakter olmalıdır.',
            'phone.required' => 'Telefon alanı zorunludur.',
            'phone.string' => 'Telefon alanı metin olmalıdır.',
            'phone.max' => 'Telefon alanı en fazla 255 karakter olmalıdır.',
            'role_id.required' => 'Rol alanı zorunludur.',
            'role_id.exists' => 'Geçersiz rol seçildi.',
            'is_active.required' => 'Aktiflik durumu zorunludur.',
            'is_active.boolean' => 'Aktiflik durumu geçerli bir boolean değer olmalıdır.',
        ];
    }
}
