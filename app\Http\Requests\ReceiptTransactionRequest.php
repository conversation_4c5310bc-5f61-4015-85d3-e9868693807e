<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ReceiptTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'transaction_date' => 'required|date',
            'invoice_id' => 'required|integer',
            'invoice_type' => 'required|integer|in:1,2',
            'current_id' => 'required|exists:currents,id',
            'payment_method' => 'required|exists:payment_types,id',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|in:TRY,USD,EUR,GBP',
            'exchange_rate' => 'required|numeric|min:0.0001',
            'document_no' => 'nullable|string|max:50',
            'bank_account' => 'nullable|string|max:100',
            'due_date' => 'nullable|date',
            'description' => 'nullable|string|max:500',
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'transaction_date' => 'İşlem Tarihi',
            'invoice_id' => 'Fatura',
            'invoice_type' => 'Fatura Tipi',
            'current_id' => 'Cari Hesap',
            'payment_method' => 'Ödeme Yöntemi',
            'amount' => 'Tutar',
            'currency' => 'Para Birimi',
            'exchange_rate' => 'Döviz Kuru',
            'document_no' => 'Belge No',
            'bank_account' => 'Banka Hesabı',
            'due_date' => 'Vade Tarihi',
            'description' => 'Açıklama',
            'is_active' => 'Aktif/Pasif',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'transaction_date.required' => 'İşlem tarihi alanı zorunludur.',
            'transaction_date.date' => 'İşlem tarihi geçerli bir tarih olmalıdır.',
            'invoice_id.required' => 'Fatura seçimi zorunludur.',
            'invoice_id.integer' => 'Geçersiz fatura seçimi.',
            'invoice_type.required' => 'Fatura tipi seçimi zorunludur.',
            'invoice_type.in' => 'Geçersiz fatura tipi.',
            'current_id.required' => 'Cari hesap seçimi zorunludur.',
            'current_id.exists' => 'Seçilen cari hesap geçerli değil.',
            'payment_method.required' => 'Ödeme yöntemi seçimi zorunludur.',
            'payment_method.exists' => 'Seçilen ödeme yöntemi geçerli değil.',
            'amount.required' => 'Tutar alanı zorunludur.',
            'amount.numeric' => 'Tutar alanı sayısal bir değer olmalıdır.',
            'amount.min' => 'Tutar alanı en az 0.01 olmalıdır.',
            'currency.required' => 'Para birimi alanı zorunludur.',
            'currency.in' => 'Geçersiz para birimi.',
            'exchange_rate.required' => 'Döviz kuru alanı zorunludur.',
            'exchange_rate.numeric' => 'Döviz kuru alanı sayısal bir değer olmalıdır.',
            'exchange_rate.min' => 'Döviz kuru en az 0.0001 olmalıdır.',
            'document_no.max' => 'Belge no en fazla 50 karakter olabilir.',
            'bank_account.max' => 'Banka hesabı en fazla 100 karakter olabilir.',
            'due_date.date' => 'Vade tarihi geçerli bir tarih olmalıdır.',
            'description.max' => 'Açıklama en fazla 500 karakter olabilir.',
        ];
    }
} 