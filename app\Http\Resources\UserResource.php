<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $tokenData = $this->createToken('Personal Access Token');

        $token = $tokenData->token;
        $token->expires_at = Carbon::now()->addYear();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'surname' => $this->surname,
            'email' => $this->email,
            'phone' => $this->phone,
            'profile_photo' => !is_null($this->photo) ? env('APP_URL') . '/upload/user/' . $this->photo : env('APP_URL') . '/upload/default_user.png',
            'token_type' => 'Bearer',
            'access_token' => $tokenData->accessToken,
            'expires_at' => $token->expires_at->format('Y-m-d H:i:s'),
        ];
    }
}
