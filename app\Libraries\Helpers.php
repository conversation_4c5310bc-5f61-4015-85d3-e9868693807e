<?php

namespace App\Libraries;

use App\Models\Logs;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Countable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Models\TempFile;
use Symfony\Component\HttpFoundation\FileBag;
use Illuminate\Support\Facades\File as FacadesFile;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\MorphOneOrMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use App\Models\InvoiceCounter;

class Helpers
{
    public static function activeMenu($groupArr)
    {
        if (Route::currentRouteName() == 'backend.' . $groupArr[0]) {
            return 'active open';
        }

        $temp = array();
        foreach ($groupArr as $value) {
            array_push($temp, 'backend.' . $value . '_list');
            array_push($temp, 'backend.' . $value . '_form');
            array_push($temp, 'backend.' . $value . '_show');
            array_push($temp, 'backend.' . $value . '_detail');
        }

        return in_array(Route::currentRouteName(), $temp) ? 'active open' : '';
    }

    public static function hasPermission($route_names)
    {
        $onuser = Auth::user();
        $permissions = $onuser->role['permissions'] ?? null;
        if (!$permissions) {
            return false;
        }

        $permissions = json_decode($permissions, true);
        foreach ((array) $route_names as $route_name) {
            if (in_array($route_name, $permissions)) {
                return true;
            }

            foreach ($permissions as $permission) {
                if (str_starts_with($permission, "backend." . $route_name)) {
                    return true;
                }
            }
        }

        return false;
    }

    public static function JsonWarning($message)
    {
        return response()->json(['status' => false, 'message' =>  $message], 400);
    }

    public static function PhoneNumber($phone)
    {
        return  preg_replace("/[^0-9]/", "", $phone);
    }

    public static function str($string = null)
    {
        if (func_num_args() === 0) {
            return new class
            {
                public function __call($method, $parameters)
                {
                    return Str::$method(...$parameters);
                }

                public function __toString()
                {
                    return '';
                }
            };
        }

        return Str::of($string);
    }

    public static function str_slug_tr($str)
    {
        if (is_array($str)) {
            $str = implode(' ', $str);
        }

        $str = str_replace(
            ['Ç', 'ç', 'Ğ', 'ğ', 'ı', 'İ', 'Ö', 'ö', 'Ş', 'ş', 'Ü', 'ü'],
            ['C', 'c', 'G', 'g', 'i', 'I', 'O', 'o', 'S', 's', 'U', 'u'],
            $str
        );

        return Str::slug($str);
    }

    public static function collect($value = [])
    {
        return new Collection($value);
    }

    public static function tempFile(FileBag $files)
    {
        $request = request();
        $token = $request->header("X-CSRF-TOKEN");
        $list = collect();
        foreach ($files->all() as $file) {
            $uniqId = uniqid();
            $result = Storage::disk("temp_path")
                ->putFileAs(
                    'upload',
                    $file,
                    $uniqId
                );

            $list->push(TempFile::query()->create([
                "path" => $result,
                "token" => $token,
                "name" => $file->getClientOriginalName(),
                "fileable_name" => $request->get("fname", "file"),
                "type" => $file->getMimeType(),
                "fileable_type" => $request->get("ftype", "file"),
                "ip" => $request->ip(),
                "user_agent" => $request->userAgent(),
                "referer" => $request->headers->get("referer"),
                "origin" => $request->headers->get("origin"),
                "expires_at" => Carbon::now()->addMinutes(30),
            ]));
        }

        return $list;
    }

    public static function tempFileManager($token, Countable $tempFileIds, Model $model)
    {
        if (!method_exists($model, 'files')) return $model;

        if (!($model->files() instanceof MorphMany) and !($model->files() instanceof MorphOne) and !($model->files() instanceof MorphOneOrMany)) return $model;

        $temp_files = TempFile::query()->whereIn("id", $tempFileIds)->where("token", $token)->get();

        if ($temp_files->count() <= 0) return $model;
        $m_class = mb_split('\\\\', $model::class);

        $directory = public_path("upload/" . Str::lower(Arr::last($m_class)));
        $files = collect();

        if (!FacadesFile::exists($directory)) {
            FacadesFile::makeDirectory($directory, 0777, true);
        }


        //Move file to storage
        foreach ($temp_files as $temp_file) {
            $old_path = storage_path('temp/' . $temp_file->path);
            $new_file_path = Str::lower(Arr::last($m_class)) . "/" . Helpers::str(now()->format("YmdHis") . "-" . $temp_file->name)->snake()->__toString();
            $new_path = $directory . "/" . Helpers::str(now()->format("YmdHis") . "-" . $temp_file->name)->snake()->__toString();

            $r = FacadesFile::copy($old_path, $new_path);

            if ($r) {
                $model->files()->create([
                    "name" => $temp_file->fileable_name,
                    "type" => $temp_file->fileable_type,
                    "extension" => $temp_file->type,
                    "path" => $new_file_path,
                    "disk" => "public_path",
                ]);

                $temp_file->delete();
                FacadesFile::delete($old_path);
            }
        }
        return $model;
    }

    public static function fileManager(Model $model, \Symfony\Component\HttpFoundation\File\UploadedFile $file, $name = 'file', $disk = 'public_path')
    {
        if ($model->id == null) {
            return $model;
        }

        if (!method_exists($model, 'files')) {
            return $model;
        }

        if (!($model->files() instanceof MorphMany) and !($model->files() instanceof MorphOne) and !($model->files() instanceof MorphOneOrMany)) {
            return $model;
        }
        $m_class = mb_split('\\\\', $model::class);

        $file = Storage::disk($disk)
            ->putFileAs('upload/' . Str::lower(Arr::last($m_class)), $file, Str::snake(Date::now()->timestamp) . " " . $file->getClientOriginalName());

        $model->files()->create([
            'path' => $file,
            'name' => $name,
        ]);

        return $model;
    }

    public static function expenseNumber($type,$month, $year, $nextNumber, $length)
    {
        return "{$type}/{$month}/{$year}/" . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    }

    public static function accountVoucherNumber($month, $year, $nextNumber, $length)
    {
        return "CHF/{$month}/{$year}/" . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    }

    public static function invoicesNumber($type,$month, $year, $nextNumber, $length)
    {
        return "MSF/{$month}/{$year}/" . str_pad($nextNumber, $length, '0', STR_PAD_LEFT);
    }

    public static function generateNextSequenceNumber($prefix, $month = null, $year = null, $length = 7)
    {
        try {
            $currentDate = Carbon::now();
            $year = $year ?? $currentDate->format('Y');
            $month = $month ?? $currentDate->format('m');

            // InvoiceCounter kaydını bul veya oluştur
            $counter = \App\Models\InvoiceCounter::where('prefix', $prefix)
                ->where('month', $month)
                ->where('year', $year)
                ->first();

            DB::beginTransaction(); // Transaction başlat

            if ($counter) {
                // Mevcutsa, last_number'ı artır
                $counter->last_number = $counter->last_number + 1;
                $counter->save();
            } else {
                // Yoksa, yeni bir counter kaydı oluştur
                $counter = \App\Models\InvoiceCounter::create([
                    'prefix' => $prefix,
                    'month' => $month,
                    'year' => $year,
                    'last_number' => 1, // Başlangıç numarası 1
                ]);
            }

            DB::commit(); // Transaction bitir

            // Yeni numarayı oluştur (prefix/ay/yıl/sıradaki_sayı formatında)
            $sequenceNumber = "{$prefix}/{$month}/{$year}/" . str_pad($counter->last_number, $length, '0', STR_PAD_LEFT);

            // Oluşturulan numaranın benzersiz olduğundan emin olmak için bir kontrol ekleyebilirsiniz
            // Ancak bu Helper genel olduğu için hangi tabloda kontrol yapacağını bilmez.
            // Kontrolü çağıran controller'da yapmak daha uygun olabilir.

            return $sequenceNumber;

        } catch (\Exception $e) {
            DB::rollBack(); // Hata durumunda transaction'ı geri al
            \Illuminate\Support\Facades\Log::error("Numara oluşturulurken hata ({$prefix}/{$month}/{$year}): " . $e->getMessage());

            // Hata durumunda benzersiz olmayan veya geçici bir numara döndürmek yerine exception fırlatmak daha güvenli olabilir.
            // Ancak uygulamanın akışını bozmamak için basit bir hata mesajı içeren numara döndürelim.
            // Gerçek uygulamada bu durum ciddi bir şekilde ele alınmalıdır.
            return "HATA_{$prefix}_NUMARA_OLUSTURULAMADI";
        }
    }
    public static function parseTrNumber($value)
    {
        $value = str_replace('.', '', $value);
        $value = str_replace(',', '.', $value);
        return floatval($value);
    }
}
