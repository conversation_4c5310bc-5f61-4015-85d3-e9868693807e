<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class AccountVoucher extends BaseModel
{
    use SoftDeletes;

    protected $table = 'account_vouchers';
    
    protected $guarded = [];

    protected $casts = ['voucher_date' => 'date', 'due_date' => 'date'];

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function voucherType()
    {
        return $this->belongsTo(VoucherType::class, 'voucher_type', 'code');
    }

    public function getVoucherTypeNameAttribute()
    {
        return $this->voucherType ? $this->voucherType->name : $this->voucher_type;
    }
}
