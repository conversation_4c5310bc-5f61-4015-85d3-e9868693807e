<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Brand extends BaseModel
{
    use SoftDeletes;

    protected $table = 'brands';

    protected $guarded = [];

    public function products()
    {
        return $this->hasMany(Product::class, 'brand_id');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'brand_categories', 'brand_id', 'category_id')
                    ->wherePivot('is_active', 1)
                    ->withPivot('is_active', 'created_by', 'updated_by', 'deleted_by')
                    ->withTimestamps();
    }

    public function getProductsCountAttribute()
    {
        if (array_key_exists('products_count', $this->attributes)) {
            return $this->attributes['products_count'];
        }

        return $this->products()->count();
    }
}
