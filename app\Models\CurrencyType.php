<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class CurrencyType extends BaseModel
{
    use SoftDeletes;

    protected $table = 'currency_types';

    protected $guarded = [];

    public function finance()
    {
        return $this->hasMany(Finance::class);
    }

    public function exchangeRates()
    {
        return $this->hasMany(ExchangeRate::class, 'code', 'code');
    }

    public function latestExchangeRate()
    {
        return $this->hasOne(ExchangeRate::class, 'code', 'code')
                    ->latest('rate_date');
    }
}
