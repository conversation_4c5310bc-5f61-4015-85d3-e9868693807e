<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Current extends BaseModel
{
    use SoftDeletes;

    protected $table = 'currents';

    protected $guarded = [];

    public function instutaion()
    {
        return $this->belongsTo(InsutationType::class);
    }

    public function currentType()
    {
        return $this->belongsTo(CurrentType::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function taxOffice()
    {
        return $this->belongsTo(TaxOffice::class);
    }

    public function district()
    {
        return $this->belongsTo(District::class);
    }
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'current_id', 'id');
    }
    public function balance()
    {
        return $this->hasMany(Balance::class, 'current_id', 'id');

    }
    public function order()
    {
        return $this->hasMany(Order::class, 'current_id', 'id');
    }
    public function offers()
    {
        return $this->hasMany(Offer::class, 'current_id', 'id');
    }
}
