<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class ExpenseVoucher extends BaseModel
{
    use SoftDeletes;

    protected $table = 'expense_vouchers';

    protected $guarded = [];

    protected $casts = ['voucher_date' => 'date'];

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function expenseType()
    {
        return $this->belongsTo(ExpenseType::class, 'expense_type_id');
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class, 'payment_type_id');
    }

    public function items()
    {
        return $this->hasMany(ExpenseVoucherItem::class, 'expense_voucher_id');
    }
}
