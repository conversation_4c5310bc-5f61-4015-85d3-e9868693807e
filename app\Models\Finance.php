<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Finance extends BaseModel
{
    use SoftDeletes;

    protected $table = 'finances';

    protected $guarded = [];

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id', 'id');
    }

    public function documentType()
    {
        return $this->belongsTo(Document::class, 'document_type_id', 'id');
    }
}
