<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends BaseModel
{
    use SoftDeletes;

    protected $table = 'invoices';

    protected $guarded = [];

    protected $casts = [
        'invoice_date' => 'datetime', 
        'due_date' => 'datetime',
        'items' => 'array'
    ];

    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function stock()
    {
        return $this->hasMany(Product::class, 'product_id', 'id');
    }

    public function documentType()
    {
        return $this->belongsTo(Document::class, 'document_type_id', 'id');
    }

    public function balance()
    {
        return $this->hasMany(Balance::class);
    }

    public function paymentTransactions()
    {
        return $this->hasMany(PaymentTransaction::class, 'invoice_id')
                    ->where('invoice_type', 1);
    }

    public function invoiceStatus()
    {
        return $this->belongsTo(InvoiceStatus::class, 'status', 'id');
    }

    public function getTotalPaidAttribute()
    {
        return $this->paymentTransactions()
                    ->where('is_active', 1)
                    ->sum('amount');
    }

    public function getRemainingAmountAttribute()
    {
        return $this->total_amount - $this->total_paid;
    }
}
