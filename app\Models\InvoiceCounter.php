<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceCounter extends BaseModel
{
    use SoftDeletes;

    protected $table = 'invoice_counters';
    
    protected $guarded = [];

    public static function getCounter($month = null, $year = null)
    {
        if ($month === null || $year === null) {
            $now = Carbon::now();
            $month = $month ?? $now->format('m');
            $year = $year ?? $now->format('Y');
        }

        return self::firstOrCreate(
            ['month' => $month, 'year' => $year],
            ['last_number' => 0, 'prefix' => 'FTR']
        );
    }


    public function incrementCounter()
    {
        $this->last_number += 1;
        $this->save();

        return $this->last_number;
    }


    public static function generateInvoiceNumber($month = null, $year = null)
    {
        $counter = self::getCounter($month, $year);

        $nextNumber = $counter->incrementCounter();

        $invoiceNumber = $counter->prefix . '/' . $counter->month . '/' . $counter->year . '/' . str_pad($nextNumber, 7, '0', STR_PAD_LEFT);

        return $invoiceNumber;
    }
}
