<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Offer extends  BaseModel
{
    use SoftDeletes, LogsActivity;

    protected $table = 'offers';

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class);
    }

    public function current()
    {
        return $this->belongsTo(Current::class)->withTrashed();
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }

    public function offerType()
    {
        return $this->belongsTo(OfferType::class);
    }

    public function offerProducts()
    {
        return $this->hasMany(OfferProduct::class);
    }

    public function hasApprovedProducts(): bool
    {
        return $this->offerProducts()->where('status', 1)->exists();
    }

    public static function bootHook()
    {
        static::deleting(function ($model) {
            if ($model->hasApprovedProducts()) {
                return false;
            }
            else{
                if($model->offerProducts()){
                    $model->offerProducts()->delete();
                }
            }
        });
    }

    public static function generateOfferNumber($offerTypeId = null)
    {
        $datePrefix = Carbon::now()->format('Ymd'); // 20250429
        $maxSequence = 0;
        $orders = self::withTrashed()
            ->where('offer_number', 'like', $datePrefix . '%')
            ->get(['offer_number']);

        foreach ($orders as $order) {
            $sequence = (int)substr($order->offer_number, -2);
            if ($sequence > $maxSequence) {
                $maxSequence = $sequence;
            }
        }

        $nextSequence = str_pad($maxSequence + 1, 5, '0', STR_PAD_LEFT);

        return $datePrefix . $nextSequence;
    }

    public function processOfferProducts(array $products): void
    {
        $existingProducts = OfferProduct::withTrashed()
            ->where('offer_id', $this->id)
            ->get()
            ->keyBy('stock_id');

        $incomingStockIds = [];
        foreach ($products as $id => $product) {
            $incomingStockIds[] = $id;
            $stock = Stock::find($id);
            if (!$stock) {
                continue;
            }
            $exchangeRateId = (int) ($product['currency'] ?? 0);
            $exchangeRate = ExchangeRate::find($exchangeRateId);
            $currencyType = $exchangeRate ? ($exchangeRate->currency_code ?? $exchangeRate->code) : 'TRY';
            $offerNumber = $this->offer_number;
            $itemNo = $line['item_no'] ?? ($existingProducts[$id]->item_no ?? OfferProduct::generateItemNo($offerNumber));

            $offerProductData = [
                'offer_id' => $this->id,
                'stock_id' => $id,
                'product_code' => $stock->product->sku ?? null,
                'product_name' => $stock->variant->name ?? $stock->product->name ,
                'quantity' => $product['quantity'],
                'unit' => $stock->product->unit->name ?? null,
                'unit_price' => $product['price'],
                'exchange_rate_id' => $exchangeRateId,
                'exchange_rate' => $exchangeRate ? $exchangeRate->selling_rate : 1,
                'currency_type' => $currencyType,
                'vat_rate' => $product['vat_rate'],
                'vat_status' => $product['vat_status'] ?? 0,
                'total_price' => $product['amount'] ?? 0,
                'item_no' => $itemNo,
                'is_active' => 1
            ];

            if (isset($existingProducts[$id])) {
                $existingProduct = $existingProducts[$id];
                $existingProduct->fill($offerProductData);
                $existingProduct->deleted_at = null;
                $existingProduct->save();
            } else {
                    OfferProduct::create($offerProductData);
            }
        }
        $toDelete = $existingProducts->keys()->diff($incomingStockIds);
        foreach ($toDelete as $stockId) {
            $product = $existingProducts[$stockId];
            if (!$product->deleted_at) {
                $product->delete();
            }
        }

    }

    public function scopeFilter($query, $filter)
    {
        // Start date ve end date birlikte varsa aralık filtreleme
        if (!empty($filter->start_date) && !empty($filter->end_date)) {
            $query->whereBetween('offer_date', [
                Carbon::parse($filter->start_date)->startOfDay(),
                Carbon::parse($filter->end_date)->endOfDay()
            ]);
        }
        // Sadece start_date varsa
        elseif (!empty($filter->start_date)) {
            $query->whereDate('offer_date', Carbon::parse($filter->start_date)->toDateString());
        }
        // Sadece end_date varsa (gün gün kontrol için)
        if (!empty($filter->end_date)) {
            $query->whereDate('offer_deadline', Carbon::parse($filter->end_date)->toDateString());
        }

        if (isset($filter->current_id) && !empty($filter->current_id)) {
            $query->whereHas('current', function ($q) use ($filter) {
                $q->where('id', $filter->current_id);
            });
        }
        return $query;
    }
}
