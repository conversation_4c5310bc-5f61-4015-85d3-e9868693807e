<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class OrderProduct extends BaseModel
{
    use SoftDeletes;

    protected $table = 'order_products';

    protected $guarded = [];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }

    public function stockReservations()
    {
        return $this->hasMany(StockReservation::class, 'order_id', 'order_id');
    }


    public function stock()
    {
        return $this->belongsTo(Stock::class, 'stock_id');
    }
    public static function generateItemNo($orderNumber)
    {
        $prefix = $orderNumber . '-';
        $lastItemNo = self::withTrashed()->where('item_no', 'like', $prefix . '%')->latest('item_no')->first();
        if ($lastItemNo) {
            $lastSequence = (int)substr($lastItemNo->item_no, strrpos($lastItemNo->item_no, '-') + 1);
            $nextSequence = str_pad($lastSequence + 1, 2, '0', STR_PAD_LEFT);
        } else {
            $nextSequence = '01';
        }

        return $prefix . $nextSequence;
    }
}
