<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTransaction extends BaseModel
{
    use SoftDeletes;

    protected $table = 'payment_transactions';

    protected $guarded = [];

    protected $casts = [
        'transaction_date' => 'datetime',
        'due_date' => 'datetime',
    ];

    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }

    public function getInvoiceTypeNameAttribute()
    {
        return $this->invoice_type == 2 ? 'Satış Faturası' : 'Alış Faturası';
    }

    public function getPaymentMethodNameAttribute()
    {
        return $this->paymentType ? $this->paymentType->name : '';
    }

    public function salesInvoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id')->where('invoice_type', 1);
    }

    public function purchaseInvoice()
    {
        return $this->belongsTo(PurchaseInvoice::class, 'invoice_id')->where('invoice_type', 2);
    }

    public function scopeFilter($query, $filter)
    {
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('transaction_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        }

        if (isset($filter->current_id) && !empty($filter->current_id)) {
            $query->whereHas('current', function ($q) use ($filter) {
                $q->where('id', $filter->current_id);
            });
        }

        return $query;
    }
}
