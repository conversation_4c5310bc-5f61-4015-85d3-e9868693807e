<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class PhysicalCount extends BaseModel
{
    use SoftDeletes;

    protected $table = 'physical_counts';

    protected $guarded = [];

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function location()
    {
        return $this->belongsTo(WarehouseLocation::class, 'location_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function items()
    {
        return $this->hasMany(PhysicalCountItem::class, 'physical_count_id');
    }

    public function scopePending($query)
    {
        return $query->where('status_id', 1);
    }

    public function scopeApproved($query)
    {
        return $query->where('status_id', 2);
    }

    public function scopeCancelled($query)
    {
        return $query->where('status_id', 3);
    }

    public function scopeFilter($query, $filter)
    {
        if (isset($filter->count_code) && !empty($filter->count_code)) {
            $query->where('count_code', 'like', '%' . $filter->count_code . '%');
        }
        if (isset($filter->warehouse_id) && !empty($filter->warehouse_id)) {
            $query->where('warehouse_id', $filter->warehouse_id);
        }
        if (isset($filter->location_id) && !empty($filter->location_id)) {
            $query->where('location_id', $filter->location_id);
        }
        if (isset($filter->status_id) && !empty($filter->status_id)) {
            $query->where('status_id', $filter->status_id);
        }
        if (isset($filter->is_active) && !is_null($filter->is_active)) {
            $query->where('is_active', $filter->is_active);
        }
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('count_date', [
                Carbon::parse($filter->start_date)->format('Y-m-d'),
                Carbon::parse($filter->end_date)->format('Y-m-d'),
            ]);
        }
        return $query;
    }
}