<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;

class PhysicalCountItem extends BaseModel
{
    use SoftDeletes;

    protected $table = 'physical_count_items';

    protected $guarded = [];

    public function physicalCount()
    {
        return $this->belongsTo(PhysicalCount::class, 'physical_count_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }
}