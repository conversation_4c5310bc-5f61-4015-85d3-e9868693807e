<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Product extends BaseModel
{
    use SoftDeletes;

    protected $table = 'products';

    protected $guarded = [];

    public static function generateSku(string $categoryName, string $productName)
    {
        $catCode = strtoupper(substr(self::turkishToEnglish($categoryName), 0, 3));
        $prodCode = strtoupper(substr(self::turkishToEnglish($productName), 0, 3));
        $prefix = $catCode . $prodCode;

        $lastSku = self::where('sku', 'like', "$prefix%")
            ->latest('sku')
            ->first();

        if ($lastSku) {
            $lastSequence = (int)substr($lastSku->sku, -3);
            $nextSequence = str_pad($lastSequence + 1, 3, '0', STR_PAD_LEFT);
        } else {
            $nextSequence = '001';
        }

        return $prefix . $nextSequence;
    }

    public static function turkishToEnglish($text)
    {
        $search = ['Ç', 'ç', 'Ğ', 'ğ', 'ı', 'İ', 'Ö', 'ö', 'Ş', 'ş', 'Ü', 'ü', ' ', '-', '_', '.'];
        $replace = ['C', 'c', 'G', 'g', 'i', 'I', 'O', 'o', 'S', 's', 'U', 'u', '', '', '', ''];
        $text = str_replace($search, $replace, $text);
        return preg_replace('/[^a-zA-Z0-9]/', '', $text);
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class, 'brand_id');
    }

    public function unitType()
    {
        return $this->belongsTo(UnitType::class, 'unit_type_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unit_id');
    }

    public function variants()
    {
        return $this->hasMany(ProductVariant::class, 'product_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'product_id');
    }

    public function attributes()
    {
        return $this->hasMany(ProductAttribute::class, 'product_id');
    }

    public function costHistories()
    {
        return $this->hasMany(ProductCostHistory::class, 'product_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovementItem::class, 'product_id');
    }

    public function unitConversions()
    {
        return $this->hasMany(ProductUnitConversion::class, 'product_id');
    }

    public function getTotalStockQuantityAttribute()
    {
        return $this->stocks()->sum('quantity');
    }

    public function getTotalReservedQuantityAttribute()
    {
        return $this->stocks()->sum('reserved_quantity');
    }

    public function getAvailableStockQuantityAttribute()
    {
        return $this->total_stock_quantity - $this->total_reserved_quantity;
    }
}
