<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class ProductAttribute extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_attributes';

    protected $guarded = [];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function values()
    {
        return $this->hasMany(ProductAttributeValue::class, 'attribute_id');
    }
}