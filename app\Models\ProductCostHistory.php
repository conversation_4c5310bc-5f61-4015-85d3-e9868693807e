<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ProductCostHistory extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_cost_histories';

    protected $guarded = [];

    protected $casts = ['valid_from' => 'datetime', 'valid_to' => 'datetime'];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }
}
