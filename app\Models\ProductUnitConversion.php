<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class ProductUnitConversion extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_unit_conversions';

    protected $guarded = [];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function fromUnit()
    {
        return $this->belongsTo(Unit::class, 'from_unit_id');
    }

    public function toUnit()
    {
        return $this->belongsTo(Unit::class, 'to_unit_id');
    }

    public function convertFromTo($quantity)
    {
        return $quantity * $this->conversion_factor;
    }

    public function convertToFrom($quantity)
    {
        return $quantity / $this->conversion_factor;
    }
}