<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class ProductVariant extends BaseModel
{
    use SoftDeletes;

    protected $table = 'product_variants';

    protected $guarded = [];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'variant_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class, 'variant_id');
    }

    public function costHistory()
    {
        return $this->hasMany(ProductCostHistory::class, 'variant_id');
    }

    public function getTotalStockQuantityAttribute()
    {
        return $this->stocks()->sum('quantity');
    }

    public function getTotalReservedQuantityAttribute()
    {
        return $this->stocks()->sum('reserved_quantity');
    }

    public function getAvailableStockQuantityAttribute()
    {
        return $this->total_stock_quantity - $this->total_reserved_quantity;
    }
}