<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseInvoice extends BaseModel
{
    use SoftDeletes;

    protected $table = 'purchase_invoices';

    protected $fillable = [
        'invoice_no',
        'invoice_date',
        'due_date',
        'current_id',
        'currency',
        'currency_code',
        'exchange_rate',
        'net_amount',
        'tax_amount',
        'total_amount',
        'status',
        'description',
        'items',
        'invoice_status',
        'is_active'
    ];

    protected $guarded = [];

    protected $casts = ['invoice_date' => 'datetime', 'due_date' => 'datetime'];

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function paymentTransactions()
    {
        return $this->hasMany(PaymentTransaction::class, 'invoice_id')
                    ->where('invoice_type', 2);
    }

    public function getTotalPaidAttribute()
    {
        return $this->paymentTransactions()
                    ->where('is_active', 1)
                    ->sum('amount');
    }

    public function getRemainingAmountAttribute()
    {
        return $this->total_amount - $this->total_paid;
    }
}
