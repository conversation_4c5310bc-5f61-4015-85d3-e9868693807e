<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class PurchaseWaybill extends BaseModel
{
    use SoftDeletes;

    protected $guarded = [];

    protected $casts = ['waybill_date' => 'date'];

    public function items()
    {
        return $this->hasMany(PurchaseWaybillItem::class, 'waybill_id');
    }

    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function invoice()
    {
        return $this->belongsTo(PurchaseInvoice::class, 'invoice_id');
    }

    public function getWaybillTypeNameAttribute()
    {
        return 'Alış İrsaliyesi';
    }
}
