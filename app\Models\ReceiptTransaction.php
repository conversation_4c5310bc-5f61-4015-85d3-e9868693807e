<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReceiptTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'current_id',
        'transaction_date',
        'invoice_id',
        'invoice_type',
        'payment_method',
        'amount',
        'description',
        'is_active',
    ];

    protected $casts = [
        'transaction_date' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class, 'payment_method');
    }
} 