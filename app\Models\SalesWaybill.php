<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;


class SalesWaybill extends BaseModel
{
    use SoftDeletes;

    protected $guarded = [];

    protected $casts = ['waybill_date' => 'date'];


    public function items()
    {
        return $this->hasMany(SalesWaybillItem::class, 'waybill_id');
    }


    public function current()
    {
        return $this->belongsTo(Current::class);
    }


    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function getWaybillTypeNameAttribute()
    {
        return 'Satış İrsaliyesi';
    }
}
