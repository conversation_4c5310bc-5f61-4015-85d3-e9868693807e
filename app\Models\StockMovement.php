<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockMovement extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_movements';

    protected $guarded = [];

    protected $casts = ['approval_date' => 'datetime', 'movement_date' => 'datetime'];

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function starter()
    {
        return $this->belongsTo(User::class, 'starter_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id');
    }

    public function reason()
    {
        return $this->belongsTo(StockMovementReason::class, 'stock_movement_reason_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function location()
    {
        return $this->belongsTo(WarehouseLocation::class, 'location_id');
    }

    public function targetWarehouse()
    {
        return $this->belongsTo(Warehouse::class, 'target_warehouse_id');
    }

    public function targetLocation()
    {
        return $this->belongsTo(WarehouseLocation::class, 'target_location_id');
    }

    public function items()
    {
        return $this->hasMany(StockMovementItem::class, 'stock_movement_id');
    }

    public function serialNumbers()
    {
        return $this->hasMany(StockSerialNumber::class, 'stock_movement_id');
    }

    public function getTotalPriceAttribute()
    {
        return $this->items()->sum('total_price');
    }
}
