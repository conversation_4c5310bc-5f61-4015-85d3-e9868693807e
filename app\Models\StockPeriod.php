<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockPeriod extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_periods';

    protected $guarded = [];

    protected $casts = ['start_date' => 'date', 'end_date' => 'date'];

    public function closingHistories()
    {
        return $this->hasMany(StockPeriodClosingHistory::class, 'period_id');
    }

    public function snapshots()
    {
        return $this->hasMany(StockPeriodSnapshot::class, 'period_id');
    }

    public function transfersFrom()
    {
        return $this->hasMany(StockTransfer::class, 'from_period_id');
    }

    public function transfersTo()
    {
        return $this->hasMany(StockTransfer::class, 'to_period_id');
    }
}
