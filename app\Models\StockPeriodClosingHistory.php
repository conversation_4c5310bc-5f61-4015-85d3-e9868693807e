<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockPeriodClosingHistory extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_period_closing_histories';

    protected $guarded = [];

    protected $casts = ['closing_date' => 'datetime', 'approval_date' => 'datetime'];

    public function period()
    {
        return $this->belongsTo(StockPeriod::class, 'period_id');
    }

    public function closingUser()
    {
        return $this->belongsTo(User::class, 'closing_user_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function snapshots()
    {
        return $this->hasMany(StockPeriodSnapshot::class, 'closing_history_id');
    }
}
