<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockPeriodSnapshot extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_period_snapshots';

    protected $guarded = [];

    public function period()
    {
        return $this->belongsTo(StockPeriod::class, 'period_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function warehouseLocation()
    {
        return $this->belongsTo(WarehouseLocation::class, 'warehouse_location_id');
    }

    public function stockBatch()
    {
        return $this->belongsTo(StockBatch::class, 'stock_batch_id');
    }

    public function closingHistory()
    {
        return $this->belongsTo(StockPeriodClosingHistory::class, 'closing_history_id');
    }
}
