<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class StockReservation extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_reservations';

    protected $guarded = [];

    protected $casts = ['start_date' => 'datetime', 'end_date' => 'datetime'];

    public function stock()
    {
        return $this->belongsTo(Stock::class, 'stock_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function reservationReason()
    {
        return $this->belongsTo(StockReservationReason::class, 'reservation_reason_id');
    }

    public function reservationType()
    {
        return $this->belongsTo(StockReservationType::class, 'reservation_type_id');
    }

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }
}
