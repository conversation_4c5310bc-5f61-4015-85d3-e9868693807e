<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockSerialNumber extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_serial_numbers';

    protected $guarded = [];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function stockBatch()
    {
        return $this->belongsTo(StockBatch::class, 'stock_batch_id');
    }

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function warehouseLocation()
    {
        return $this->belongsTo(WarehouseLocation::class, 'warehouse_location_id');
    }

    public function stockMovement()
    {
        return $this->belongsTo(StockMovement::class, 'stock_movement_id');
    }

    public function serialNumberStatus()
    {
        return $this->belongsTo(SerialNumberStatus::class, 'serial_number_status');
    }

    public function movementItems()
    {
        return $this->hasMany(StockMovementItem::class, 'serial_number_id');
    }
}
