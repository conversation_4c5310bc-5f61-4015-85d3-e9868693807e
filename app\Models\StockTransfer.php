<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockTransfer extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_transfers';

    protected $guarded = [];

    protected $casts = ['transfer_date' => 'datetime', 'approval_date' => 'datetime'];

    public function fromPeriod()
    {
        return $this->belongsTo(StockPeriod::class, 'from_period_id');
    }

    public function toPeriod()
    {
        return $this->belongsTo(StockPeriod::class, 'to_period_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id');
    }

    public function starter()
    {
        return $this->belongsTo(User::class, 'starter_id');
    }

    public function approver()
    {
        return $this->belongsTo(User::class, 'approver_id');
    }

    public function items()
    {
        return $this->hasMany(StockTransferItem::class, 'stock_transfer_id');
    }
}
