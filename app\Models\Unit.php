<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Unit extends BaseModel
{
    use SoftDeletes;

    protected $table = 'units';

    protected $guarded = [];


    public function unitType()
    {
        return $this->belongsTo(UnitType::class, 'unit_type_id');
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'unit_id');
    }

    public function convertions()
    {
        return $this->hasMany(ProductUnitConversion::class, 'unit_id');
    }
}
