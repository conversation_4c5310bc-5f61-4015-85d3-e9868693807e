<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Warehouse extends BaseModel
{
    use SoftDeletes;

    protected $table = 'warehouses';

    protected $guarded = [];

    public function warehouseType()
    {
        return $this->belongsTo(WarehouseType::class, 'warehouse_type_id');
    }

    public function locations()
    {
        return $this->hasMany(WarehouseLocation::class, 'warehouse_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'warehouse_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class, 'warehouse_id');
    }

    public function targetStockMovements()
    {
        return $this->hasMany(StockMovement::class, 'target_warehouse_id');
    }

    public function physicalCounts()
    {
        return $this->hasMany(PhysicalCount::class, 'warehouse_id');
    }

    public function remainingWeightCapacity()
    {
        return $this->max_weight_capacity - $this->current_weight;
    }

    public function remainingVolumeCapacity()
    {
        return $this->max_volume_capacity - $this->current_volume;
    }

    public function getVolumeUsagePercentAttribute()
    {
        if ($this->max_volume_capacity > 0) {
            return ($this->current_volume / $this->max_volume_capacity) * 100;
        }
        return 0;
    }

    public function getWeightUsagePercentAttribute()
    {
        if ($this->max_weight_capacity > 0) {
            return ($this->current_weight / $this->max_weight_capacity) * 100;
        }
        return 0;
    }
}