<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class WarehouseLocation extends BaseModel
{
    use SoftDeletes;

    protected $table = 'warehouse_locations';

    protected $guarded = [];

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    public function parent()
    {
        return $this->belongsTo(WarehouseLocation::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(WarehouseLocation::class, 'parent_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'warehouse_location_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class, 'location_id');
    }

    public function targetStockMovements()
    {
        return $this->hasMany(StockMovement::class, 'target_location_id');
    }

    public function physicalCounts()
    {
        return $this->hasMany(PhysicalCount::class, 'location_id');
    }

    public function remainingWeightCapacity()
    {
        return $this->max_weight_capacity - $this->current_weight;
    }

    public function remainingVolumeCapacity()
    {
        return $this->max_volume_capacity - $this->current_volume;
    }

    public function getVolumeUsagePercentAttribute()
    {
        if ($this->max_volume_capacity > 0) {
            return ($this->current_volume / $this->max_volume_capacity) * 100;
        }
        return 0;
    }

    public function getWeightUsagePercentAttribute()
    {
        if ($this->max_weight_capacity > 0) {
            return ($this->current_weight / $this->max_weight_capacity) * 100;
        }
        return 0;
    }
}
