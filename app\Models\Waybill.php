<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Waybill extends BaseModel
{
    use SoftDeletes;

    protected $guarded = [];

    protected $casts = ['waybill_date' => 'date'];

    public function current()
    {
        return $this->belongsTo(Current::class);
    }

    public function items()
    {
        return $this->hasMany(WaybillItem::class);
    }

    public function salesInvoice()
    {
        return $this->belongsTo(Invoice::class, 'invoice_id')->where('invoice_type', 1);
    }

    public function purchaseInvoice()
    {
        return $this->belongsTo(PurchaseInvoice::class, 'invoice_id')->where('invoice_type', 2);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function getWaybillTypeNameAttribute()
    {
        return $this->waybill_type == 1 ? 'Satış İrsaliyesi' : '<PERSON><PERSON>ş İrsaliyesi';
    }
}
