<?php

namespace App\Services;

use App\Models\ExchangeRate;
use App\Models\CurrencyType;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use SimpleXMLElement;

class ExchangeRateService
{
    protected $tcmbUrl = 'https://www.tcmb.gov.tr/kurlar/today.xml';
    protected $cacheKey = 'exchange_rates';
    protected $cacheDuration = 3600; // 1 saat

    /**
     * Döviz koduna göre uygun sembol döndürür
     */
    protected function getCurrencySymbol($code)
    {
        $symbols = [
            'TRY' => '₺',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'JPY' => '¥',
            'CHF' => 'CHF',
            'CAD' => 'C$',
            'AUD' => 'A$',
            'NOK' => 'kr',
            'SEK' => 'kr',
            'DKK' => 'kr',
            'PLN' => 'zł',
            'BGN' => 'лв',
            'RON' => 'lei',
            'CZK' => 'Kč',
            'HUF' => 'Ft',
            'RUB' => '₽',
            'CNY' => '¥',
            'KRW' => '₩',
            'SAR' => 'ر.س',
            'KWD' => 'د.ك',
            'QAR' => 'ر.ق',
            'AED' => 'د.إ',
            'ILS' => '₪',
            'INR' => '₹',
            'PKR' => '₨',
            'ZAR' => 'R',
            'MXN' => '$',
            'BRL' => 'R$',
            'ARS' => '$',
            'CLP' => '$',
            'COP' => '$',
            'PEN' => 'S/',
            'UYU' => '$U',
        ];

        return $symbols[$code] ?? $code;
    }

    /**
     * Veritabanındaki döviz kurlarını çeker
     * Sadece görüntüleme için kullanılır
     *
     * @return array
     */
    public function getExchangeRates()
    {
        return Cache::remember($this->cacheKey, $this->cacheDuration, function () {
            try {
                // Veritabanındaki kurları getir
                $dbRates = ExchangeRate::where('is_active', true)->get();

                return $dbRates->map(function ($rate) {
                    return [
                        'code' => $rate->code,
                        'name' => $rate->name,
                        'symbol' => $rate->symbol,
                        'unit' => $rate->unit,
                        'buying_rate' => $rate->buying_rate,
                        'selling_rate' => $rate->selling_rate,
                        'rate_date' => $rate->rate_date,
                        'updated_at' => $rate->updated_at->format('d.m.Y H:i:s'),
                    ];
                })->toArray();
            } catch (\Exception $e) {
                throw $e;
            }
        });
    }

    /**
     * XML yanıtını parse eder
     *
     * @param string $xmlContent
     * @return array
     */
    protected function parseXmlResponse($xmlContent)
    {
        try {
            $xml = new SimpleXMLElement($xmlContent);
            $rates = [];
            $now = now();

            // Önce TRY (Türk Lirası) ekleyelim
            $rates[] = [
                'code' => 'TRY',
                'name' => 'TÜRK LİRASI',
                'symbol' => $this->getCurrencySymbol('TRY'),
                'unit' => 1,
                'buying_rate' => 1.0,
                'selling_rate' => 1.0,
                'rate_date' => $now,
                'updated_at' => $now->format('d.m.Y H:i:s'),
            ];

            foreach ($xml->Currency as $currency) {
                $code = (string) $currency['CurrencyCode'];

                // XDR (Special Drawing Right) birimini filtrele
                if ($code === 'XDR') {
                    continue;
                }

                $rates[] = [
                    'code' => $code,
                    'name' => (string) $currency->CurrencyName,
                    'symbol' => $this->getCurrencySymbol($code),
                    'unit' => (int) $currency->Unit,
                    'buying_rate' => (float) str_replace(',', '.', $currency->ForexBuying),
                    'selling_rate' => (float) str_replace(',', '.', $currency->ForexSelling),
                    'rate_date' => $now,
                    'updated_at' => $now->format('d.m.Y H:i:s'),
                ];
            }

            return $rates;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Döviz kurlarını veritabanına kaydeder
     * Her gün için yeni kayıt oluşturur
     *
     * @param array $rates
     * @return void
     */
    protected function saveRatesToDatabase($rates)
    {
        $today = now()->startOfDay();

        foreach ($rates as $rate) {
            // Bugün için bu döviz koduna ait kayıt var mı kontrol et
            $existingRate = ExchangeRate::where('code', $rate['code'])
                ->whereDate('rate_date', $today)
                ->first();

            // Eğer bugün için kayıt yoksa yeni kayıt oluştur
            if (!$existingRate) {
                $formattedRate = [
                    'code' => $rate['code'],
                    'name' => $rate['name'],
                    'symbol' => $rate['symbol'],
                    'unit' => $rate['unit'],
                    'buying_rate' => $rate['buying_rate'],
                    'selling_rate' => $rate['selling_rate'],
                    'rate_date' => $today,
                    'is_active' => true,
                ];

                ExchangeRate::create($formattedRate);

                // Yeni para birimi eklendiyse currency_types tablosuna da ekle
                $this->addToCurrencyTypes($rate);
            } else {
                // Bugün için kayıt varsa güncelle
                $existingRate->update([
                    'symbol' => $rate['symbol'],
                    'buying_rate' => $rate['buying_rate'],
                    'selling_rate' => $rate['selling_rate'],
                    'unit' => $rate['unit'],
                    'name' => $rate['name']
                ]);
            }
        }
    }
    protected function addToCurrencyTypes($rate)
    {
        $existsCurrency = CurrencyType::where('code', $rate['code'])->first();
        if (!$existsCurrency) {
            CurrencyType::create([
                'name' => $rate['name'],
                'symbol' => $rate['symbol'],
                'code' => $rate['code'],
                'is_active' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        }
    }
    /**
     * Döviz kurlarını manuel olarak günceller ve veritabanına kaydeder
     *
     * @return void
     */
    public function refreshRates()
    {
        try {
            $response = Http::get($this->tcmbUrl);

            if ($response->successful()) {
                $rates = $this->parseXmlResponse($response->body());
                $this->saveRatesToDatabase($rates);

                // Önbelleği temizle
                Cache::forget($this->cacheKey);

                return;
            }

            throw new \Exception('Döviz kurları güncellenirken bir hata oluştu.');
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Belirli bir tarihe ait TCMB XML verilerini çeker (veritabanına kaydetmeden)
     *
     * @param string|null $date Y-m-d formatında tarih
     * @return array Döviz kuru verileri
     */
    public function getExternalRatesForDate(?string $date = null): array
    {
        $date = $date ? Carbon::parse($date) : now();
        $formattedDate = $date->format('dmY');
        $year = $date->format('Y');
        $month = $date->format('m');

        // TCMB XML URL'ini oluştur
        $url = "https://www.tcmb.gov.tr/kurlar/{$year}{$month}/{$formattedDate}.xml";

        try {
            // XML verisini çek
            $xmlContent = file_get_contents($url);
            if ($xmlContent === false) {
                throw new \Exception("$url adresinden veri çekilemedi");
            }

            $xml = new SimpleXMLElement($xmlContent);
            $rates = [];

            // XML verilerini işle
            foreach ($xml->Currency as $currency) {
                $rates[] = [
                    'code' => (string)$currency['CurrencyCode'],
                    'name' => (string)$currency->CurrencyName,
                    'symbol' => $this->getCurrencySymbol((string)$currency['CurrencyCode']),
                    'buying_rate' => (string)$currency->ForexBuying,
                    'selling_rate' => (string)$currency->ForexSelling,
                    'rate_date' => $date->format('Y-m-d')
                ];
            }

            return [
                'success' => true,
                'data' => $rates,
                'date' => $date->format('Y-m-d')
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'date' => $date->format('Y-m-d')
            ];
        }
    }
}
