<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('currents', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('surname');
            $table->integer('instutation_id');
            $table->integer('current_type_id');
            $table->integer('country_id');
            $table->integer('city_id');
            $table->integer('tax_offices_id');
            $table->longText('address');
            $table->integer('district_id');
            $table->integer('banks_id')->nullable();
            $table->integer('communication_id');
            $table->string('email');
            $table->string('phone');
            $table->integer('tax_number');
            $table->decimal('chance_limits');
            $table->decimal('bonds_limits');
            $table->decimal('order_limits');
            $table->decimal('balance_limits');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currents');
    }
};
