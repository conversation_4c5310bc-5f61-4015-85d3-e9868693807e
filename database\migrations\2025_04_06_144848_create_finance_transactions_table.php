<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('finance_transactions', function (Blueprint $table) {
            $table->id();
            $table->dateTime('transaction_date')->nullable();
            $table->string('transaction_type', 50)->nullable();
            $table->integer('current_id')->nullable();
            $table->decimal('amount', 15, 2)->default(0);
            $table->integer('currency_id')->nullable();
            $table->decimal('exchange_rate', 15, 4)->default(1);
            $table->string('doc_no')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_reconciled')->default(false);
            $table->dateTime('reconciliation_date')->nullable();
            $table->string('bank_name')->nullable();
            $table->string('bank_account')->nullable();
            $table->string('check_number')->nullable();
            $table->string('drawer_name')->nullable();
            $table->date('due_date')->nullable();
            $table->string('receipt_file')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('finance_transactions');
    }
};
