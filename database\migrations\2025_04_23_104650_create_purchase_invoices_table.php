<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_no', 50)->unique()->comment('Alış fatura numarası (otomatik veya manuel)');
            $table->dateTime('invoice_date')->nullable()->comment('Fatura düzenlenme tarihi');
            $table->dateTime('due_date')->nullable()->comment('Vade tarihi (opsiyonel)');
            $table->integer('current_id')->comment('<PERSON>i hesaba (tedarikçi) referans');
            $table->string('currency', 3)->default('TRY')->comment('Para birimi kodu');
            $table->string('currency_code', 3)->nullable()->comment('<PERSON>öviz kodu');
            $table->decimal('exchange_rate', 10, 4)->default(1.0000)->comment('Döviz kuru');
            $table->decimal('net_amount', 15, 2)->default(0)->comment('Vergisiz ara toplam');
            $table->decimal('tax_amount', 15, 2)->default(0)->comment('Toplam vergi tutarı');
            $table->decimal('total_amount', 15, 2)->default(0)->comment('Genel toplam (net + vergi)');
            $table->unsignedTinyInteger('status')->default(0)->comment('Fatura durumu');
            $table->text('description')->nullable()->comment('Fatura ile ilgili açıklamalar');
            $table->json('items')->nullable()->comment('Fatura kalemleri JSON formatında');
           $table->integer('invoice_status')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_invoices');
    }
};
