<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exchange_rates', function (Blueprint $table) {
            $table->id();
            $table->string('code', 10)->comment('Döviz kodu (USD, EUR, GBP, vb.)');
            $table->string('symbol', 10)->comment('<PERSON>ö<PERSON>z sembolü ($, €, £, vb.)');
            $table->string('name')->comment('Döviz adı');
            $table->integer('unit')->default(1)->comment('Birim');
            $table->decimal('buying_rate', 10, 4)->default(0)->comment('Alış kuru');
            $table->decimal('selling_rate', 10, 4)->default(0)->comment('Satış kuru');
            $table->timestamp('rate_date')->comment('<PERSON>r tarihi');
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exchange_rates');
    }
};
