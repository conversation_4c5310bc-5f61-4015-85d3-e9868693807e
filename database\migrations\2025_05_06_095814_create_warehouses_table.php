<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouses', function (Blueprint $table) {
            $table->id();
            $table->string('name', 191);
            $table->string('code', 191)->nullable();
            $table->integer('warehouse_type_id')->nullable();
            $table->string('description', 255)->nullable();
            $table->string('address', 255)->nullable();
            $table->decimal('max_weight_capacity', 12, 3)->default(0)->nullable();
            $table->decimal('max_volume_capacity', 12, 4)->default(0)->nullable();
            $table->decimal('current_weight', 12, 3)->default(0)->nullable();
            $table->decimal('current_volume', 12, 4)->default(0)->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouses');
    }
};
