<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->integer('current_id')->nullable();
            $table->integer('starter_id')->nullable();
            $table->integer('approver_id')->nullable();
            $table->dateTime('approval_date')->nullable();
            $table->dateTime('movement_date')->nullable();
            $table->integer('status_id')->nullable();
            $table->integer('stock_movement_reason_id')->nullable();
            $table->string('notes', 255)->nullable();
            $table->integer('warehouse_id')->nullable();
            $table->integer('location_id')->nullable();
            $table->integer('target_warehouse_id')->nullable();
            $table->integer('target_location_id')->nullable();
            $table->integer('stock_movement_type_id')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
