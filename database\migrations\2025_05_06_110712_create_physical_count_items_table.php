<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('physical_count_items', function (Blueprint $table) {
            $table->id();
            $table->integer('physical_count_id');
            $table->integer('product_id')->nullable();
            $table->integer('variant_id')->nullable();
            $table->decimal('system_quantity', 10, 2)->default(0);
            $table->decimal('counted_quantity', 10, 2)->default(0);
            $table->decimal('difference', 10, 2)->default(0);
            $table->string('notes', 255)->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('physical_count_items');
    }
};
