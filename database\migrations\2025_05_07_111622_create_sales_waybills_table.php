<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sales_waybills', function (Blueprint $table) {
            $table->id();
            $table->string('waybill_no', 50);
            $table->date('waybill_date');
            $table->unsignedBigInteger('current_id');
            $table->unsignedBigInteger('invoice_id')->nullable();
            $table->string('invoice_no', 50)->nullable();
            $table->text('note')->nullable();
            $table->decimal('total_amount', 15, 2)->default(0);
            $table->tinyInteger('waybill_type')->default(1); // 1: Satış İrsaliyesi
            $table->string('status', 20)->default('pending'); // pending, approved, cancelled
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sales_waybills');
    }
};
