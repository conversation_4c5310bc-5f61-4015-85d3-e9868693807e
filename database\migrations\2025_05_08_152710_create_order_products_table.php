<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_products', function (Blueprint $table) {
            $table->id();
            $table->integer('order_id');
            $table->string('item_no')->unique();
            $table->integer('stock_id');
            $table->integer('exchange_rate_id');
            $table->string('product_code');
            $table->string('product_name');
            $table->integer('quantity');
            $table->string('unit');
            $table->decimal('unit_price', 10, 2);
            $table->tinyInteger('status')->default(0)->comment('0: Bekliyor, 1: Onaylandı, 2: Reddedildi');
            $table->tinyInteger('vat_status')->default(0)->comment('0: Hariç, 1: Dahil');
            $table->string('currency_type');
            $table->decimal('exchange_rate', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->decimal('vat_rate', 10, 2);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_products');
    }
};
