<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('offers', function (Blueprint $table) {
            $table->id();
            $table->integer('branch_id');
            $table->integer('warehouse_id');
            $table->string('offer_number')->unique();
            $table->date('offer_date');
            $table->date('offer_deadline');
            $table->integer('exchange_rate_id');
            $table->integer('current_id');
            $table->string('shipping_address')->nullable();
            $table->integer('payment_type_id');
            $table->integer('offer_type_id');
            $table->longText('notes')->nullable();
            $table->decimal('total_price', 10, 2);
            $table->decimal('vat_amount', 10, 2);
            $table->decimal('total_amount', 10, 2);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('offers');
    }
};
