<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_serial_numbers', function (Blueprint $table) {
            $table->id();
            $table->integer('product_id')->nullable();
            $table->integer('variant_id')->nullable();
            $table->integer('stock_batch_id')->nullable();
            $table->string('serial_number')->unique();
            $table->integer('warehouse_id')->nullable();
            $table->integer('warehouse_location_id')->nullable();
            $table->integer('stock_movement_id')->nullable()->comment('Son stok hareketi referansı');
            $table->integer('serial_number_status')->default(1)->comment('1: Stok<PERSON>, 2: <PERSON><PERSON><PERSON>ld<PERSON>, 3: <PERSON><PERSON><PERSON><PERSON>, 4: <PERSON><PERSON><PERSON>');
            $table->string('notes', 255)->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_serial_numbers');
    }
};
