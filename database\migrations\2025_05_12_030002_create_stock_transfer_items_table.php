<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_transfer_items', function (Blueprint $table) {
            $table->id();
            $table->integer('stock_transfer_id');
            $table->integer('product_id')->nullable();
            $table->integer('variant_id')->nullable();
            $table->integer('warehouse_id');
            $table->integer('warehouse_location_id')->nullable();
            $table->integer('stock_batch_id')->nullable();
            $table->decimal('previous_quantity', 15, 5)->comment('Önceki dönem sonu miktarı');
            $table->decimal('new_quantity', 15, 5)->comment('Yeni dönem başlangıç miktarı');
            $table->decimal('difference', 15, 5)->default(0)->comment('Fark (yeni - önceki)');
            $table->decimal('unit_cost', 15, 5)->nullable()->comment('Birim maliyet');
            $table->decimal('total_cost', 15, 5)->nullable()->comment('Toplam maliyet');
            $table->string('currency_code', 10)->nullable();
            $table->string('notes', 255)->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_transfer_items');
    }
};
