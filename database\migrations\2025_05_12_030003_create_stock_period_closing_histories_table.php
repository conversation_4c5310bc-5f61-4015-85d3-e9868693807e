<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_period_closing_histories', function (Blueprint $table) {
            $table->id();
            $table->integer('period_id');
            $table->dateTime('closing_date');
            $table->integer('closing_user_id');
            $table->integer('approver_id')->nullable();
            $table->dateTime('approval_date')->nullable();
            $table->string('notes', 255)->nullable();
            $table->tinyInteger('status_id')->default(1);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_period_closing_histories');
    }
};
