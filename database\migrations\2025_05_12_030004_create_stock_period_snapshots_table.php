<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_period_snapshots', function (Blueprint $table) {
            $table->id();
            $table->integer('period_id');
            $table->integer('product_id')->nullable();
            $table->integer('variant_id')->nullable();
            $table->integer('warehouse_id');
            $table->integer('warehouse_location_id')->nullable();
            $table->integer('stock_batch_id')->nullable();
            $table->decimal('opening_quantity', 15, 5)->default(0)->comment('Dönem başı miktarı');
            $table->decimal('closing_quantity', 15, 5)->default(0)->comment('Dönem sonu miktarı');
            $table->decimal('received_quantity', 15, 5)->default(0)->comment('Dönem içi giriş miktarı');
            $table->decimal('issued_quantity', 15, 5)->default(0)->comment('Dönem içi çıkış miktarı');
            $table->decimal('opening_value', 15, 5)->default(0)->comment('Dönem başı değeri');
            $table->decimal('closing_value', 15, 5)->default(0)->comment('Dönem sonu değeri');
            $table->string('currency_code', 10)->nullable();
            $table->integer('closing_history_id')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_period_snapshots');
    }
};
