<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedTinyInteger('transaction_type')->comment('1: Tahsilat, 2: Ödeme');
            $table->integer('invoice_id')->comment('İlişkili fatura ID');
            $table->unsignedTinyInteger('invoice_type')->comment('1: Satış Faturası, 2: Alış Faturası');
            $table->integer('current_id')->comment('Cari hesap ID');
            $table->dateTime('transaction_date')->comment('İşlem tarihi');
            $table->unsignedTinyInteger('payment_method')->comment('1: <PERSON><PERSON><PERSON>, 2: <PERSON><PERSON>, 3: <PERSON><PERSON>, 4: <PERSON><PERSON>, 5: <PERSON><PERSON>, 6: <PERSON><PERSON>, 7: <PERSON><PERSON><PERSON>, 8: <PERSON><PERSON><PERSON>');
            $table->decimal('amount', 15, 2)->comment('İşlem tutarı');
            $table->string('currency', 3)->default('TRY')->comment('Para birimi');
            $table->decimal('exchange_rate', 10, 4)->default(1.0000)->comment('Döviz kuru');
            $table->text('description')->nullable()->comment('İşlem açıklaması');
            $table->string('document_no', 50)->nullable()->comment('Belge numarası');
            $table->string('bank_account', 100)->nullable()->comment('Banka hesabı');
            $table->date('due_date')->nullable()->comment('Çek/Senet vadesi');
            $table->tinyInteger('is_active')->default(1)->comment('İşlem aktif mi, pasif mi');
            $table->timestamps();
            $table->softDeletes();
            $table->integer('created_by')->nullable()->comment('Kaydı oluşturan kullanıcı ID');
            $table->integer('updated_by')->nullable()->comment('Kaydı güncelleyen kullanıcı ID');
            $table->integer('deleted_by')->nullable()->comment('Kaydı silen kullanıcı ID');
            $table->index('invoice_id');
            $table->index('current_id');
            $table->index('transaction_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
