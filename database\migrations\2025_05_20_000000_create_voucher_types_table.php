<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('voucher_types', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->unique();
            $table->string('name', 100);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Varsayılan fiş türlerini ekle
        $this->seedVoucherTypes();
    }

    public function down(): void
    {
        Schema::dropIfExists('voucher_types');
    }

    private function seedVoucherTypes(): void
    {
        $types = [
            ['code' => 'bank', 'name' => 'Banka Fişi'],
            ['code' => 'check', 'name' => 'Çek'],
            ['code' => 'promissory_note', 'name' => 'Senet'],
            ['code' => 'wire_transfer', 'name' => 'Havale'],
            ['code' => 'eft', 'name' => 'EFT'],
            ['code' => 'other', 'name' => 'Diğer'],
        ];

        $now = now();

        foreach ($types as $type) {
            DB::table('voucher_types')->insert([
                'code' => $type['code'],
                'name' => $type['name'],
                'is_active' => true,
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }
    }
};
