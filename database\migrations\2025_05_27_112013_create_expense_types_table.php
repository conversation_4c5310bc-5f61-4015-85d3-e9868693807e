<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expense_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Varsayılan masraf türle<PERSON> ekle
        DB::table('expense_types')->insert([
            ['name' => 'Ulaşım', 'code' => 'TRANSPORT', 'sort_order' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Kırtasiye', 'code' => 'STATIONERY', 'sort_order' => 2, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Yemek', 'code' => 'FOOD', 'sort_order' => 3, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Konaklama', 'code' => 'ACCOMMODATION', 'sort_order' => 4, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Diğer', 'code' => 'OTHER', 'sort_order' => 5, 'created_at' => now(), 'updated_at' => now()],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expense_types');
    }
};
