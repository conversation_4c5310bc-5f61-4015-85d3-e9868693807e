<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->string('code', 20)->unique();
            $table->text('description')->nullable();
            $table->integer('sort_order')->default(0);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Varsayılan ödeme türlerini ekle
        DB::table('payment_types')->insert([
            ['name' => 'Nakit', 'code' => 'CASH', 'sort_order' => 1, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Banka', 'code' => 'BANK', 'sort_order' => 2, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Çek', 'code' => 'CHECK', 'sort_order' => 3, 'created_at' => now(), 'updated_at' => now()],
            ['name' => 'Kredi Kartı', 'code' => 'CREDIT_CARD', 'sort_order' => 4, 'created_at' => now(), 'updated_at' => now()],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_types');
    }
};
