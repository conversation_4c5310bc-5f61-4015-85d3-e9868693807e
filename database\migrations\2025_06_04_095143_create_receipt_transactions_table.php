<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receipt_transactions', function (Blueprint $table) {
            $table->id();
            $table->integer('current_id');
            $table->timestamp('transaction_date');
            $table->integer('invoice_type')->comment('1: Satış Faturası');
            $table->integer('invoice_id');
            $table->integer('payment_method');
            $table->decimal('amount', 10, 2);
            $table->text('description')->nullable();
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receipt_transactions');
    }
};
