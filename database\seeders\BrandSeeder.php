<?php

namespace Database\Seeders;

use App\Models\Brand;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Transaction içinde çalıştır
        DB::transaction(function () {
            $this->createBrands();
        });
    }

    /**
     * Markaları oluştur
     */
    private function createBrands(): void
    {
        $brands = $this->getBrandData();
        
        foreach ($brands as $brandData) {
            // İlgili kategorileri bul
            $categoryIds = $this->findCategoryIds($brandData['categories']);
            
            $brand = Brand::create([
                'name' => $brandData['name'],
                'description' => $brandData['description'] ?? null,
                'is_active' => $brandData['is_active'] ?? true,
                'created_by' => 1,
                'updated_by' => 1,
            ]);
            
            // Kategorileri many-to-many ilişki ile ekle
            if (!empty($categoryIds)) {
                $brand->categories()->attach($categoryIds);
            }
        }
    }

    /**
     * Kategori isimlerinden ID'leri bul
     * Hem ana kategori hem de alt kategorilerde arama yapar
     */
    private function findCategoryIds(array $categoryNames): array
    {
        $categoryIds = [];
        
        foreach ($categoryNames as $categoryName) {
            // Önce tam eşleşme dene
            $categories = Category::where('name', $categoryName)->get();
            
            // Eğer birden fazla sonuç varsa (aynı isimde birden fazla kategori)
            // En derin olanları tercih et (çünkü genelde alt kategoriler daha spesifik)
            if ($categories->count() > 1) {
                // Parent sayısına göre sırala (en çok parent'a sahip olan en derindir)
                $deepestCategory = $categories->sortByDesc(function ($category) {
                    $depth = 0;
                    $parent = $category->parent;
                    while ($parent) {
                        $depth++;
                        $parent = $parent->parent;
                    }
                    return $depth;
                })->first();
                
                if ($deepestCategory) {
                    $categoryIds[] = $deepestCategory->id;
                }
            } elseif ($categories->count() == 1) {
                $categoryIds[] = $categories->first()->id;
            }
        }
        
        return array_unique($categoryIds);
    }

    /**
     * Marka verilerini döndür
     */
    private function getBrandData(): array
    {
        return [
            // Elektronik Markaları
            [
                'name' => 'Apple',
                'description' => 'Dünyanın en değerli teknoloji şirketi',
                'categories' => [
                    'Akıllı Telefon', 
                    'Tablet', 
                    'Dizüstü Bilgisayar', 
                    'Masaüstü Bilgisayar',
                    'Aksiyon Kamerası',
                    'Kulaklık',
                    'Saat'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Samsung',
                'description' => 'Güney Kore merkezli teknoloji devi',
                'categories' => [
                    'Akıllı Telefon',
                    'Tablet',
                    'Televizyon',
                    'Monitör',
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Klima',
                    'Mikrodalga Fırın'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Sony',
                'categories' => [
                    'PlayStation',
                    'Televizyon',
                    'Ses Sistemleri',
                    'Kulaklık',
                    'DSLR Fotoğraf Makinesi',
                    'Aynasız Fotoğraf Makinesi',
                    'Kamera Lensleri'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'LG',
                'categories' => [
                    'Televizyon',
                    'Monitör',
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Klima',
                    'Mikrodalga Fırın'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Lenovo',
                'categories' => [
                    'Dizüstü Bilgisayar',
                    'Masaüstü Bilgisayar',
                    'Tablet',
                    'Monitör',
                    'Gaming Bilgisayar',
                    'Workstation'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'HP',
                'categories' => [
                    'Dizüstü Bilgisayar',
                    'Masaüstü Bilgisayar',
                    'Yazıcı',
                    'Tarayıcı',
                    'Monitör',
                    'Workstation'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Dell',
                'categories' => [
                    'Dizüstü Bilgisayar',
                    'Masaüstü Bilgisayar',
                    'Monitör',
                    'Gaming Bilgisayar',
                    'Workstation'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Asus',
                'categories' => [
                    'Dizüstü Bilgisayar',
                    'Masaüstü Bilgisayar',
                    'Anakart',
                    'Ekran Kartı',
                    'Monitör',
                    'Gaming Bilgisayar'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'MSI',
                'categories' => [
                    'Gaming Bilgisayar',
                    'Dizüstü Bilgisayar',
                    'Anakart',
                    'Ekran Kartı',
                    'Monitör'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Intel',
                'categories' => [
                    'İşlemci',
                    'SSD'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'AMD',
                'categories' => [
                    'İşlemci',
                    'Ekran Kartı'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'NVIDIA',
                'categories' => [
                    'Ekran Kartı'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Logitech',
                'categories' => [
                    'Klavye',
                    'Mouse',
                    'Webcam',
                    'Hoparlör',
                    'Kulaklık',
                    'Mikrofon'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Canon',
                'categories' => [
                    'DSLR Fotoğraf Makinesi',
                    'Aynasız Fotoğraf Makinesi',
                    'Kompakt Fotoğraf Makinesi',
                    'Kamera Lensleri',
                    'Yazıcı',
                    'Tarayıcı'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Nikon',
                'categories' => [
                    'DSLR Fotoğraf Makinesi',
                    'Aynasız Fotoğraf Makinesi',
                    'Kompakt Fotoğraf Makinesi',
                    'Kamera Lensleri'
                ],
                'is_active' => true,
            ],
            
            // Beyaz Eşya Markaları
            [
                'name' => 'Bosch',
                'categories' => [
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Bulaşık Makinesi',
                    'Fırın',
                    'Ocak',
                    'Aspiratör',
                    'Süpürge',
                    'Elektrikli El Aletleri'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Siemens',
                'categories' => [
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Bulaşık Makinesi',
                    'Fırın',
                    'Ocak',
                    'Kahve Makinesi'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Arçelik',
                'categories' => [
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Bulaşık Makinesi',
                    'Fırın',
                    'Televizyon',
                    'Klima',
                    'Süpürge'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Beko',
                'categories' => [
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Bulaşık Makinesi',
                    'Fırın',
                    'Mikrodalga Fırın',
                    'Derin Dondurucu'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Vestel',
                'categories' => [
                    'Televizyon',
                    'Buzdolabı',
                    'Çamaşır Makinesi',
                    'Klima',
                    'Akıllı Telefon'
                ],
                'is_active' => true,
            ],
            
            // Giyim Markaları
            [
                'name' => 'Nike',
                'description' => 'Dünyanın en büyük spor giyim markası',
                'categories' => [
                    'Koşu', // Spor ayakkabı alt kategorisi
                    'Basketbol',
                    'Futbol',
                    'Fitness',
                    'Outdoor',
                    'T-Shirt', // Kadın ve Erkek Giyim altında
                    'Pantolon',
                    'Şort',
                    'Sırt Çantası'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Adidas',
                'description' => 'Üç şeritli dünya markası',
                'categories' => [
                    'Koşu',
                    'Basketbol',
                    'Futbol',
                    'Fitness',
                    'Outdoor',
                    'T-Shirt',
                    'Pantolon',
                    'Şort',
                    'Sırt Çantası'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Puma',
                'description' => 'Alman spor giyim markası',
                'categories' => [
                    'Koşu',
                    'Basketbol',
                    'Futbol',
                    'Fitness',
                    'T-Shirt',
                    'Pantolon',
                    'Sırt Çantası'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Zara',
                'description' => 'Hızlı moda öncüsü İspanyol markası',
                'categories' => [
                    'Elbise',
                    'Etek',
                    'Pantolon',
                    'Gömlek',
                    'Gömlek ve Bluz', // Kadın giyim
                    'T-Shirt',
                    'Ceket ve Mont',
                    'Günlük', // Ayakkabı kategorisi
                    'Klasik',
                    'El Çantası',
                    'Omuz Çantası'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'H&M',
                'description' => 'İsveç merkezli hızlı moda markası',
                'categories' => [
                    'Elbise',
                    'Pantolon',
                    'Gömlek',
                    'T-Shirt',
                    'Body ve Zıbın', // Bebek
                    'Tulum',
                    'Elbise', // Kız çocuk
                    'Pantolon' // Erkek çocuk
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Mango',
                'description' => 'İspanyol moda markası',
                'categories' => [
                    'Elbise',
                    'Etek',
                    'Pantolon',
                    'Gömlek ve Bluz',
                    'Günlük',
                    'Klasik',
                    'El Çantası'
                ],
                'is_active' => true,
            ],
            [
                'name' => "Levi's",
                'description' => 'Kot pantolonun mucidi',
                'categories' => [
                    'Kot Pantolon',
                    'Pantolon',
                    'Gömlek',
                    'T-Shirt',
                    'Ceket ve Mont'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'LC Waikiki',
                'categories' => [
                    'Giyim',
                    'Elbise',
                    'Pantolon',
                    'Gömlek',
                    'T-Shirt',
                    'Bebek',
                    'Kız Çocuk',
                    'Erkek Çocuk',
                    'Ev Tekstili'
                ],
                'is_active' => true,
            ],
            
            // Kozmetik Markaları
            [
                'name' => "L'Oréal",
                'categories' => [
                    'Fondöten',
                    'Ruj',
                    'Rimel',
                    'Şampuan',
                    'Saç Kremi',
                    'Saç Boyası'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Maybelline',
                'categories' => [
                    'Fondöten',
                    'Ruj',
                    'Rimel',
                    'Göz Farı',
                    'Allık',
                    'Kapatıcı'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'MAC',
                'categories' => [
                    'Fondöten',
                    'Ruj',
                    'Rimel',
                    'Göz Farı',
                    'Allık',
                    'Makyaj Fırçası'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Nivea',
                'categories' => [
                    'Nemlendirici',
                    'Güneş Kremi',
                    'Deodorant',
                    'Roll-On',
                    'Yüz Temizleyici'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Dove',
                'categories' => [
                    'Şampuan',
                    'Saç Kremi',
                    'Deodorant',
                    'Roll-On'
                ],
                'is_active' => true,
            ],
            
            // Mobilya Markaları
            [
                'name' => 'IKEA',
                'categories' => [
                    'Oturma Odası Mobilyaları',
                    'Yatak Odası Mobilyaları',
                    'Mutfak Mobilyaları',
                    'Çocuk Odası Mobilyaları',
                    'Çalışma Odası Mobilyaları',
                    'Bahçe Mobilyaları',
                    'Aydınlatma',
                    'Dekorasyon'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Bellona',
                'categories' => [
                    'Oturma Odası Mobilyaları',
                    'Yatak Odası Mobilyaları',
                    'Mutfak Mobilyaları',
                    'Çocuk Odası Mobilyaları'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'İstikbal',
                'categories' => [
                    'Oturma Odası Mobilyaları',
                    'Yatak Odası Mobilyaları',
                    'Mutfak Mobilyaları',
                    'Çocuk Odası Mobilyaları'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Koçtaş',
                'categories' => [
                    'Boya',
                    'El Aletleri',
                    'Elektrikli El Aletleri',
                    'Hırdavat',
                    'Elektrik Malzemeleri',
                    'Tesisat Malzemeleri',
                    'Bahçe El Aletleri'
                ],
                'is_active' => true,
            ],
            
            // Oyuncak Markaları
            [
                'name' => 'LEGO',
                'categories' => [
                    'Lego ve Yapı Oyuncakları'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Barbie',
                'categories' => [
                    'Bebek ve Peluş Oyuncaklar'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Hot Wheels',
                'categories' => [
                    'Uzaktan Kumandalı Oyuncaklar',
                    'Aksiyon Figürleri'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Fisher-Price',
                'categories' => [
                    'Eğitici Oyuncaklar',
                    'Bebek ve Peluş Oyuncaklar'
                ],
                'is_active' => true,
            ],
            
            // Bisiklet Markaları
            [
                'name' => 'Bianchi',
                'description' => 'İtalyan bisiklet üreticisi',
                'categories' => [
                    'Yol Bisikleti',
                    'Dağ Bisikleti',
                    'Şehir Bisikleti',
                    'Lastik ve Jant', // Bisiklet Parçaları
                    'Sele',
                    'Gidon'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Giant',
                'description' => 'Dünyanın en büyük bisiklet üreticisi',
                'categories' => [
                    'Dağ Bisikleti',
                    'Yol Bisikleti',
                    'Şehir Bisikleti',
                    'Elektrikli Bisiklet',
                    'Çocuk Bisikleti',
                    'Lastik ve Jant',
                    'Sele',
                    'Gidon'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Trek',
                'description' => 'Amerikan bisiklet markası',
                'categories' => [
                    'Dağ Bisikleti',
                    'Yol Bisikleti',
                    'Şehir Bisikleti',
                    'Elektrikli Bisiklet',
                    'Lastik ve Jant',
                    'Sele',
                    'Gidon'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Shimano',
                'description' => 'Bisiklet parçaları üreticisi',
                'categories' => [
                    'Vites Sistemi',
                    'Fren Sistemi',
                    'Zincir',
                    'Lastik ve Jant'
                ],
                'is_active' => true,
            ],
            
            // Spor Markaları
            [
                'name' => 'Decathlon',
                'description' => 'Fransız spor mağazalar zinciri',
                'categories' => [
                    'Koşu Bandı',
                    'Kondisyon Bisikleti',
                    'Dağ Bisikleti',
                    'Yol Bisikleti',
                    'Şehir Bisikleti',
                    'Kask', // Bisiklet Aksesuarları
                    'Bisiklet Kilit',
                    'Bisiklet Işıkları',
                    'Çadır',
                    'Uyku Tulumu',
                    'Kamp Sandalyesi',
                    'Yoga Matı'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'The North Face',
                'description' => 'Outdoor giyim ve ekipman markası',
                'categories' => [
                    'Ceket ve Mont',
                    'Sırt Çantası',
                    'Çadır',
                    'Uyku Tulumu'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Columbia',
                'description' => 'Amerikan outdoor giyim markası',
                'categories' => [
                    'Ceket ve Mont',
                    'Pantolon',
                    'Bot', // Bot ve Çizme yerine
                    'Outdoor',
                    'Sırt Çantası'
                ],
                'is_active' => true,
            ],
            
            // Kitap Yayınevi
            [
                'name' => 'Can Yayınları',
                'categories' => [
                    'Roman',
                    'Öykü',
                    'Şiir'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'İş Bankası Kültür Yayınları',
                'categories' => [
                    'Roman',
                    'Tarih',
                    'Bilim',
                    'Çocuk Kitapları'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Yapı Kredi Yayınları',
                'categories' => [
                    'Roman',
                    'Şiir',
                    'Öykü',
                    'Tarih'
                ],
                'is_active' => true,
            ],
            
            // Gıda Markaları
            [
                'name' => 'Ülker',
                'categories' => [
                    'Çikolata',
                    'Bisküvi',
                    'Kraker'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Eti',
                'categories' => [
                    'Çikolata',
                    'Bisküvi',
                    'Kraker',
                    'Lokum ve Şekerleme'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Nestle',
                'categories' => [
                    'Çikolata',
                    'Kahve',
                    'Su',
                    'Bebek Maması'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Coca-Cola',
                'categories' => [
                    'Gazlı İçecek',
                    'Meyve Suyu',
                    'Su',
                    'Enerji İçeceği'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Pepsi',
                'categories' => [
                    'Gazlı İçecek',
                    'Cips'
                ],
                'is_active' => true,
            ],
            
            // Pet Shop Markaları
            [
                'name' => 'Royal Canin',
                'categories' => [
                    'Kedi Maması',
                    'Köpek Maması'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Pro Plan',
                'categories' => [
                    'Kedi Maması',
                    'Köpek Maması'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Whiskas',
                'categories' => [
                    'Kedi Maması'
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Pedigree',
                'categories' => [
                    'Köpek Maması'
                ],
                'is_active' => true,
            ],
        ];
    }
}
