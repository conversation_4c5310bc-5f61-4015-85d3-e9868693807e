<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call([
            RouteSeeder::class,
            RoleSeeder::class,
            UserSeeder::class,
            CategorySeeder::class,
            BrandSeeder::class,
            UnitTypeSeeder::class,
            UnitSeeder::class,
            StatusSeeder::class,
            WarehouseSeeder::class,
            WarehouseLocationSeeder::class,
            StockMovementTypeSeeder::class,
            StockMovementReasonSeeder::class,
            StockReservationTypeSeeder::class,
            StockReservationReasonSeeder::class,
            ProductSeeder::class,
            StockSeeder::class,
            OfferTypeSeeder::class,
            OrderTypeSeeder::class,
            CurrentTypeSeeder::class,
            InsutationTypeSeeder::class,
        ]);
    }
}
