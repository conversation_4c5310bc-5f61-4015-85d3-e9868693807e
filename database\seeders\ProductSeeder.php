<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\UnitType;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Önce birim tipleri ve birimleri oluşturalım
        $unitTypes = [
            'Uzunluk' => ['Metre', 'Santimetre', 'Milimetre'],
            'Ağırlık' => ['Kilogram', 'Gram', 'Ton'],
            'Adet' => ['Adet', 'Paket', 'Koli', 'Düzine'],
            'Hacim' => ['Litre', 'Mililitre', 'Metreküp']
        ];

        foreach ($unitTypes as $typeName => $units) {
            $unitType = UnitType::firstOrCreate(['name' => $typeName]);
            
            foreach ($units as $unitName) {
                Unit::firstOrCreate([
                    'name' => $unitName,
                    'symbol' => substr($unitName, 0, 2),
                    'unit_type_id' => $unitType->id
                ]);
            }
        }

        // Kategorileri ve markaları al
        $categories = Category::all();
        $brands = Brand::all();
        $units = Unit::all();

        if ($categories->isEmpty() || $brands->isEmpty()) {
            $this->command->warn('Lütfen önce CategorySeeder ve BrandSeeder\'ı çalıştırın!');
            return;
        }

        $products = [
            // Elektronik Ürünler
            [
                'name' => 'iPhone 15 Pro Max',
                'category' => 'Cep Telefonu',
                'brand' => 'Apple',
                'purchase_price' => 45000,
                'sale_price' => 54999,
                'unit' => 'Adet',
                'critical_stock_level' => 5,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Samsung Galaxy S24 Ultra',
                'category' => 'Cep Telefonu',
                'brand' => 'Samsung',
                'purchase_price' => 40000,
                'sale_price' => 48999,
                'unit' => 'Adet',
                'critical_stock_level' => 5,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'MacBook Pro 16" M3',
                'category' => 'Dizüstü Bilgisayar',
                'brand' => 'Apple',
                'purchase_price' => 85000,
                'sale_price' => 99999,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'ASUS ROG Strix G16',
                'category' => 'Dizüstü Bilgisayar',
                'brand' => 'ASUS',
                'purchase_price' => 35000,
                'sale_price' => 42999,
                'unit' => 'Adet',
                'critical_stock_level' => 3,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'LG OLED65C3PSA 65" 4K TV',
                'category' => 'Televizyon',
                'brand' => 'LG',
                'purchase_price' => 28000,
                'sale_price' => 34999,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'default_vat_rate' => 20
            ],
            
            // Giyim Ürünleri
            [
                'name' => 'Nike Air Max 270',
                'category' => 'Koşu',
                'brand' => 'Nike',
                'purchase_price' => 1200,
                'sale_price' => 1899,
                'unit' => 'Adet',
                'critical_stock_level' => 10,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Adidas Ultraboost 22',
                'category' => 'Koşu',
                'brand' => 'Adidas',
                'purchase_price' => 1500,
                'sale_price' => 2299,
                'unit' => 'Adet',
                'critical_stock_level' => 10,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Zara Kadın Blazer Ceket',
                'category' => 'Ceket',
                'brand' => 'Zara',
                'purchase_price' => 400,
                'sale_price' => 799,
                'unit' => 'Adet',
                'critical_stock_level' => 15,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'H&M Erkek Slim Fit Pantolon',
                'category' => 'Pantolon',
                'brand' => 'H&M',
                'purchase_price' => 200,
                'sale_price' => 399,
                'unit' => 'Adet',
                'critical_stock_level' => 20,
                'default_vat_rate' => 20
            ],
            
            // Bisiklet Ürünleri
            [
                'name' => 'Bianchi Via Nirone 7',
                'category' => 'Yol Bisikleti',
                'brand' => 'Bianchi',
                'purchase_price' => 15000,
                'sale_price' => 22999,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Giant Talon 29 2',
                'category' => 'Dağ Bisikleti',
                'brand' => 'Giant',
                'purchase_price' => 12000,
                'sale_price' => 17999,
                'unit' => 'Adet',
                'critical_stock_level' => 3,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Trek FX 3 Disc',
                'category' => 'Şehir Bisikleti',
                'brand' => 'Trek',
                'purchase_price' => 8000,
                'sale_price' => 12999,
                'unit' => 'Adet',
                'critical_stock_level' => 4,
                'default_vat_rate' => 20
            ],
            
            // Ev ve Yaşam
            [
                'name' => 'IKEA EKTORP 3 Kişilik Kanepe',
                'category' => 'Kanepe & Koltuk',
                'brand' => 'IKEA',
                'purchase_price' => 3500,
                'sale_price' => 5999,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Bosch Serie 6 Bulaşık Makinesi',
                'category' => 'Bulaşık Makinesi',
                'brand' => 'Bosch',
                'purchase_price' => 8000,
                'sale_price' => 11999,
                'unit' => 'Adet',
                'critical_stock_level' => 3,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Arçelik No-Frost Buzdolabı',
                'category' => 'Buzdolabı',
                'brand' => 'Arçelik',
                'purchase_price' => 12000,
                'sale_price' => 17999,
                'unit' => 'Adet',
                'critical_stock_level' => 2,
                'default_vat_rate' => 20
            ],
            
            // Gıda Ürünleri
            [
                'name' => 'Nestle Fitness Tahıllı Gevrek 400g',
                'category' => 'Kahvaltılık',
                'brand' => 'Nestle',
                'purchase_price' => 25,
                'sale_price' => 45.90,
                'unit' => 'Adet',
                'critical_stock_level' => 50,
                'default_vat_rate' => 10
            ],
            [
                'name' => 'Ülker Çikolatalı Gofret',
                'category' => 'Bisküvi & Kraker',
                'brand' => 'Ülker',
                'purchase_price' => 8,
                'sale_price' => 14.90,
                'unit' => 'Adet',
                'critical_stock_level' => 100,
                'default_vat_rate' => 10
            ],
            
            // Kozmetik
            [
                'name' => 'L\'Oreal Paris Revitalift Gündüz Kremi',
                'category' => 'Cilt Bakımı',
                'brand' => 'L\'Oreal',
                'purchase_price' => 120,
                'sale_price' => 249.90,
                'unit' => 'Adet',
                'critical_stock_level' => 20,
                'default_vat_rate' => 20
            ],
            [
                'name' => 'Maybelline Fit Me Fondöten',
                'category' => 'Makyaj',
                'brand' => 'Maybelline',
                'purchase_price' => 80,
                'sale_price' => 159.90,
                'unit' => 'Adet',
                'critical_stock_level' => 25,
                'default_vat_rate' => 20
            ]
        ];

        foreach ($products as $productData) {
            $category = $categories->where('name', $productData['category'])->first();
            $brand = $brands->where('name', $productData['brand'])->first();
            $unit = $units->where('name', $productData['unit'])->first();

            if ($category && $brand && $unit) {
                Product::create([
                    'name' => $productData['name'],
                    'sku' => Product::generateSku($category->name, $productData['name']),
                    'description' => $productData['name'] . ' ürünü',
                    'category_id' => $category->id,
                    'brand_id' => $brand->id,
                    'unit_id' => $unit->id,
                    'unit_type_id' => $unit->unit_type_id,
                    'purchase_price' => $productData['purchase_price'],
                    'sale_price' => $productData['sale_price'],
                    'purchase_currency_code' => 'TRY',
                    'sale_currency_code' => 'TRY',
                    'critical_stock_level' => $productData['critical_stock_level'],
                    'default_vat_rate' => $productData['default_vat_rate'],
                    'is_active' => true
                ]);
            }
        }

        $this->command->info('ProductSeeder başarıyla tamamlandı!');
    }
}
