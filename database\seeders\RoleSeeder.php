<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Role<PERSON>eeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $items = [
            [
                'name' => 'Süper Admin',
                'slug' => 'super-admin',
                'permissions' => json_encode(
                    [
                        "backend.role_list",
                        "backend.role_form",
                        "backend.role_save",
                        "backend.role_delete",
                        "backend.user_list",
                        "backend.user_form",
                        "backend.user_save",
                        "backend.user_delete",
                    ]
                ),
            ],
        ];

        DB::table('roles')->insert($items);
    }
}
