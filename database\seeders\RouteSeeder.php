<?php

namespace Database\Seeders;

use App\Models\Route;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RouteSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
      $items = [
            [
                'category_name' => 'Kullanıcılar',
                'route_name' => 'backend.user_list',
                'name' => 'Listeleme',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Kullanıcılar',
                'route_name' => 'backend.user_form',
                'name' => 'Form',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Kullanıcılar',
                'route_name' => 'backend.user_save',
                'name' => 'Kaydetme',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Kullanıcılar',
                'route_name' => 'backend.user_delete',
                'name' => 'Silme',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Roller',
                'route_name' => 'backend.role_list',
                'name' => 'Listeleme',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Roller',
                'route_name' => 'backend.role_form',
                'name' => 'Form',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Roller',
                'route_name' => 'backend.role_save',
                'name' => 'Kaydetme',
                'is_active' => 1,
            ],
            [
                'category_name' => 'Roller',
                'route_name' => 'backend.role_delete',
                'name' => 'Silme',
                'is_active' => 1,
            ],

      ];

        DB::table('routes')->insert($items);
    }
}
