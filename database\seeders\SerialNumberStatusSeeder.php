<?php

namespace Database\Seeders;

use App\Models\SerialNumberStatus;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SerialNumberStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $statuses = [
            ['name' => 'Depoda'],
            ['name' => 'Satıldı'],
            ['name' => 'Arızal<PERSON>'],
            ['name' => 'Onarımda'],
            ['name' => 'Kullanıldı'],
            ['name' => 'İade Edildi'],
        ];

        foreach ($statuses as $status) {
            SerialNumberStatus::updateOrCreate(
                ['name' => $status['name']],
                $status
            );
        }
    }
}
