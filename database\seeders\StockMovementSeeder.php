<?php

namespace Database\Seeders;

use App\Models\StockMovement;
use App\Models\Stock;
use App\Models\StockBatch;
use App\Models\StockMovementReason;
use App\Models\StockMovementType;
use App\Models\Status;
use App\Models\User;
use App\Models\CurrentAccount;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class StockMovementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Gerekli verileri al
        $stocks = Stock::with(['product', 'variant', 'warehouse', 'warehouseLocation'])->get();
        $batches = StockBatch::all();
        $movementTypes = StockMovementType::all()->keyBy('name');
        $movementReasons = StockMovementReason::all();
        $statuses = Status::all()->keyBy('name');
        $users = User::all();
        // $currentAccounts = CurrentAccount::all();
        $warehouses = Warehouse::all();

        // Gerekli verileri kontrol et
        if ($stocks->isEmpty() || $users->isEmpty()  || $statuses->isEmpty()) {
            echo "Gerekli veriler bulunamadı. Önce diğer seeder'ları çalıştırın.\n";
            return;
        }

        // Her bir hareket tipi için nedenler
        $entryReasons = $movementReasons->where('movement_type_id', $movementTypes->get('Giriş')?->id);
        $exitReasons = $movementReasons->where('movement_type_id', $movementTypes->get('Çıkış')?->id);
        $transferReasons = $movementReasons->where('movement_type_id', $movementTypes->get('Transfer')?->id);

        $movements = [];

        // Geçmiş tarihli hareketler oluştur
        foreach ($stocks->take(50) as $stock) {
            // Giriş hareketi (Alım)
            $alimReason = $entryReasons->where('name', 'Alım')->first();

            if ($alimReason) {
                $movements[] = [
                    'stock_id' => $stock->id,
                    'stock_batch_id' => $batches->where('product_id', $stock->product_id)->first()?->id ?? null,
                    'product_id' => $stock->product_id,
                    'variant_id' => $stock->variant_id,
                    'unit_id' => $stock->product->unit_id,
                    'base_quantity' => $stock->quantity,
                    'quantity' => $stock->quantity,
                    // 'current_account_id' => $currentAccounts->where('type_id', 1)->first()?->id ?? $currentAccounts->first()?->id, // Tedarikçi
                    'starter_id' => $users->random()->id,
                    'approver_id' => $users->random()->id,
                    'approval_date' => Carbon::now()->subDays(rand(60, 90)),
                    'movement_date' => Carbon::now()->subDays(rand(60, 90)),
                    'status_id' => $statuses['Tamamlandı']->id,
                    'stock_movement_reason_id' => $alimReason->id,
                    'notes' => 'İlk stok girişi',
                    'warehouse_id' => $stock->warehouse_id,
                    'location_id' => $stock->warehouse_location_id,
                    'target_warehouse_id' => null,
                    'target_location_id' => null,
                    'is_active' => true,
                ];
            }

            // Satış hareketleri (Çıkış)
            if (rand(0, 1)) {
                $satisReason = $exitReasons->where('name', 'Satış')->first();

                if ($satisReason) {
                    $saleQuantity = rand(1, min(10, $stock->quantity));

                    $movements[] = [
                        'stock_id' => $stock->id,
                        'stock_batch_id' => $batches->where('product_id', $stock->product_id)->first()?->id ?? null,
                        'product_id' => $stock->product_id,
                        'variant_id' => $stock->variant_id,
                        'unit_id' => $stock->product->unit_id,
                        'base_quantity' => $saleQuantity,
                        'quantity' => $saleQuantity,
                        // 'current_account_id' => $currentAccounts->where('type_id', 2)->first()?->id ?? $currentAccounts->first()?->id, // Müşteri
                        'starter_id' => $users->random()->id,
                        'approver_id' => $users->random()->id,
                        'approval_date' => Carbon::now()->subDays(rand(1, 30)),
                        'movement_date' => Carbon::now()->subDays(rand(1, 30)),
                        'status_id' => $statuses['Tamamlandı']->id,
                        'stock_movement_reason_id' => $satisReason->id,
                        'notes' => 'Satış işlemi',
                        'warehouse_id' => $stock->warehouse_id,
                        'location_id' => $stock->warehouse_location_id,
                        'target_warehouse_id' => null,
                        'target_location_id' => null,
                        'is_active' => true,
                    ];
                }
            }

            // Transfer hareketleri
            if (rand(0, 2) === 0 && $warehouses->count() > 1) {
                $transferOutReason = $exitReasons->where('name', 'Transfer Çıkış')->first();
                $transferInReason = $entryReasons->where('name', 'Transfer Giriş')->first();

                // Transfer nedenleri yoksa atla
                if (!$transferOutReason || !$transferInReason) {
                    continue;
                }

                $transferQuantity = rand(1, min(5, $stock->quantity));
                $targetWarehouse = $warehouses->where('id', '!=', $stock->warehouse_id)->random();
                $targetLocation = WarehouseLocation::where('warehouse_id', $targetWarehouse->id)->inRandomOrder()->first();

                if (!$targetLocation) {
                    continue; // Eğer hedef lokasyon yoksa bu transferi atla
                }

                // Çıkış hareketi (Transfer çıkış)
                $movements[] = [
                    'stock_id' => $stock->id,
                    'stock_batch_id' => null,
                    'product_id' => $stock->product_id,
                    'variant_id' => $stock->variant_id,
                    'unit_id' => $stock->product->unit_id,
                    'base_quantity' => $transferQuantity,
                    'quantity' => $transferQuantity,
                    'current_account_id' => null,
                    'starter_id' => $users->random()->id,
                    'approver_id' => $users->random()->id,
                    'approval_date' => Carbon::now()->subDays(rand(5, 20)),
                    'movement_date' => Carbon::now()->subDays(rand(5, 20)),
                    'status_id' => $statuses['Tamamlandı']->id,
                    'stock_movement_reason_id' => $transferOutReason->id,
                    'notes' => 'Depolar arası transfer - Çıkış',
                    'warehouse_id' => $stock->warehouse_id,
                    'location_id' => $stock->warehouse_location_id,
                    'target_warehouse_id' => $targetWarehouse->id,
                    'target_location_id' => $targetLocation?->id,
                    'is_active' => true,
                ];

                // Transfer giriş için hedef depoda stok oluştur
                $targetStock = Stock::firstOrCreate(
                    [
                        'product_id' => $stock->product_id,
                        'variant_id' => $stock->variant_id,
                        'warehouse_id' => $targetWarehouse->id,
                        'warehouse_location_id' => $targetLocation->id,
                    ],
                    [
                        'stock_batch_id' => null,
                        'quantity' => 0, // Başlangıç miktarı 0
                        'notes' => 'Transfer için oluşturuldu',
                        'is_active' => true,
                    ]
                );

                // Giriş hareketi (Transfer giriş)
                $movements[] = [
                    'stock_id' => $targetStock->id, // Yeni oluşturulan stok ID'si
                    'stock_batch_id' => null,
                    'product_id' => $stock->product_id,
                    'variant_id' => $stock->variant_id,
                    'unit_id' => $stock->product->unit_id,
                    'base_quantity' => $transferQuantity,
                    'quantity' => $transferQuantity,
                    'current_account_id' => null,
                    'starter_id' => $users->random()->id,
                    'approver_id' => $users->random()->id,
                    'approval_date' => Carbon::now()->subDays(rand(5, 20)),
                    'movement_date' => Carbon::now()->subDays(rand(5, 20)),
                    'status_id' => $statuses['Tamamlandı']->id,
                    'stock_movement_reason_id' => $transferInReason->id,
                    'notes' => 'Depolar arası transfer - Giriş',
                    'warehouse_id' => $targetWarehouse->id,
                    'location_id' => $targetLocation?->id,
                    'target_warehouse_id' => null,
                    'target_location_id' => null,
                    'is_active' => true,
                ];
            }
        }

        // Bekleyen hareketler oluştur
        for ($i = 0; $i < 10; $i++) {
            $randomStock = $stocks->random();
            $movements[] = [
                'stock_id' => $randomStock->id,
                'stock_batch_id' => null,
                'product_id' => $randomStock->product_id,
                'variant_id' => $randomStock->variant_id,
                'unit_id' => $randomStock->product->unit_id,
                'base_quantity' => rand(1, 10),
                'quantity' => rand(1, 10),
                // 'current_account_id' => $currentAccounts->first()?->id,
                'starter_id' => $users->random()->id,
                'approver_id' => null,
                'approval_date' => null,
                'movement_date' => Carbon::now()->addDays(rand(1, 10)),
                'status_id' => $statuses['Beklemede']->id,
                'stock_movement_reason_id' => $exitReasons->first()?->id,
                'notes' => 'Onay bekliyor',
                'warehouse_id' => $randomStock->warehouse_id,
                'location_id' => $randomStock->warehouse_location_id,
                'target_warehouse_id' => null,
                'target_location_id' => null,
                'is_active' => true,
            ];
        }

        // Hareketleri kaydet
        foreach ($movements as $movement) {
            StockMovement::create($movement);
        }
    }
}
