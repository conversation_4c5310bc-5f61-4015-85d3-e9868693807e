<?php

namespace Database\Seeders;

use App\Models\StockMovementType;
use Illuminate\Database\Seeder;

class StockMovementTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            ['name' => 'Giri<PERSON>', 'description' => 'Depoya mal girişi'],
            ['name' => 'Çıkış', 'description' => 'Depodan mal çıkışı'],
            ['name' => 'Transfer', 'description' => 'Depodan depoya mal transferi'],
        ];

        foreach ($types as $type) {
            StockMovementType::updateOrCreate(
                ['name' => $type['name']],
                $type
            );
        }
    }
}
