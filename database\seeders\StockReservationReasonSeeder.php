<?php

namespace Database\Seeders;

use App\Models\StockReservationReason;
use App\Models\StockReservationType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StockReservationReasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Önce StockReservationType'ların var olduğundan emin olalım
        $this->call(StockReservationTypeSeeder::class);

        // Rezervasyon tiplerini alalım
        $salesOrderType = StockReservationType::where('name', 'Satış Siparişi')->first();
        $productionType = StockReservationType::where('name', 'Üretim')->first();
        $transferType = StockReservationType::where('name', 'Transfer')->first();
        $otherType = StockReservationType::where('name', 'Diğer')->first();

        $reasons = [
            ['name' => 'Müşteri Siparişi', 'description' => 'Müşteri siparişi için rezerve', 'stock_reservation_type_id' => $salesOrderType->id],
            ['name' => 'Öncelikli Sipariş', 'description' => 'Öncelikli sipariş için rezerve', 'stock_reservation_type_id' => $salesOrderType->id],
            ['name' => 'Üretim İhtiyacı', 'description' => 'Üretim ihtiyacı için rezerve', 'stock_reservation_type_id' => $productionType->id],
            ['name' => 'Transfer', 'description' => 'Transfer için rezerve', 'stock_reservation_type_id' => $transferType->id],
            ['name' => 'Dahili Kullanım', 'description' => 'Dahili kullanım için rezerve', 'stock_reservation_type_id' => $otherType->id],
            ['name' => 'Diğer', 'description' => 'Diğer rezervasyonlar', 'stock_reservation_type_id' => $otherType->id],
        ];

        foreach ($reasons as $reason) {
            StockReservationReason::updateOrCreate(
                ['name' => $reason['name']],
                $reason
            );
        }
    }
}
