<?php

namespace Database\Seeders;

use App\Models\StockReservationType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StockReservationTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            ['name' => 'Satış Siparişi', 'description' => 'Satış siparişi için rezervasyon'],
            ['name' => 'Üretim', 'description' => 'Üretim için rezervasyon'],
            ['name' => 'Transfer', 'description' => 'Transfer için rezervasyon'],
            ['name' => '<PERSON>ğ<PERSON>', 'description' => 'Diğer rezervasyonlar'],
        ];

        foreach ($types as $type) {
            StockReservationType::updateOrCreate(
                ['name' => $type['name']],
                $type
            );
        }
    }
}
