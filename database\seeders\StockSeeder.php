<?php

namespace Database\Seeders;

use App\Models\Stock;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Warehouse;
use App\Models\WarehouseLocation;
use Illuminate\Database\Seeder;

class StockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Depo ve lokasyonları al
        $warehouses = Warehouse::all()->keyBy('code');
        $locations = WarehouseLocation::all();
        
        // Ana depo lokasyonlarını grupla
        $mainWarehouseLocations = $locations->where('warehouse_id', $warehouses->get('ANA-01')?->id);
        $izmirWarehouseLocations = $locations->where('warehouse_id', $warehouses->get('IZM-01')?->id);
        $ankaraWarehouseLocations = $locations->where('warehouse_id', $warehouses->get('ANK-01')?->id);

        // Ürün ve varyantları al
        $products = Product::where('is_active', true)->get();
        
        $stocks = [];

        foreach ($products as $product) {
            // Varyantsız ürünler için stok oluştur
            if ($product->variants->count() === 0) {
                // Ana depoda stok
                if ($mainWarehouseLocations->isNotEmpty()) {
                    $stocks[] = [
                        'product_id' => $product->id,
                        'variant_id' => null,
                        'warehouse_id' => $warehouses['ANA-01']->id,
                        'warehouse_location_id' => $mainWarehouseLocations->random()->id,
                        'stock_batch_id' => null,
                        'quantity' => rand(50, 200),
                        'notes' => 'Başlangıç stoğu',
                        'is_active' => true,
                    ];
                }

                // İzmir deposunda stok
                if (rand(0, 1) && $izmirWarehouseLocations->isNotEmpty()) { // %50 ihtimalle İzmir'de de stok olsun
                    $stocks[] = [
                        'product_id' => $product->id,
                        'variant_id' => null,
                        'warehouse_id' => $warehouses['IZM-01']->id,
                        'warehouse_location_id' => $izmirWarehouseLocations->random()->id,
                        'stock_batch_id' => null,
                        'quantity' => rand(20, 80),
                        'notes' => 'Başlangıç stoğu',
                        'is_active' => true,
                    ];
                }

                // Ankara deposunda stok
                if (rand(0, 1) && $ankaraWarehouseLocations->isNotEmpty()) { // %50 ihtimalle Ankara'da da stok olsun
                    $stocks[] = [
                        'product_id' => $product->id,
                        'variant_id' => null,
                        'warehouse_id' => $warehouses['ANK-01']->id,
                        'warehouse_location_id' => $ankaraWarehouseLocations->random()->id,
                        'stock_batch_id' => null,
                        'quantity' => rand(10, 50),
                        'notes' => 'Başlangıç stoğu',
                        'is_active' => true,
                    ];
                }
            } else {
                // Varyantlı ürünler için stok oluştur
                foreach ($product->variants as $variant) {
                    // Ana depoda stok
                    if ($mainWarehouseLocations->isNotEmpty()) {
                        $stocks[] = [
                            'product_id' => $product->id,
                            'variant_id' => $variant->id,
                            'warehouse_id' => $warehouses['ANA-01']->id,
                            'warehouse_location_id' => $mainWarehouseLocations->random()->id,
                            'stock_batch_id' => null,
                            'quantity' => rand(10, 50),
                            'notes' => 'Başlangıç stoğu - ' . $variant->name,
                            'is_active' => true,
                        ];
                    }

                    // Bazı varyantlar için diğer depolarda da stok oluştur
                    if (rand(0, 2) === 0 && $izmirWarehouseLocations->isNotEmpty()) { // %33 ihtimalle İzmir'de stok
                        $stocks[] = [
                            'product_id' => $product->id,
                            'variant_id' => $variant->id,
                            'warehouse_id' => $warehouses['IZM-01']->id,
                            'warehouse_location_id' => $izmirWarehouseLocations->random()->id,
                            'stock_batch_id' => null,
                            'quantity' => rand(5, 30),
                            'notes' => 'Başlangıç stoğu - ' . $variant->name,
                            'is_active' => true,
                        ];
                    }

                    if (rand(0, 2) === 0 && $ankaraWarehouseLocations->isNotEmpty()) { // %33 ihtimalle Ankara'da stok
                        $stocks[] = [
                            'product_id' => $product->id,
                            'variant_id' => $variant->id,
                            'warehouse_id' => $warehouses['ANK-01']->id,
                            'warehouse_location_id' => $ankaraWarehouseLocations->random()->id,
                            'stock_batch_id' => null,
                            'quantity' => rand(5, 20),
                            'notes' => 'Başlangıç stoğu - ' . $variant->name,
                            'is_active' => true,
                        ];
                    }
                }
            }
        }

        // Stokları kaydet
        foreach ($stocks as $stock) {
            Stock::create($stock);
        }
    }
}
