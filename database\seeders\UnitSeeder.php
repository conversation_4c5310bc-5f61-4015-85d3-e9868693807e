<?php

namespace Database\Seeders;

use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Database\Seeder;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure UnitTypes exist first
        $this->call(UnitTypeSeeder::class);

        // Get unit type IDs
        $weightTypeId = UnitType::where('name', 'Ağırlık')->first()->id;
        $lengthTypeId = UnitType::where('name', 'Uzunluk')->first()->id;
        $volumeTypeId = UnitType::where('name', 'Hacim')->first()->id;
        $areaTypeId = UnitType::where('name', 'Alan')->first()->id;
        $countTypeId = UnitType::where('name', 'Adet')->first()->id;
        $sizeTypeId = UnitType::where('name', 'Beden')->first()->id;
        $shoeTypeId = UnitType::where('name', '<PERSON>yakkabı Numarası')->first()->id;
        $liquidTypeId = UnitType::where('name', 'Sıvı')->first()->id;
        
        // Define units for each type
        $units = [
            // Weight units
            [
                'name' => 'Kilogram',
                'symbol' => 'kg',
                'unit_type_id' => $weightTypeId,
            ],
            [
                'name' => 'Gram',
                'symbol' => 'g',
                'unit_type_id' => $weightTypeId,
            ],
            [
                'name' => 'Ton',
                'symbol' => 't',
                'unit_type_id' => $weightTypeId,
            ],
            [
                'name' => 'Miligram',
                'symbol' => 'mg',
                'unit_type_id' => $weightTypeId,
            ],

            // Length units
            [
                'name' => 'Metre',
                'symbol' => 'm',
                'unit_type_id' => $lengthTypeId,
            ],
            [
                'name' => 'Santimetre',
                'symbol' => 'cm',
                'unit_type_id' => $lengthTypeId,
            ],
            [
                'name' => 'Milimetre',
                'symbol' => 'mm',
                'unit_type_id' => $lengthTypeId,
            ],
            [
                'name' => 'Kilometre',
                'symbol' => 'km',
                'unit_type_id' => $lengthTypeId,
            ],
            [
                'name' => 'İnç',
                'symbol' => 'in',
                'unit_type_id' => $lengthTypeId,
            ],

            // Liquid units (Sıvı birimleri)
            [
                'name' => 'Litre',
                'symbol' => 'lt',
                'unit_type_id' => $liquidTypeId,
            ],
            [
                'name' => 'Mililitre',
                'symbol' => 'ml',
                'unit_type_id' => $liquidTypeId,
            ],
            [
                'name' => 'Santilitre',
                'symbol' => 'cl',
                'unit_type_id' => $liquidTypeId,    
            ],
            [
                'name' => 'Desilitre',
                'symbol' => 'dl',
                'unit_type_id' => $liquidTypeId,
            ],

            // Volume units (Hacim birimleri)
            [
                'name' => 'Metreküp',
                'symbol' => 'm³',
                'unit_type_id' => $volumeTypeId,
            ],
            [
                'name' => 'Santimetreküp',
                'symbol' => 'cm³',
                'unit_type_id' => $volumeTypeId,
            ],
            [
                'name' => 'Desimetreküp',
                'symbol' => 'dm³',
                'unit_type_id' => $volumeTypeId,
            ],

            // Area units
            [
                'name' => 'Metrekare',
                'symbol' => 'm²',
                'unit_type_id' => $areaTypeId,
            ],
            [
                'name' => 'Santimetrekare',
                'symbol' => 'cm²',
                'unit_type_id' => $areaTypeId,
            ],
            [
                'name' => 'Hektar',
                'symbol' => 'ha',
                'unit_type_id' => $areaTypeId,
            ],
            [
                'name' => 'Dönüm',
                'symbol' => 'dönüm',
                'unit_type_id' => $areaTypeId,
            ],

            // Count units
            [
                'name' => 'Adet',
                'symbol' => 'ad',
                'unit_type_id' => $countTypeId,
            ],
            [
                'name' => 'Düzine',
                'symbol' => 'dz',
                'unit_type_id' => $countTypeId,
            ],
            [
                'name' => 'Paket',
                'symbol' => 'pkt',
                'unit_type_id' => $countTypeId, 
            ],
            [
                'name' => 'Koli',
                'symbol' => 'koli',
                'unit_type_id' => $countTypeId,
            ],
            [
                'name' => 'Çift',
                'symbol' => 'çift',
                'unit_type_id' => $countTypeId,
            ],
            [
                'name' => 'Takım',
                'symbol' => 'takım',
                'unit_type_id' => $countTypeId,
            ],

            // Size units
            [
                'name' => 'XX-Small',
                'symbol' => 'XXS',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'X-Small',
                'symbol' => 'XS',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'Small',
                'symbol' => 'S',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'Medium',
                'symbol' => 'M',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'Large',
                'symbol' => 'L',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'X-Large',
                'symbol' => 'XL',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'XX-Large',
                'symbol' => 'XXL',
                'unit_type_id' => $sizeTypeId,
            ],
            [
                'name' => 'XXX-Large',
                'symbol' => 'XXXL',
                'unit_type_id' => $sizeTypeId,  
            ],

            // Shoe size units
            [
                'name' => '35',
                'symbol' => '35',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '36',
                'symbol' => '36',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '37',
                'symbol' => '37',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '38',
                'symbol' => '38',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '39',
                'symbol' => '39',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '40',
                'symbol' => '40',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '41',
                'symbol' => '41',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '42',
                'symbol' => '42',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '43',
                'symbol' => '43',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '44',
                'symbol' => '44',
                'unit_type_id' => $shoeTypeId,
            ],
            [
                'name' => '45',
                'symbol' => '45',
                'unit_type_id' => $shoeTypeId,  
            ],
            [
                'name' => '46',
                'symbol' => '46',
                'unit_type_id' => $shoeTypeId,  
            ],
            [
                'name' => '47',
                'symbol' => '47',
                'unit_type_id' => $shoeTypeId,  
            ],
        ];

        foreach ($units as $unit) {
            Unit::updateOrCreate(
                ['name' => $unit['name'], 'unit_type_id' => $unit['unit_type_id']],
                [
                    'name' => $unit['name'],
                    'symbol' => $unit['symbol'],
                    'unit_type_id' => $unit['unit_type_id'],
                    'is_active' => 1,
                ]
            );
        }
    }
}
