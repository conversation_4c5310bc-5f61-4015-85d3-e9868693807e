<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $items = [[
            'name' => 'Test',
            'surname' => 'Example',
            'tc' => '11111111112',
            'title' => 'Süper Admin',
            'role_id' => 1,
            'phone' => '0(111)111 11 12',
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'password' => Hash::make('123123'),
            'is_active' => 1,
            ],[
            'name' => 'Talha',
            'surname' => '<PERSON><PERSON>',
            'tc' => '11111111111',
            'title' => 'Süper Admin',
            'role_id' => 1,
            'phone' => '0(111)111 11 11',
            'email' => '<EMAIL>',
            'username' => 'mtk',
            'password' => Hash::make('123123'),
            'is_active' => 1,
        ]];

        DB::table('users')->insert($items);
    }
}
