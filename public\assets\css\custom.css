/* DataTable Dark Mode Fixes */

/* Fix for DataTable scrollX background in dark mode */
[data-theme="dark"] .dataTables_scrollBody {
    background-color: var(--dark-2) !important;
}

/* Fix for DataTable header and footer in dark mode */
[data-theme="dark"] .dataTables_scrollHead,
[data-theme="dark"] .dataTables_scrollFoot {
    background-color: var(--dark-2) !important;
}

/* Fix for DataTable scrollbar in dark mode */
[data-theme="dark"] .dataTables_scrollBody::-webkit-scrollbar {
    height: 8px !important;
    background-color: var(--dark-3) !important;
}

[data-theme="dark"] .dataTables_scrollBody::-webkit-scrollbar-thumb {
    background-color: var(--neutral-400) !important;
    border-radius: 4px;
}

[data-theme="dark"] .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background-color: var(--neutral-500) !important;
}

/* Fix for DataTable borders in dark mode */
[data-theme="dark"] .dataTable {
    border-color: var(--neutral-200) !important;
}

/* Fix for DataTable row hover in dark mode */
[data-theme="dark"] .dataTable tbody tr:hover td {
    background-color: var(--neutral-100) !important;
}

/* SweetAlert2 Dark Mode Fixes */
[data-theme="dark"] .swal2-popup {
    background-color: var(--dark-2) !important;
    color: var(--text-primary-light) !important;
}

[data-theme="dark"] .swal2-title,
[data-theme="dark"] .swal2-html-container {
    color: var(--text-primary-light) !important;
}

[data-theme="dark"] .swal2-confirm {
    background-color: var(--primary-600) !important;
}

[data-theme="dark"] .swal2-styled.swal2-confirm:focus {
    box-shadow: 0 0 0 3px var(--primary-400) !important;
}

[data-theme="dark"] .swal2-icon.swal2-warning {
    border-color: var(--warning-main) !important;
    color: var(--warning-main) !important;
}

/* SweetAlert2 Theme Compatible Classes */
.swal2-theme-compatible {
    background-color: var(--white) !important;
}

.swal2-theme-compatible-title,
.swal2-theme-compatible-content {
    color: var(--text-primary-light) !important;
}

.swal2-theme-compatible-button {
    background-color: var(--primary-600) !important;
}

[data-theme="dark"] .swal2-theme-compatible {
    background-color: var(--dark-2) !important;
}

[data-theme="dark"] .swal2-theme-compatible-title,
[data-theme="dark"] .swal2-theme-compatible-content {
    color: var(--text-primary-light) !important;
}

[data-theme="dark"] .swal2-theme-compatible-button {
    background-color: var(--primary-600) !important;
}

