/* ========================== Authentication css start ============================== */
.auth {
    min-height: 100vh;
    &-left {
        background: linear-gradient(90deg, #ECF0FF 0%, #FAFBFF 100%);
        width: 50%;
    }
    &-right {
        width: 50%;
        @media (max-width: 991px) {
            width: 100%;
        }
        > div {
            @media (max-width: 768px) {
                max-width: 100%;
                width: 100%;
            }   
        }
    }

    &.forgot-password-page {
        .auth-left {
            background: var(--gradients-Colors-gradients-2, linear-gradient(90deg, #F7E9FF 0.12%, #FDF8F7 99.89%));
        }
    }
}

.center-border-horizontal {
    position: relative;
    z-index: 1;
    &::before {
        position: absolute;
        content: "";
        width: 100%;
        height: 1px;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
        background-color: var(--neutral-300);
        z-index: -1;
    }
}
/* ========================== Authentication css End ============================== */