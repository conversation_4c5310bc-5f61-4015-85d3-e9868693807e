.btn {
  padding: rem(9px) rem(12px);
  font-size: rem(16px);
  font-weight: 500;
  &.btn-lg {
    padding: rem(15px) rem(14px);
    font-weight: 600;
  }
  &.btn-sm {
    padding: rem(8px) rem(14px);
    font-size: rem(14px);
  }
}

// // Add btn class name and color through sass loop
$btn-colors: (
  "success" --success,
  "danger" --danger,
  "warning" --warning,
  "info" --info,
);

@each $name, $color in $btn-colors {
  .btn-#{$name} {
    background-color: var(#{$color}-main);
    color: #fff;
    border-color: var(#{$color}-main);
    &:hover {
      background-color: var(#{$color}-hover);
      color: #fff;
      border-color: var(#{$color}-main);
    }
    &:active,
    &:focus {
      background-color: var(#{$color}-pressed);
      color: #fff;
      border-color: var(#{$color}-pressed);
    }
  }
}

@each $name, $color in $btn-colors {
  .btn-outline-#{$name} {
    background-color: transparent;
    color: var(#{$color}-main);
    border-color: var(#{$color}-main);
    &:hover {
      background-color: var(#{$color}-hover);
      color: #fff;
      border-color: var(#{$color}-main);
    }
    &:active,
    &:focus {
      background-color: var(#{$color}-pressed) !important;
      color: #fff !important;
      border-color: var(#{$color}-pressed) !important;
    }
  }
}


/* Pill Btn */
@each $color, $shades in $colors {
  @each $shade, $value in $shades {
    .btn-#{$color}-#{$shade} {
      background-color: var(--#{$color}-#{$shade});
      color: #fff;
      border-color: var(--#{$color}-#{$shade});
      &:hover {
        background-color: var(--#{$color}-700);
        color: #fff !important;
        border-color: var(--#{$color}-700);
      }
      &:active,
      &:focus {
        background-color: var(--#{$color}-800) !important;
        color: #fff !important;
        border-color: var(--#{$color}-800) !important;
      }
    }
  }
}

/* Outline Btn */
@each $color, $shades in $colors {
  @each $shade, $value in $shades {
    .btn-outline-#{$color}-#{$shade} {
      background-color: transparent !important;
      color:  var(--#{$color}-#{$shade}) !important;
      border-color: var(--#{$color}-#{$shade}) !important;
      &:hover {
        background-color: var(--#{$color}-#{$shade}) !important;
        color: #fff !important;
        border-color: var(--#{$color}-#{$shade}) !important;
      }
      &:active,
      &:focus {
        background-color: var(--#{$color}-800) !important;
        color: #fff !important;
        border-color: var(--#{$color}-800) !important;
      }
    }
  }
}
