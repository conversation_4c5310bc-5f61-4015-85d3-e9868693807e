/* ========================= Image upload csss start ================================= */
.uploaded-image-name-list {
    &:hover {
        .remove-image {
            visibility: visible;
            opacity: 1;
        }
    }
    .remove-image {
        visibility: hidden;
        opacity: 0;
    }
}

/* Upload Image Css Start */
  .avatar-upload {
    position: relative;
    max-width: max-content;
    &.style-two {
      width: 180px;
      height: 150px;
      max-width: unset;
    }
  }

  #imagePreview {
    background-image: url('../images/user-grid/user-grid-img13.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  #previewImage1, #previewImage2 {
    background-image: url('../images/payment/upload-image.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
  

  .avatar-upload .avatar-preview {
    width: 150px;
    height: 150px;
    position: relative;
    border-radius: 100%;
    border: 1px solid var(--primary-600);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    &.style-two {
      width: 120px;
      height: 120px;
      border-radius: 8px;
      border: 1px solid var(--border-color)!important;
      box-shadow: none;
      > div {
        border-radius: inherit !important;
      }
    }
  }
  .avatar-upload .avatar-preview > div {
    background-image: url('');
    width: 100%;
    height: 100%;
    border-radius: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
  
/* Upload Image Css End */
/* ========================= Image upload csss End ================================= */