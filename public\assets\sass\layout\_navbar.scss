.navbar-header {
  height: rem(72px);
  background-color: var(--white);
  position: sticky;
  top: 0;
  padding: rem(16px) rem(24px);
  z-index: 2;
}

.sidebar-toggle,
.sidebar-mobile-toggle {
  line-height: 1.2;
  color: var(--text-primary-light);
  .icon {
    font-size: rem(22px);
  }
}

.sidebar-toggle {
  display: none;
  @include media(xl) {
    display: inline-block;
  }
  &.active {
    .icon {
      &.non-active {
        display: none;
      }
      &.active {
        display: inline-block;
      }
    }
  }
  .icon {
    &.active {
      display: none;
    }
  }
}

.sidebar-mobile-toggle {
  display: inline-block;
  @include media(xl) {
    display: none;
  }
}

.navbar-search {
  position: relative;
  display: none;
  @include media(lg) {
    display: inline-block;
  }
  input {
    width: rem(388px);
    height: rem(40px);
    background-color: var(--neutral-50);
    border: 1px solid var(--input-form-light);
    @include border-radius(rem(8px));
    padding-block: rem(5px);
    padding-inline-start: rem(42px);
    padding-inline-end: rem(20px);
    color: var(--text-primary-light);
    &:focus {
      border-color: var(--brand);
    }
  }
  .icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    inset-inline-start: rem(15px);
    font-size: rem(18px);
    color: var(--black);
  }
}

[data-theme-toggle] {
  font-size: 0;
  position: relative;
  &::after {
    position: absolute;
    content: "\f1bf";
    font-family: remixicon;
    font-style: normal;
    font-size: rem(20px);
    color: var(--text-primary-light);
  }
  &[aria-label="dark"] {
    &::after {
      content: "\ef6f";
      color: #fff;
    }
  }
}

.positioned-icon {
  inset-inline-start: 16px;
  
}