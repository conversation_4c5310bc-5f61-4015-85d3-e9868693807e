@extends('layout.layout')

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">{{ $container->title }} Fiş</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('backend.account_vouchers_save') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <input type="hidden" name="id" value="{{ $item->id ?? '' }}">

                    <div class="row gy-3">
                        <!-- Fiş No -->
                        <div class="col-md-4">
                            <label class="form-label">Fiş No</label>
                            <input type="text" class="form-control" id="voucher_no" name="voucher_no" value="{{ $item->voucher_no }}" readonly>
                            <small class="text-muted">Format: CHF/AY/YIL/00001 (otomatik oluşturulur)</small>
                        </div>

                        <!-- Fiş Tarihi -->
                        <div class="col-md-4">
                            <label class="form-label">Fiş Tarihi</label>
                            <input type="date" class="form-control" name="voucher_date" value="{{ $item->voucher_date ? date('Y-m-d', strtotime($item->voucher_date)) : date('Y-m-d') }}" required>
                        </div>

                        <!-- Vade Tarihi -->
                        <div class="col-md-4">
                            <label class="form-label">Vade Tarihi</label>
                            <input type="date" class="form-control" name="due_date" value="{{ $item->due_date ? date('Y-m-d', strtotime($item->due_date)) : '' }}">
                        </div>

                        <!-- Cari -->
                        <div class="col-md-6">
                            <label class="form-label">Cari</label>
                            <select class="form-control select2" name="current_id" required>
                                <option value="">Cari Seç</option>
                                @foreach ($currents as $current)
                                    <option value="{{ $current->id }}" {{ (isset($item) && $item->current_id == $current->id) ? 'selected' : '' }}>
                                        {{ $current->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Fiş Türü -->
                        <div class="col-md-6">
                            <label class="form-label">Fiş Türü</label>
                            <select class="form-control" name="voucher_type" required>
                                <option value="">Fiş Türü Seç</option>
                                @foreach ($voucherTypes as $type)
                                    <option value="{{ $type->code }}" {{ (isset($item) && $item->voucher_type == $type->code) ? 'selected' : '' }}>
                                        {{ $type->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Tutar -->
                        <div class="col-md-4">
                            <label class="form-label">Tutar</label>
                            <input type="number" step="0.01" class="form-control" name="amount" value="{{ $item->amount ?? 0 }}" required>
                        </div>

                        <!-- Para Birimi -->
                        <div class="col-md-4">
                            <label class="form-label">Para Birimi</label>
                            <input type="text" class="form-control" name="currency" value="{{ $item->currency ?? 'TRY' }}">
                        </div>

                        <!-- Döviz Kuru -->
                        <div class="col-md-4">
                            <label class="form-label">Döviz Kuru</label>
                            <input type="number" step="0.0001" class="form-control" name="exchange_rate" value="{{ $item->exchange_rate ?? 1.0000 }}">
                        </div>

                        <!-- Referans No -->
                        <div class="col-md-6">
                            <label class="form-label">Referans No (Çek/Senet No vb.)</label>
                            <input type="text" class="form-control" name="reference_no" value="{{ $item->reference_no ?? '' }}">
                        </div>

                        <!-- Açıklama -->
                        <div class="col-md-6">
                            <label class="form-label">Açıklama</label>
                            <input type="text" class="form-control" name="description" value="{{ $item->description ?? '' }}">
                        </div>

                        <!-- Banka Adı -->
                        <div class="col-md-4">
                            <label class="form-label">Banka Adı</label>
                            <input type="text" class="form-control" name="bank_name" value="{{ $item->bank_name ?? '' }}">
                        </div>

                        <!-- Banka Şubesi -->
                        <div class="col-md-4">
                            <label class="form-label">Banka Şubesi</label>
                            <input type="text" class="form-control" name="bank_branch" value="{{ $item->bank_branch ?? '' }}">
                        </div>

                        <!-- Hesap No -->
                        <div class="col-md-4">
                            <label class="form-label">Hesap No</label>
                            <input type="text" class="form-control" name="account_no" value="{{ $item->account_no ?? '' }}">
                        </div>

                        <!-- Belge Dosyası -->
                        <div class="col-md-6">
                            <label class="form-label">Belge Dosyası</label>
                            <input type="file" class="form-control" name="document_file" accept=".pdf,.doc,.docx,.png,.xls,.xlsx">
                            @if(isset($item) && $item->document_file)
                                <small class="text-info">Mevcut dosya: {{ $item->document_file }}</small>
                            @endif
                        </div>

                        <!-- Aktif/Pasif -->
                        <div class="col-md-6">
                            <label class="form-label">Aktif mi?</label>
                            <select class="form-control" name="is_active">
                                <option value="1" {{ (isset($item) && $item->is_active == 1) ? 'selected' : '' }}>Aktif</option>
                                <option value="0" {{ (isset($item) && $item->is_active == 0) ? 'selected' : '' }}>Pasif</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                        <a href="{{ route('backend.account_vouchers_list') }}" class="btn btn-secondary">İptal</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script>
// Initialize form when DOM is loaded
window.addEventListener('DOMContentLoaded', async function() {
    // Fiş numarasını kontrol et
    const voucherNoField = document.getElementById('voucher_no');

    // Eğer fiş numarası boşsa, sunucudan al
    if (!voucherNoField.value) {
        try {
            const response = await fetch('/backend/account/vouchers/next-voucher-number');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.voucher_no) {
                voucherNoField.value = data.voucher_no;
                console.log('Fiş numarası alındı:', data.voucher_no);
            } else {
                console.error('Fiş numarası alınamadı:', data);
            }
        } catch (error) {
            console.error('Fiş numarası alınırken hata:', error);
        }
    }
});
</script>
@endsection
