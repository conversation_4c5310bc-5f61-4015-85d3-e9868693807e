@extends('layout.layout')

@php
    $title = $container->title ?? '<PERSON><PERSON>sap Fişleri';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.account_vouchers_list') }}?export=excel"
                    class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
                    Excel
                </a>
                <a href="{{ route('backend.account_vouchers_form') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    Ekle
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Fiş No</th>
                            <th scope="col" class="text-center">Cari</th>
                            <th scope="col" class="text-center">Fiş Tarihi</th>
                            <th scope="col" class="text-center">Fiş Türü</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.account_vouchers_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    }
                },
                columns: [{
                        data: 'voucher_no',
                        name: 'voucher_no',
                        className: 'text-center'
                    },
                    {
                        data: 'current_name',
                        name: 'current_name',
                        className: 'text-center'
                    },
                    {
                        data: 'voucher_date',
                        name: 'voucher_date',
                        className: 'text-center'
                    },
                    {
                        data: 'voucher_type_name',
                        name: 'voucher_type_name',
                        className: 'text-center'
                    },
                    {
                        data: 'amount_formatted',
                        name: 'amount_formatted',
                        className: 'text-center'
                    },
                    {
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    },
                    {
                        render: function(data, type, row) {
                            return `
                <td class="text-center">
                    <div class="d-flex align-items-center gap-10 justify-content-center">
                        <a href="{{ route('backend.account_vouchers_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                            <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                        </a>
                        <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                            <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                        </button>
                    </div>
                </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    }
                ],
                order: [
                    [2, 'desc']
                ],
                pageLength: 15,
            });

            // Silme işlemi
            $(document).on('click', '.remove-item-btn', function() {
                var id = $(this).attr('row-delete');
                if (confirm('Bu kaydı silmek istediğinizden emin misiniz?')) {
                    $.ajax({
                        url: "{{ route('backend.account_vouchers_delete') }}",
                        type: 'POST',
                        data: {
                            id: id,
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success) {
                                table.ajax.reload();
                                toastr.success(response.message);
                            } else {
                                toastr.error(response.message || response.error);
                            }
                        },
                        error: function(xhr) {
                            toastr.error('Bir hata oluştu: ' + xhr.responseText);
                        }
                    });
                }
            });
        });
    </script>
@endsection
