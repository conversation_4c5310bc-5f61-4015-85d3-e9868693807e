@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">
                    {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                </h5>
            </div>

            <div class="card-body">
                <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}" method="POST">
                    @csrf
                    <div class="row gy-3">
                        <div class="col-md-6">
                            <div class="form-group mb-2">
                                <label class="form-label"><PERSON><PERSON></label>
                                <select class="form-control select" name="current_id" required>
                                    <option value=""><PERSON><PERSON></option>
                                    @foreach ($currents as $cr)
                                        <option value="{{ $cr->id }}"
                                            {{ old('current_id', $item->current_id) == $cr->id ? 'selected' : '' }}>
                                            {{ $cr->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('current_id')
                                    <span class="badge badge-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Borç Bakiyesi Tanımla</label>
                            <div class="icon-field">
                                <span class="icon">
                                    <iconify-icon icon="ph:currency-circle-dollar-fill"></iconify-icon>
                                </span>
                                <input type="text" class="form-control" id="debit_balance" name="debit_balance"
                                       value="{{ old('debit_balance', $item->debit_balance ?? '') }}"
                                       placeholder="00.00">
                                <x-form-error field="debit_balance" />
                            </div>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Alacak Bakiyesi Tanımla</label>
                            <div class="icon-field">
                                <span class="icon">
                                    <iconify-icon icon="ph:currency-circle-dollar-fill"></iconify-icon>
                                </span>
                                <input type="text" class="form-control" id="credit_balance" name="credit_balance"
                                       value="{{ old('credit_balance', $item->credit_balance ?? '') }}"
                                       placeholder="00.00">
                                <x-form-error field="credit_balance" />
                            </div>
                        </div>
                        <div class="col-6">
                            <label class="form-label">Durum</label>
                            <div class="icon-field">
                                <span class="icon">
                                    <iconify-icon icon="carbon:badge"></iconify-icon>
                                </span>
                                <select class="form-control form-select" name="is_active">
                                    <option value="1" {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif</option>
                                    <option value="0" {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif</option>
                                </select>
                                <x-form-error field="is_active" />
                            </div>
                        </div>

                        {{-- Kaydet Butonu --}}
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary-600">Kaydet</button>
                        </div>
                    </div><!-- row -->
                </form>
            </div><!-- card-body -->
        </div><!-- card -->
    </div><!-- col-md-12 -->
</div><!-- row -->
@endsection
