@extends('layout.layout')

@php
    $title = $container->title ?? 'Marka';
    $subTitle = $container->title . ' Detayı';
@endphp

@section('content')
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $item->name }} - Detay Bilgileri</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>Marka Adı:</strong> {{ $item->name }}</p>
                    <p class="mb-1"><strong>Açıklama:</strong> {{ $item->description ?? '-' }}</p>

                    <p class="mb-1"><strong>Kategoriler:</strong></p>
                    @if($item->categories->count() > 0)
                        <div class="ms-3">
                            @foreach($item->categories as $category)
                                <span class="badge bg-primary-100 text-primary-600 border border-primary-main px-12 py-4 radius-8 fw-medium text-sm me-2 mb-2 d-inline-block">
                                    {{ $category->name }}
                                </span>
                            @endforeach
                        </div>
                    @else
                        <span class="text-muted ms-3">-</span>
                    @endif
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>Ürün Sayısı:</strong> {{ $item->products()->count() }}</p>
                    <p class="mb-1"><strong>Durum:</strong> 
                        @if($item->is_active)
                            <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>
                        @else
                            <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>
                        @endif
                    </p>
                    <p class="mb-1"><strong>Oluşturma Tarihi:</strong> {{ $item->created_at ? $item->created_at->format('d.m.Y H:i') : '-' }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Bu Markaya Ait Ürünler</h5>
        </div>
        <div class="card-body">
            <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length='10'>
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Ürün Adı</th>
                        <th scope="col">SKU</th>
                        <th scope="col">Kategori</th>
                        <th scope="col">Birim</th>
                        <th scope="col">Satış Fiyatı</th>
                        <th scope="col" class="text-center">Durum</th>
                        <th scope="col">Oluşturma Tarihi</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_detail', $unique) }}?datatable=true",
                    type: 'POST',
                    data: function(d) {
                        d.datatable = true;
                        return d;
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                        width: '50px'
                    },
                    {
                        data: 'name',
                        name: 'name',
                        className: 'text-start',
                        width: '250px'
                    },
                    {
                        data: 'sku',
                        name: 'sku',
                        className: 'text-start',
                        width: '150px'
                    },
                    {
                        data: 'category',
                        name: 'category',
                        className: 'text-start',
                        width: '150px'
                    },
                    {
                        data: 'unit',
                        name: 'unit',
                        className: 'text-center',
                        width: '100px'
                    },
                    {
                        data: 'sale_price',
                        name: 'sale_price',
                        className: 'text-end',
                        width: '150px'
                    },
                    {
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center act-col',
                        width: '100px',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            if (row.is_active == 'Aktif') {
                                return '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>';
                            } else {
                                return '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>';
                            }
                        }
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        className: 'text-start',
                        width: '150px'
                    }
                ],
                order: [
                    [1, 'asc']
                ],

                pageLength: 10,
                autoWidth: false,
                scrollX: true,
                fixedColumns: true
            });
        });
    </script>
@endsection
