@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON>zenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            {{-- Marka Adı --}}
                            <div class="col-md-6">
                                <label class="form-label"><PERSON><PERSON></label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:product"></iconify-icon>
                                    </span>
                                    <input type="text" 
                                        class="form-control" 
                                        name="name"
                                        placeholder="Lütfen marka adını giriniz"
                                        value="{{ old('name', $item->name ?? '') }}">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            
                            {{-- Açıklama --}}
                            <div class="col-md-6">
                                <label class="form-label">Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:text-annotation-toggle"></iconify-icon>
                                    </span>
                                    <input type="text" 
                                        class="form-control" 
                                        name="description"
                                        placeholder="Marka açıklamasını giriniz"
                                        value="{{ old('description', $item->description ?? '') }}">
                                    <x-form-error field="description" />
                                </div>
                            </div>
                            
                            {{-- Durum --}}
                            <div class="col-md-6">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1" {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>
                                            Aktif
                                        </option>
                                        <option value="0" {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>
                                            Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            
                            {{-- Kategoriler --}}
                            <div class="col-12 mb-3">
                                <label class="form-label">Kategoriler</label>
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <button type="button" style="float: right"
                                            class="btn btn-primary float-right select-all-categories-btn waves-effect waves-themed">
                                            Tümünü Seç
                                        </button>
                                    </div>
                                    <x-form-error field="categories" />
                                    
                                    @foreach ($categoryGroups as $categoryName => $categories)
                                        <div class="col-md-4 mb-3">
                                            <div class="p-3">
                                                <strong class="d-block mb-2">{{ $categoryName }}</strong>
                                                <div>
                                                    @foreach ($categories as $category)
                                                        <div class="form-check">
                                                            <input type="checkbox" 
                                                                class="form-check-input category-checkbox"
                                                                name="categories[]" 
                                                                value="{{ $category['id'] }}" 
                                                                id="category_{{ $category['id'] }}"
                                                                data-depth="{{ $category['depth'] }}"
                                                                data-parent="{{ $category['parent_id'] ?? '' }}"
                                                                @if((old('categories') && in_array($category['id'], old('categories'))) || 
                                                                   (!old('categories') && isset($item->categories) && is_array($item->categories) && in_array($category['id'], $item->categories))) checked @endif>
                                                            <label class="form-check-label" for="category_{{ $category['id'] }}">
                                                                @if($category['depth'] > 0)
                                                                    <span class="text-primary">{{ $category['prefix'] }}</span>
                                                                @endif
                                                                {{ $category['name'] }}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            {{-- Form Butonları --}}
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-600">
                                    <i class="fas fa-save me-2"></i>Kaydet
                                </button>
                                <a href="{{ route('backend.' . $container->page . '_list') }}" 
                                    class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-2"></i>İptal
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('style')
    <style>
        /* Role blade benzeri minimal stil */
        .form-check {
            padding: 4px 0;
        }
        
        /* Prefix simgeleri (tire) */
        .text-primary {
            font-weight: 500;
        }
        
        /* Seçili kategoriler için */
        .form-check-input:checked ~ .form-check-label {
            color: #198754;
            font-weight: 500;
        }
        
        /* Ana kategori başlıkları */
        strong.d-block {
            color: #2c3e50;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
@endsection

@section('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllBtn = document.querySelector('.select-all-categories-btn');
            const checkboxes = document.querySelectorAll('.category-checkbox');
            let allSelected = false;
            
            // Tümünü seç/temizle butonu
            selectAllBtn.addEventListener('click', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = !allSelected;
                });
                
                allSelected = !allSelected;
                selectAllBtn.textContent = allSelected ? 'Tümünü Temizle' : 'Tümünü Seç';
            });
            
            // Checkbox değişiklik eventi
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const categoryId = parseInt(this.value);
                    const isChecked = this.checked;
                    
                    if (isChecked) {
                        // Seçildiğinde: tüm alt kategorileri seç ve tüm üst kategorileri seç
                        selectDescendants(categoryId, true);
                        selectAncestors(categoryId);
                    } else {
                        // Bırakıldığında: tüm alt kategorileri bırak
                        selectDescendants(categoryId, false);
                    }
                });
            });
            
            // Alt kategorileri seç/bırak
            function selectDescendants(parentId, checked) {
                const childCheckboxes = document.querySelectorAll(`.category-checkbox[data-parent="${parentId}"]`);
                childCheckboxes.forEach(childCheckbox => {
                    childCheckbox.checked = checked;
                    // Recursive olarak alt kategorileri de kontrol et
                    selectDescendants(parseInt(childCheckbox.value), checked);
                });
            }
            
            // Üst kategorileri seç
            function selectAncestors(categoryId) {
                const checkbox = document.querySelector(`#category_${categoryId}`);
                if (checkbox) {
                    const parentId = checkbox.getAttribute('data-parent');
                    if (parentId) {
                        const parentCheckbox = document.querySelector(`#category_${parentId}`);
                        if (parentCheckbox) {
                            parentCheckbox.checked = true;
                            // Recursive olarak üst kategorileri de seç
                            selectAncestors(parseInt(parentId));
                        }
                    }
                }
            }
        });
    </script>
@endsection