@extends('layout.layout')

@php
    $title = $container->title ?? 'Kategori';
    $subTitle = $container->title . ' Detayı';
@endphp

@section('content')
    <!-- Kategori Bilgileri -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $item->name }} - Detay Bilgileri</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>Kategori Adı:</strong> {{ $item->name }}</p>
                    <p class="mb-1"><strong>Üst Kategori:</strong> {{ $item->parent ? $item->parent->name : 'Ana Kategori' }}</p>
                    <p class="mb-1"><strong>Açıklama:</strong> {{ $item->description ?? '-' }}</p>
                </div>
                <div class="col-md-6">
                    <p class="mb-1"><strong>Alt Kategori <PERSON>ısı:</strong> {{ $item->children()->count() }}</p>
                    <p class="mb-1"><strong>Ürün Sayısı:</strong> {{ $item->products()->count() }}</p>
                    <p class="mb-1"><strong>Durum:</strong> 
                        @if($item->is_active)
                            <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>
                        @else
                            <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>
                        @endif
                    </p>
                    <p class="mb-1"><strong>Oluşturma Tarihi:</strong> {{ $item->created_at ? $item->created_at->format('d.m.Y H:i') : '-' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Alt Kategoriler -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="card-title mb-0">Alt Kategoriler</h5>
        </div>
        <div class="card-body">
            <table class="table bordered-table mb-0" id="subcategoriesTable">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Kategori Adı</th>
                        <th scope="col">Alt Kategori Sayısı</th>
                        <th scope="col">Ürün Sayısı</th>
                        <th scope="col" class="text-center">Durum</th>
                        <th scope="col">Oluşturma Tarihi</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Bu Kategorideki Ürünler -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Bu Kategorideki Ürünler</h5>
        </div>
        <div class="card-body">
            <table class="table bordered-table mb-0" id="productsTable">
                <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Ürün Adı</th>
                        <th scope="col">SKU</th>
                        <th scope="col">Marka</th>
                        <th scope="col">Birim</th>
                        <th scope="col">Satış Fiyatı</th>
                        <th scope="col" class="text-center">Durum</th>
                        <th scope="col">Oluşturma Tarihi</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function(e) {
            // Alt Kategoriler Tablosu
            var subcategoriesTable = $('#subcategoriesTable').DataTable({
                ajax: {
                    url: '{{ route("backend." . $container->page . "_detail", $item->id) }}?datatable=subcategories',
                    type: 'POST',
                    data: function(d) {
                        d.datatable = 'subcategories';
                        return d;
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                        width: '50px'
                    },
                    {
                        data: 'name',
                        name: 'name',
                        className: 'text-start',
                        width: '250px'
                    },
                    {
                        data: 'children_count',
                        name: 'children_count',
                        className: 'text-center',
                        width: '150px'
                    },
                    {
                        data: 'products_count',
                        name: 'products_count',
                        className: 'text-center',
                        width: '100px'
                    },
                    {
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center act-col',
                        width: '100px',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            if (row.is_active == 'Aktif') {
                                return '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>';
                            } else {
                                return '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>';
                            }
                        }
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        className: 'text-start',
                        width: '150px'
                    }
                ],
                order: [
                    [1, 'asc']
                ],
                language: {
                    url: "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
                },
                pageLength: 10,
                autoWidth: false,
                scrollX: true,
                fixedColumns: true
            });

            // Ürünler Tablosu
            var productsTable = $('#productsTable').DataTable({
                ajax: {
                    url: '{{ route("backend." . $container->page . "_detail", $item->id) }}?datatable=products',
                    type: 'POST',
                    data: function(d) {
                        d.datatable = 'products';
                        return d;
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                columns: [{
                        data: 'DT_RowIndex',
                        name: 'DT_RowIndex',
                        orderable: false,
                        searchable: false,
                        className: 'text-center',
                        width: '50px'
                    },
                    {
                        data: 'name',
                        name: 'name',
                        className: 'text-start',
                        width: '250px'
                    },
                    {
                        data: 'sku',
                        name: 'sku',
                        className: 'text-start',
                        width: '150px'
                    },
                    {
                        data: 'brand',
                        name: 'brand',
                        className: 'text-start',
                        width: '150px'
                    },
                    {
                        data: 'unit',
                        name: 'unit',
                        className: 'text-center',
                        width: '100px'
                    },
                    {
                        data: 'sale_price',
                        name: 'sale_price',
                        className: 'text-end',
                        width: '150px'
                    },
                    {
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center act-col',
                        width: '100px',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            if (row.is_active == 'Aktif') {
                                return '<span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Aktif</span>';
                            } else {
                                return '<span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Pasif</span>';
                            }
                        }
                    },
                    {
                        data: 'created_at',
                        name: 'created_at',
                        className: 'text-start',
                        width: '150px'
                    }
                ],
                order: [
                    [1, 'asc']
                ],
                language: {
                    url: "//cdn.datatables.net/plug-ins/1.13.7/i18n/tr.json"
                },
                pageLength: 10,
                autoWidth: false,
                scrollX: true,
                fixedColumns: true
            });
        });
    </script>
@endsection
