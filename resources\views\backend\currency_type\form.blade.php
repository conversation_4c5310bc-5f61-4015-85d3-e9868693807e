@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON>le');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-md-6">
                                <label class="form-label">Adı</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mdi:tag"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen para birimi adını giriniz"
                                        value="{{ old('name') ?? ($item->name ?? '') }}" name="name">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Sembol</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mdi:tag"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen sembolünü giriniz"
                                        value="{{ old('symbol') ?? ($item->symbol ?? '') }}" name="symbol">
                                    <x-form-error field="symbol" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Kod</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mdi:tag"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen kodunu giriniz"
                                        value="{{ old('code') ?? ($item->code ?? '') }}" name="code">
                                    <x-form-error field="code" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            <div class="mt-4">
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                                <a href="{{ route('backend.' . $container->page . '_list') }}" class="btn btn-secondary">İptal</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
