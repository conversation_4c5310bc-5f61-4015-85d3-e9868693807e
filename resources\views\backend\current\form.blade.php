@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON>üzenle');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>


                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            {{-- Ad --}}
                            <div class="col-6">
                                <label class="form-label">Ünvan</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen şirket ünvanınızı giriniz"
                                        name="name" value="{{ old('name', $item->name ?? '') }}">
                                    <x-form-error field="name" />
                                </div>
                            </div>

                            {{-- Soyad --}}
                            <div class="col-6">
                                <label class="form-label">Kısa Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen kısa açıklama giriniz"
                                        name="surname" value="{{ old('surname', $item->surname ?? '') }}">
                                    <x-form-error field="surname" />
                                </div>
                            </div>

                            {{-- Email --}}
                            <div class="col-6">
                                <label class="form-label">Email</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mage:email"></iconify-icon>
                                    </span>
                                    <input type="email" class="form-control" placeholder="Lütfen e-posta adresi giriniz"
                                        name="email" value="{{ old('email', $item->email ?? '') }}">
                                    <x-form-error field="email" />
                                </div>
                            </div>

                            {{-- Phone --}}
                            <div class="col-6">
                                <label class="form-label">Telefon No</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:phone-calling-linear"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" id="phone" name="phone"
                                        value="{{ old('phone', $item->phone ?? '') }}"
                                        placeholder="Lütfen telefon numarası giriniz">
                                    <x-form-error field="phone" />
                                </div>
                            </div>

                            {{-- Kurum Tipi --}}
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label class="form-label">Kurum Tipi</label>
                                    <select class="form-control select" name="instutation_id" required>
                                        <option value="">Kurum Tipi Seçin</option>
                                        @foreach ($insutations as $insutation)
                                            <option value="{{ $insutation->id }}"
                                                {{ old('instutation_id', $item->instutation_id) == $insutation->id ? 'selected' : '' }}>
                                                {{ $insutation->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('instutation_id')
                                        <span class="badge badge-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            {{-- Cari Tipi --}}
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label class="form-label">Cari Tipi</label>
                                    <select class="form-control select" name="current_type_id" required>
                                        <option value="">Cari Tipi Seçin</option>
                                        @foreach ($currenttypes as $currenttype)
                                            <option value="{{ $currenttype->id }}"
                                                {{ old('current_type_id', $item->currenttype_id) == $currenttype->id ? 'selected' : '' }}>
                                                {{ $currenttype->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('current_type_id')
                                        <span class="badge badge-danger">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            {{-- Vergi No --}}
                            <div class="col-6">
                                <label class="form-label">Vergi No</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen Vergi Numaranızı giriniz"
                                        name="tax_number" value="{{ old('tax_number', $item->tax_number ?? '') }}">
                                    <x-form-error field="tax_number" />
                                </div>
                            </div>

                            {{-- Durum --}}
                            <div class="col-6">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>

                            {{-- Ülke Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Ülke</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="ic:round-flag"></iconify-icon>
                                    </span>
                                    <select id="country" name="country_id" class="form-control select">
                                        <option value="">Ülke Seçiniz</option>
                                        @foreach ($countrys as $country)
                                            <option value="{{ $country->id }}"
                                                {{ old('country_id') == $country->id || $item->country_id == $country->id || $country->id == 136 ? 'selected' : '' }}>
                                                {{ $country->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="country_id" />
                                </div>
                            </div>

                            {{-- Şehir Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Şehir</label>
                                <select id="city" name="city_id" class="form-control select">
                                    <option value="">Şehir Seçiniz</option>
                                    {{-- Mevcut şehirler (veya AJAX ile doldurulacak) --}}
                                    @foreach ($cities as $oneCity)
                                        <option value="{{ $oneCity->id }}"
                                            {{ old('city_id', $item->city_id ?? '') == $oneCity->id ? 'selected' : '' }}>
                                            {{ $oneCity->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            {{-- İlçe Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">İlçe</label>
                                <select id="district" name="district_id" class="form-control select">
                                    <option value="">İlçe Seçiniz</option>
                                    @foreach ($districts as $dist)
                                        <option value="{{ $dist->id }}"
                                            {{ old('district_id', $item->district_id ?? '') == $dist->id ? 'selected' : '' }}>
                                            {{ $dist->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            {{-- Mahalle Seçimi
                            <div class="col-md-6">
                                <label class="form-label">Mahalle</label>
                                <select id="neighborhood" name="neighborhood_id" class="form-control select">
                                    <option value="">Mahalle Seçiniz</option>
                                    @foreach ($neighborhoods as $nhood)
                                        <option value="{{ $nhood->id }}"
                                            {{ old('neighborhood_id', $item->neighborhood_id ?? '') == $nhood->id ? 'selected' : '' }}>
                                            {{ $nhood->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div> --}}

                            {{-- Vergi Dairesi Seçimi --}}
                            <div class="col-md-6">
                                <label class="form-label">Vergi Dairesi</label>
                                <select id="tax_offices" name="tax_offices_id" class="form-control select">
                                    <option value="">Vergi Dairesi Seçiniz</option>
                                    @foreach ($taxoffices as $taxoffice)
                                        <option value="{{ $taxoffice->id }}"
                                            {{ old('tax_offices_id', $item->tax_offices_id ?? '') == $taxoffice->id ? 'selected' : '' }}>
                                            {{ $taxoffice->name . ' - ' . $taxoffice->code . ' - ' . ($taxoffice->city->name ??'') }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            {{-- EK FİNANS BİLGİLERİ (Tam Genişlik + Sağ Üstte + İkon) --}}

                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center border p-3">
                                    <h6 class="mb-0">EK FİNANS BİLGİLERİ</h6>
                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                        data-bs-toggle="collapse" data-bs-target="#ekFinansBilgileri"
                                        aria-expanded="false" aria-controls="ekFinansBilgileri">
                                        <iconify-icon icon="mdi:plus"></iconify-icon>
                                    </button>
                                </div>
                                <div class="collapse mt-3" id="ekFinansBilgileri">
                                    <div class="row gy-3 px-2">
                                        {{-- Risk Limiti --}}
                                        <div class="col-6">
                                            <label class="form-label">Risk Limiti</label>
                                            <div class="icon-field">
                                                <span class="icon">
                                                    <iconify-icon icon="mdi:currency-try"></iconify-icon>
                                                </span>
                                                <input type="text" class="form-control" id="chance_limits"
                                                    name="chance_limits"
                                                    value="{{ old('chance_limits', $item->chance_limits ?? '') }}"
                                                    placeholder="00.00">
                                                <x-form-error field="chance_limits" />
                                            </div>
                                        </div>
                                        {{-- Çek Limiti --}}
                                        <div class="col-6">
                                            <label class="form-label">Çek Limiti</label>
                                            <div class="icon-field">
                                                <span class="icon">
                                                    <iconify-icon icon="mdi:currency-try"></iconify-icon>
                                                </span>
                                                <input type="text" class="form-control" id="bonds_limits"
                                                    name="bonds_limits"
                                                    value="{{ old('bonds_limits', $item->bonds_limits ?? '') }}"
                                                    placeholder="00.00">
                                                <x-form-error field="bonds_limits" />
                                            </div>
                                        </div>

                                        {{-- Sipariş Limiti --}}
                                        <div class="col-6">
                                            <label class="form-label">Sipariş Limiti</label>
                                            <div class="icon-field">
                                                <span class="icon">
                                                    <iconify-icon icon="mdi:currency-try"></iconify-icon>
                                                </span>
                                                <input type="text" class="form-control" id="order_limits"
                                                    name="order_limits"
                                                    value="{{ old('order_limits', $item->order_limits ?? '') }}"
                                                    placeholder="00.00">
                                                <x-form-error field="order_limits" />
                                            </div>
                                        </div>

                                        {{-- Borç Limiti --}}
                                        <div class="col-6">
                                            <label class="form-label">Borç Limiti</label>
                                            <div class="icon-field">
                                                <span class="icon">
                                                    <iconify-icon icon="mdi:currency-try"></iconify-icon>
                                                </span>
                                                <input type="text" class="form-control" id="balance_limits"
                                                    name="balance_limits"
                                                    value="{{ old('balance_limits', $item->balance_limits ?? '') }}"
                                                    placeholder="00.00">
                                                <x-form-error field="balance_limits" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Kaydet Butonu --}}
                            <div class="col-12 mt-3">
                                <button type="submit" class="btn btn-primary-600">Kaydet</button>
                            </div>
                        </div><!-- row -->
                    </form>
                </div><!-- card-body -->
            </div><!-- card -->
        </div><!-- col-md-12 -->
    </div><!-- row -->
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {

            // —————————————————————————————————————
            // 1) Sayfa açılınca varsa eski country seçili olsun,
            //    city/district de güncellensin:
            // —————————————————————————————————————
            let oldCountryId = '{{ old('country_id', $item->country_id ?? '') }}';
            if (oldCountryId) {
                // Tetikleyerek şehir listesini doldurabiliriz
                $('#country').val(oldCountryId).trigger('change');
            }

            // —————————————————————————————————————
            // 2) ÜLKE DEĞİŞTİĞİNDE ŞEHİRLERİ ÇEK
            // —————————————————————————————————————
            $(document).on('change', '#country', function() {
                let country_id = $(this).val();

                // Şehir, ilçe, mahalle temizle
                $('#city').html('<option value="">Şehir Seçiniz</option>');
                $('#district').html('<option value="">İlçe Seçiniz</option>');
                $('#neighborhood').html('<option value="">Mahalle Seçiniz</option>');

                if (country_id) {
                    $.ajax({
                        url: "/backend/get-city/" + country_id,
                        type: "GET",
                        success: function(response) {
                            // Şehirleri ekle
                            $.each(response.cities, function(index, city) {
                                $('#city').append('<option value="' + city.id + '">' +
                                    city.name + '</option>');
                            });

                            // select yenile
                            $('#city').select('destroy').select({
                                placeholder: "Şehir Seçiniz"
                            });

                            // Ülke seçilince eğer eski city_id varsa onu set edelim
                            let oldCityId = '{{ old('city_id', $item->city_id ?? '') }}';
                            if (oldCityId) {
                                $('#city').val(oldCityId).trigger('change');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Hatası (City):", error);
                        }
                    });
                }
            });

            // —————————————————————————————————————
            // 3) ŞEHİR DEĞİŞTİĞİNDE İLÇELERİ ÇEK
            // —————————————————————————————————————
            $(document).on('change', '#city', function() {
                let city_id = $(this).val();

                // İlçe, mahalle temizle
                $('#district').html('<option value="">İlçe Seçiniz</option>');
                $('#neighborhood').html('<option value="">Mahalle Seçiniz</option>');

                if (city_id) {
                    $.ajax({
                        url: "/backend/get-districts/" + city_id,
                        type: "GET",
                        success: function(response) {
                            // İlçeleri ekle
                            $.each(response.districts, function(index, dist) {
                                $('#district').append('<option value="' + dist.id +
                                    '">' + dist.name + '</option>');
                            });

                            // select yenile
                            $('#district').select('destroy').select({
                                placeholder: "İlçe Seçiniz"
                            });

                            // Şehir seçilince eğer eski district_id varsa onu set edelim
                            let oldDistrictId =
                                '{{ old('district_id', $item->district_id ?? '') }}';
                            if (oldDistrictId) {
                                $('#district').val(oldDistrictId).trigger('change');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Hatası (District):", error);
                        }
                    });
                }
            });

            // —————————————————————————————————————
            // 4) İLÇE DEĞİŞTİĞİNDE MAHALLELERİ ÇEK
            // —————————————————————————————————————
            $(document).on('change', '#district', function() {
                let district_id = $(this).val();

                // Mahalle temizle
                $('#neighborhood').html('<option value="">Mahalle Seçiniz</option>');

                if (district_id) {
                    $.ajax({
                        url: "/backend/get-neighborhood/" + district_id,
                        type: "GET",
                        success: function(response) {
                            // Mahalleleri ekle
                            $.each(response.neighborhoods, function(index, nhood) {
                                $('#neighborhood').append('<option value="' + nhood.id +
                                    '">' + nhood.name + '</option>');
                            });

                            // select yenile
                            $('#neighborhood').select('destroy').select({
                                placeholder: "Mahalle Seçiniz"
                            });

                            // İlçe seçilince eğer eski neighborhood_id varsa set edelim
                            let oldNeighborhoodId =
                                '{{ old('neighborhood_id', $item->neighborhood_id ?? '') }}';
                            if (oldNeighborhoodId) {
                                $('#neighborhood').val(oldNeighborhoodId).trigger('change');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Hatası (Neighborhood):", error);
                        }
                    });
                }
            });

        });
    </script>
@endpush
