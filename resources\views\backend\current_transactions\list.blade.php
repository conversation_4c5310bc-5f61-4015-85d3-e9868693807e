@extends('layout.layout')

@php
    $title = $container->title ?? 'Cari Hareketleri';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.current_transactions_list') }}?export=excel"
                class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
                Excel
            </a>
        </div>
        <div class="card-body">
            <!-- Filtreler -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <label class="form-label">Cari</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="filter-current-name" placeholder="<PERSON><PERSON> se<PERSON>"
                            readonly>
                        <input type="hidden" id="filter-current" value="">
                        <button class="btn btn-outline-secondary" type="button" id="btn-select-current">
                            <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Başlangıç Tarihi</label>
                    <input type="date" class="form-control" id="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Bitiş Tarihi</label>
                    <input type="date" class="form-control" id="filter-end-date">
                </div>
                <div class="col-md-4">
                    <label class="form-label">İşlem Tipi</label>
                    <select class="form-control" id="filter-transaction-type">
                        <option value="">Tüm İşlemler</option>
                        <option value="sales_invoice">Satış Faturaları</option>
                        <option value="purchase_invoice">Alış Faturaları</option>
                        <option value="account_voucher_bank">Banka Fişleri</option>
                        <option value="account_voucher_check">Çek</option>
                        <option value="account_voucher_promissory_note">Senet</option>
                        <option value="account_voucher_wire_transfer">Havale</option>
                        <option value="account_voucher_eft">EFT</option>
                        <option value="account_voucher_other">Diğer</option>
                    </select>
                </div>
            </div>
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="25">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">İşlem No</th>
                            <th scope="col" class="text-center">Cari</th>
                            <th scope="col" class="text-center">Tarih</th>
                            <th scope="col" class="text-center">İşlem Tipi</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">Detay</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>


    <!-- Cari Seçim Modalı -->
    <div class="modal fade" id="currentSelectModal" tabindex="-1" aria-labelledby="currentSelectModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="currentSelectModalLabel">Cari Seçimi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="current-search" placeholder="Cari ara...">
                    </div>
                    <div class="table-responsive">
                        <table class="table bordered-table mb-0" id="current-table">
                            <thead>
                                <tr>
                                    <th>Cari Adı</th>
                                    <th>İşlem</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Tüm Cariler</strong></td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-sm btn-primary select-current" data-id=""
                                            data-name="Tüm Cariler">Seç</button>
                                    </td>
                                </tr>
                                @foreach ($currents as $current)
                                    <tr>
                                        <td>{{ $current->name }}</td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-primary select-current"
                                                data-id="{{ $current->id }}" data-name="{{ $current->name }}">Seç</button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        // Sayıları formatlamak için yardımcı fonksiyon
        function number_format(number, decimals, dec_point, thousands_sep) {
            // Parametreleri kontrol et ve varsayılan değerleri ata
            number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
            var n = !isFinite(+number) ? 0 : +number,
                prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
                sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
                dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
                s = '',
                toFixedFix = function(n, prec) {
                    var k = Math.pow(10, prec);
                    return '' + Math.round(n * k) / k;
                };

            // Fix for IE parseFloat(0.55).toFixed(0) = 0;
            s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');
            if (s[0].length > 3) {
                s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
            }
            if ((s[1] || '').length < prec) {
                s[1] = s[1] || '';
                s[1] += new Array(prec - s[1].length + 1).join('0');
            }
            return s.join(dec);
        }

        // Detay modalını oluşturan fonksiyon
        function generateDetailModal(row, modalId) {
            let title = '';
            let content = '';

            if (row.transaction_type === 'sales_invoice') {
                title = 'Satış Faturası Detayı';
            } else if (row.transaction_type === 'purchase_invoice') {
                title = 'Alış Faturası Detayı';
            } else if (row.transaction_type && row.transaction_type.startsWith('account_voucher_')) {
                title = 'Cari Hesap Fişi Detayı';
            } else {
                title = 'İşlem Detayı';
            }

            // Güvenli bir şekilde değerleri kontrol et ve varsayılan değerler ata
            const invoiceNo = row.invoice_no || '-';
            const invoiceDate = row.invoice_date || '-';
            const currentName = row.current_name || '-';
            const transactionTypeName = row.transaction_type_name || '-';
            const totalAmountFormatted = row.total_amount_formatted || '-';

            // Tamamen basit bir tablo yapısı kullan
            content = '<table class="table table-bordered">' +
                '<tr><td width="30%" class="fw-bold">Belge No</td><td>' + invoiceNo + '</td></tr>' +
                '<tr><td class="fw-bold">Belge Tarihi</td><td>' + invoiceDate + '</td></tr>' +
                '<tr><td class="fw-bold">Cari Hesap</td><td>' + currentName + '</td></tr>' +
                '<tr><td class="fw-bold">İşlem Tipi</td><td>' + transactionTypeName + '</td></tr>' +
                '<tr><td class="fw-bold">Tutar</td><td>' + totalAmountFormatted + '</td></tr>' +
                '</table>';

            // Basitleştirilmiş modal yapısı
            return '<div class="modal fade" id="' + modalId + '" tabindex="-1" aria-hidden="true">' +
                '<div class="modal-dialog modal-lg">' +
                '<div class="modal-content">' +
                '<div class="modal-header">' +
                '<h5 class="modal-title">' + title + '</h5>' +
                '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                '</div>' +
                '<div class="modal-body">' + content + '</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>' +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
        }

        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";

            // DataTable'ı oluştur
            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.current_transactions_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        d.current_id = $('#filter-current').val();
                        d.start_date = $('#filter-start-date').val();
                        d.end_date = $('#filter-end-date').val();
                        d.transaction_type = $('#filter-transaction-type').val();
                        return d;
                    }
                },
                columns: [{
                        data: 'invoice_no',
                        name: 'invoice_no',
                        className: 'text-center'
                    },
                    {
                        data: 'current_name',
                        name: 'current_name',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_date',
                        name: 'invoice_date',
                        className: 'text-center'
                    },
                    {
                        data: 'transaction_type_name',
                        name: 'transaction_type_name',
                        className: 'text-center'
                    },
                    {
                        data: 'total_amount_formatted',
                        name: 'total_amount_formatted',
                        className: 'text-center',
                        render: function(data, type, row) {
                            // Veri tanımsız ise boş string döndür
                            if (!data) {
                                // Eğer total_amount varsa, formatla
                                if (row.total_amount) {
                                    const amount = number_format(row.total_amount, 2, ',', '.');
                                    const isNegative = row.transaction_type ===
                                        'purchase_invoice' ||
                                        (row.transaction_type && row.transaction_type.startsWith(
                                            'account_voucher_'));

                                    const formattedAmount = (isNegative ? '-' : '+') + amount +
                                    ' ₺';
                                    row.total_amount_formatted = formattedAmount;

                                    const cssClass = isNegative ? 'text-danger-light bg-danger-50' :
                                        'text-success-light bg-success-50';
                                    return '<span class="px-2 py-1 rounded ' + cssClass + '">' +
                                        formattedAmount + '</span>';
                                }
                                return '';
                            }

                            // Tutarın ilk karakteri + veya - olabilir
                            const isNegative = data.startsWith('-');
                            const cssClass = isNegative ? 'text-danger-light bg-danger-50' :
                                'text-success-light bg-success-50';

                            // total_amount_formatted değerini row nesnesine ekle
                            row.total_amount_formatted = data;

                            return '<span class="px-2 py-1 rounded ' + cssClass + '">' + data +
                                '</span>';
                        }
                    },
                    {
                        data: null,
                        name: 'status',
                        className: 'text-center',
                        render: function(data, type, row) {
                            // row.total_amount_formatted tanımsız ise varsayılan olarak borç göster
                            if (!row.total_amount_formatted) {
                                return '<span class="badge bg-danger-100 text-danger-600">Borç</span>';
                            }

                            const isNegative = row.total_amount_formatted.startsWith('-');
                            return isNegative ?
                                '<span class="badge bg-danger-100 text-danger-600">Borç</span>' :
                                '<span class="badge bg-success-100 text-success-600">Alacak</span>';
                        },
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: null,
                        name: 'details',
                        className: 'text-center',
                        render: function(data, type, row) {
                            // Basit bir ID oluştur
                            const modalId = 'modal_' + row.id;

                            // Iconify göz ikonu ile buton oluştur
                            return '<button type="button" class="btn btn-sm btn-info rounded-circle w-40-px h-40-px d-flex justify-content-center align-items-center" data-bs-toggle="modal" data-bs-target="#' +
                                modalId + '">' +
                                '<iconify-icon icon="mdi:eye" class="menu-icon"></iconify-icon>' +
                                '</button>' +
                                generateDetailModal(row, modalId);
                        },
                        orderable: false,
                        searchable: false
                    }
                ],
                order: [
                    [2, 'desc']
                ], // Tarihe göre sırala (en yeni en üstte)
                pageLength: 25,
            });

            // Cari, işlem tipi veya tarih değiştiğinde anlık filtreleme yap
            $('#filter-current, #filter-transaction-type, #filter-start-date, #filter-end-date').on('change',
                function() {
                    table.ajax.reload();
                });

            // Cari seçim modalını aç
            $('#btn-select-current').on('click', function() {
                $('#currentSelectModal').modal('show');
            });

            // Cari seçildiğinde
            $('.select-current').on('click', function() {
                const currentId = $(this).data('id');
                const currentName = $(this).data('name');

                $('#filter-current').val(currentId).trigger('change');
                $('#filter-current-name').val(currentName);

                $('#currentSelectModal').modal('hide');
            });

            // Cari arama
            $('#current-search').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('#current-table tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
        });
    </script>
@endsection
