@extends('layout.layout')

@php
    $title = $container->title ?? 'Cari Stok Hareketleri';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
<div class="card basic-data-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        <a href="{{ route('backend.current_stock_movements_list') }}?export=excel" class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
            <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
            Excel
        </a>
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-4">
            <i class="fas fa-info-circle me-2"></i> Bu sayfada tüm cari hesapların stok hareketleri listelenmektedir.
        </div>

        <!-- <PERSON><PERSON>ccordion -->
        <div class="accordion" id="currentAccordion">
            @foreach($currents as $current)
            <div class="accordion-item mb-3 border">
                <h2 class="accordion-header" id="heading{{ $current->id }}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $current->id }}" aria-expanded="false" aria-controls="collapse{{ $current->id }}">
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <div>
                                <strong>{{ $current->name }}</strong>
                            </div>
                            <div class="text-muted me-3">
                                Son İşlem: {{ $currentLastDates[$current->id] ?? '-' }}
                            </div>
                        </div>
                    </button>
                </h2>
                <div id="collapse{{ $current->id }}" class="accordion-collapse collapse" aria-labelledby="heading{{ $current->id }}" data-bs-parent="#currentAccordion">
                    <div class="accordion-body">
                        <div class="text-end mb-3">
                            <button type="button" class="btn btn-sm btn-info rounded-circle w-40-px h-40-px d-flex justify-content-center align-items-center"
                                    data-bs-toggle="modal" data-bs-target="#detailModal{{ $current->id }}">
                                <iconify-icon icon="mdi:eye" class="menu-icon"></iconify-icon>
                            </button>
                        </div>

                        <!-- Ürün Bazlı Tablo -->
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped product-summary-table" id="product-summary-{{ $current->id }}">
                                <thead>
                                    <tr>
                                        <th>Ürün</th>
                                        <th class="text-center">Satış Miktarı</th>
                                        <th class="text-center">Satış Tutarı</th>
                                        <th class="text-center">Alış Miktarı</th>
                                        <th class="text-center">Alış Tutarı</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="5" class="text-center">
                                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Yükleniyor...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detay Modalı -->
            <div class="modal fade" id="detailModal{{ $current->id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ $current->id }}" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="detailModalLabel{{ $current->id }}">{{ $current->name }} - Stok Hareketleri Detayı</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
                        </div>
                        <div class="modal-body">
                            <ul class="nav nav-tabs" id="movementTabs{{ $current->id }}" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="sales-tab{{ $current->id }}" data-bs-toggle="tab" data-bs-target="#sales{{ $current->id }}" type="button" role="tab" aria-controls="sales{{ $current->id }}" aria-selected="true">Satışlar</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="purchases-tab{{ $current->id }}" data-bs-toggle="tab" data-bs-target="#purchases{{ $current->id }}" type="button" role="tab" aria-controls="purchases{{ $current->id }}" aria-selected="false">Alışlar</button>
                                </li>
                            </ul>

                            <div class="tab-content mt-3" id="movementTabContent{{ $current->id }}">
                                <!-- Satışlar Tab -->
                                <div class="tab-pane fade show active" id="sales{{ $current->id }}" role="tabpanel" aria-labelledby="sales-tab{{ $current->id }}">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped sales-table" id="sales-table-{{ $current->id }}">
                                            <thead>
                                                <tr>
                                                    <th>Fatura No</th>
                                                    <th>Tarih</th>
                                                    <th>Ürün</th>
                                                    <th class="text-center">Miktar</th>
                                                    <th class="text-center">Birim Fiyat</th>
                                                    <th class="text-center">Toplam</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="6" class="text-center">
                                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Yükleniyor...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Alışlar Tab -->
                                <div class="tab-pane fade" id="purchases{{ $current->id }}" role="tabpanel" aria-labelledby="purchases-tab{{ $current->id }}">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped purchases-table" id="purchases-table-{{ $current->id }}">
                                            <thead>
                                                <tr>
                                                    <th>Fatura No</th>
                                                    <th>Tarih</th>
                                                    <th>Ürün</th>
                                                    <th class="text-center">Miktar</th>
                                                    <th class="text-center">Birim Fiyat</th>
                                                    <th class="text-center">Toplam</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colspan="6" class="text-center">
                                                        <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Yükleniyor...
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    // Accordion açıldığında veri yükleme
    $('.accordion-button').on('click', function() {
        const currentId = $(this).closest('.accordion-item').find('.accordion-collapse').attr('id').replace('collapse', '');
        loadCurrentStockData(currentId);
    });

    // Modal açıldığında veri yükleme
    $('[data-bs-target^="#detailModal"]').on('click', function() {
        const currentId = $(this).attr('data-bs-target').replace('#detailModal', '');
        loadCurrentDetailData(currentId);
    });

    // Cari için stok verilerini yükle
    function loadCurrentStockData(currentId) {
        const productSummaryTable = $(`#product-summary-${currentId} tbody`);

        // Eğer tablo zaten doldurulmuşsa tekrar yükleme
        if (!productSummaryTable.find('.spinner-border').length) {
            return;
        }

        // Yükleniyor mesajı göster
        productSummaryTable.html('<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Yükleniyor...</td></tr>');

        // API'den verileri getir
        $.ajax({
            url: "{{ url('/admin/current-transactions/stock-relations') }}/" + currentId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Ürün özeti verilerini doldur
                    fillProductSummaryTable(productSummaryTable, response.product_summary);
                } else {
                    // Hata mesajı göster
                    const errorMessage = response.message || 'Veriler getirilirken bir hata oluştu';
                    productSummaryTable.html('<tr><td colspan="5" class="text-center text-danger">' + errorMessage + '</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                // Hata mesajı göster
                const errorMessage = 'Veriler getirilirken bir hata oluştu: ' + error;
                productSummaryTable.html('<tr><td colspan="5" class="text-center text-danger">' + errorMessage + '</td></tr>');
                console.error('Stok hareketleri getirme hatası:', error);
            }
        });
    }

    // Cari için detay verilerini yükle
    function loadCurrentDetailData(currentId) {
        const salesTable = $(`#sales-table-${currentId} tbody`);
        const purchasesTable = $(`#purchases-table-${currentId} tbody`);

        // Eğer tablolar zaten doldurulmuşsa tekrar yükleme
        if (!salesTable.find('.spinner-border').length && !purchasesTable.find('.spinner-border').length) {
            return;
        }

        // Yükleniyor mesajı göster
        salesTable.html('<tr><td colspan="6" class="text-center"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Yükleniyor...</td></tr>');
        purchasesTable.html('<tr><td colspan="6" class="text-center"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div> Yükleniyor...</td></tr>');

        // API'den verileri getir
        $.ajax({
            url: "{{ url('/admin/current-transactions/stock-relations') }}/" + currentId,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Satış ve alış verilerini doldur
                    fillSalesTable(salesTable, response.sales_items);
                    fillPurchasesTable(purchasesTable, response.purchase_items);
                } else {
                    // Hata mesajı göster
                    const errorMessage = response.message || 'Veriler getirilirken bir hata oluştu';
                    salesTable.html('<tr><td colspan="6" class="text-center text-danger">' + errorMessage + '</td></tr>');
                    purchasesTable.html('<tr><td colspan="6" class="text-center text-danger">' + errorMessage + '</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                // Hata mesajı göster
                const errorMessage = 'Veriler getirilirken bir hata oluştu: ' + error;
                salesTable.html('<tr><td colspan="6" class="text-center text-danger">' + errorMessage + '</td></tr>');
                purchasesTable.html('<tr><td colspan="6" class="text-center text-danger">' + errorMessage + '</td></tr>');
                console.error('Stok hareketleri detay getirme hatası:', error);
            }
        });
    }

    // Ürün özeti tablosunu doldur
    function fillProductSummaryTable(tbody, data) {
        tbody.empty();

        if (data && data.length > 0) {
            data.forEach(function(item) {
                const row = `
                    <tr>
                        <td>${item.product_name}</td>
                        <td class="text-center">${formatNumber(item.sales_quantity)}</td>
                        <td class="text-center">${formatCurrency(item.sales_total)}</td>
                        <td class="text-center">${formatNumber(item.purchase_quantity)}</td>
                        <td class="text-center">${formatCurrency(item.purchase_total)}</td>
                    </tr>
                `;

                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="5" class="text-center">Veri bulunamadı</td></tr>');
        }
    }

    // Satışlar tablosunu doldur
    function fillSalesTable(tbody, data) {
        tbody.empty();

        if (data && data.length > 0) {
            data.forEach(function(item) {
                const row = `
                    <tr>
                        <td>${item.invoice_no}</td>
                        <td>${item.invoice_date}</td>
                        <td>${item.product_name}</td>
                        <td class="text-center">${formatNumber(item.quantity)}</td>
                        <td class="text-center">${formatCurrency(item.unit_price)}</td>
                        <td class="text-center">${formatCurrency(item.total)}</td>
                    </tr>
                `;

                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="6" class="text-center">Satış verisi bulunamadı</td></tr>');
        }
    }

    // Alışlar tablosunu doldur
    function fillPurchasesTable(tbody, data) {
        tbody.empty();

        if (data && data.length > 0) {
            data.forEach(function(item) {
                const row = `
                    <tr>
                        <td>${item.invoice_no}</td>
                        <td>${item.invoice_date}</td>
                        <td>${item.product_name}</td>
                        <td class="text-center">${formatNumber(item.quantity)}</td>
                        <td class="text-center">${formatCurrency(item.unit_price)}</td>
                        <td class="text-center">${formatCurrency(item.total)}</td>
                    </tr>
                `;

                tbody.append(row);
            });
        } else {
            tbody.append('<tr><td colspan="6" class="text-center">Alış verisi bulunamadı</td></tr>');
        }
    }

    // Sayı formatla
    function formatNumber(value) {
        return parseFloat(value).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }

    // Para birimi formatla
    function formatCurrency(value) {
        return parseFloat(value).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' ₺';
    }
});
</script>
@endsection
