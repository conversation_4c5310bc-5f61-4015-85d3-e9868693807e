@extends('layout.layout')

@php
    $title = $container->title ?? 'Cari Stok Hareketleri';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
<div class="card basic-data-table">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        <a href="{{ route('backend.current_stock_movements_list') }}?export=excel" class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
            <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
            Excel
        </a>
    </div>
    <div class="card-body">
        <!-- Filtreler -->
        <div class="row mb-4">
            <div class="col-md-3">
                <label class="form-label">Cari</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="current-name" readonly placeholder="<PERSON><PERSON> se<PERSON>">
                    <input type="hidden" id="filter-current" value="">
                    <button class="btn btn-outline-secondary" type="button" id="btn-select-current">
                        <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">Ürün</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="product-name" readonly placeholder="Ürün seçiniz">
                    <input type="hidden" id="filter-product" value="">
                    <button class="btn btn-outline-secondary" type="button" id="btn-select-product">
                        <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">Başlangıç Tarihi</label>
                <input type="date" class="form-control" id="filter-start-date">
            </div>
            <div class="col-md-2">
                <label class="form-label">Bitiş Tarihi</label>
                <input type="date" class="form-control" id="filter-end-date">
            </div>
            <div class="col-md-2">
                <label class="form-label">İşlem Tipi</label>
                <select class="form-control" id="filter-transaction-type">
                    <option value="">Tüm İşlemler</option>
                    <option value="sales">Satış</option>
                    <option value="purchase">Alış</option>
                </select>
            </div>
        </div>


        <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="25">
            <thead>
                <tr>
                    <th scope="col" class="text-center">İşlem No</th>
                    <th scope="col" class="text-center">Cari</th>
                    <th scope="col" class="text-center">Ürün</th>
                    <th scope="col" class="text-center">Tarih</th>
                    <th scope="col" class="text-center">İşlem Tipi</th>
                    <th scope="col" class="text-center">Miktar</th>
                    <th scope="col" class="text-center">Birim Fiyat</th>
                    <th scope="col" class="text-center">Toplam</th>
                    <th scope="col" class="text-center">Detay</th>
                </tr>
            </thead>
            <tbody></tbody>
        </table>
    </div>
</div>

<!-- Cari Seçim Modalı -->
<div class="modal fade" id="currentSelectModal" tabindex="-1" aria-labelledby="currentSelectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currentSelectModalLabel">Cari Seçimi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="current-search" placeholder="Cari ara...">
                </div>
                <div class="table-responsive">
                    <table class="table bordered-table mb-0" id="current-table">
                        <thead>
                            <tr>
                                <th>Cari Adı</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($currents as $current)
                            <tr>
                                <td>{{ $current->name }}</td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-primary select-current" data-id="{{ $current->id }}" data-name="{{ $current->name }}">Seç</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<!-- Ürün Seçim Modalı -->
<div class="modal fade" id="productSelectModal" tabindex="-1" aria-labelledby="productSelectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productSelectModalLabel">Ürün Seçimi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="product-search" placeholder="Ürün ara...">
                </div>
                <div class="table-responsive">
                    <table class="table bordered-table mb-0" id="product-table">
                        <thead>
                            <tr>
                                <th>Ürün Adı</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($products as $product)
                            <tr>
                                <td>{{ $product->name }}</td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-primary select-product" data-id="{{ $product->id }}" data-name="{{ $product->name }}">Seç</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<!-- Detay Modalı Template -->
<div class="modal fade" id="detailModalTemplate" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">İşlem Detayı</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>İşlem No:</strong> <span id="detail-invoice-no"></span></p>
                        <p><strong>İşlem Tarihi:</strong> <span id="detail-invoice-date"></span></p>
                        <p><strong>Cari Hesap:</strong> <span id="detail-current-name"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>İşlem Tipi:</strong> <span id="detail-transaction-type"></span></p>
                        <p><strong>Ürün:</strong> <span id="detail-product-name"></span></p>
                        <p><strong>Toplam Tutar:</strong> <span id="detail-total-amount"></span></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
$(document).ready(function() {
    BaseCRUD.selector = "[datatable]";

    // DataTable'ı oluştur
    var table = BaseCRUD.ajaxtable({
        ajax: {
            url: "{{ route('backend.current_stock_movements_list') }}?datatable=true",
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: function(d) {
                d.current_id = $('#filter-current').val();
                d.product_id = $('#filter-product').val();
                d.start_date = $('#filter-start-date').val();
                d.end_date = $('#filter-end-date').val();
                d.transaction_type = $('#filter-transaction-type').val();
                return d;
            }
        },
        columns: [
            { data: 'invoice_no',     name: 'invoice_no',   className: 'text-center' },
            { data: 'current_name',   name: 'current_name', className: 'text-center' },
            { data: 'product_name',   name: 'product_name', className: 'text-center' },
            { data: 'invoice_date',   name: 'invoice_date', className: 'text-center' },
            { data: 'transaction_type_name', name: 'transaction_type_name', className: 'text-center' },
            {
                data: 'quantity',
                name: 'quantity',
                className: 'text-center',
                render: function(data, type, row) {
                    return formatNumber(data);
                }
            },
            {
                data: 'unit_price',
                name: 'unit_price',
                className: 'text-center',
                render: function(data, type, row) {
                    return formatCurrency(data);
                }
            },
            {
                data: 'total',
                name: 'total',
                className: 'text-center',
                render: function(data, type, row) {
                    return formatCurrency(data);
                }
            },
            {
                data: null,
                name: 'details',
                className: 'text-center',
                render: function(data, type, row) {
                    return `
                    <div class="d-flex justify-content-center">
                        <button type="button" class="btn btn-sm btn-info rounded-circle w-40-px h-40-px d-flex justify-content-center align-items-center show-detail"
                                data-invoice-no="${row.invoice_no}"
                                data-invoice-date="${row.invoice_date}"
                                data-current-name="${row.current_name}"
                                data-transaction-type="${row.transaction_type_name}"
                                data-product-name="${row.product_name}"
                                data-total="${formatCurrency(row.total)}">
                            <iconify-icon icon="mdi:eye" class="menu-icon"></iconify-icon>
                        </button>
                    </div>`;
                },
                orderable: false,
                searchable: false
            }
        ],
        order: [[3, 'desc']], // Tarihe göre sırala (en yeni en üstte)
        pageLength: 25,
    });

    // Tarih ve işlem tipi filtrelerinde değişiklik olduğunda otomatik filtrele
    $('#filter-start-date, #filter-end-date, #filter-transaction-type').on('change', function() {
        table.ajax.reload();
    });

    // Cari seçim modalını aç
    $('#btn-select-current').on('click', function() {
        $('#currentSelectModal').modal('show');
    });

    // Ürün seçim modalını aç
    $('#btn-select-product').on('click', function() {
        $('#productSelectModal').modal('show');
    });

    // Cari seçildiğinde
    $('.select-current').on('click', function() {
        const currentId = $(this).data('id');
        const currentName = $(this).data('name');
        $('#filter-current').val(currentId);
        $('#current-name').val(currentName);
        $('#currentSelectModal').modal('hide');
        // Otomatik filtrele
        table.ajax.reload();
    });

    // Ürün seçildiğinde
    $('.select-product').on('click', function() {
        const productId = $(this).data('id');
        const productName = $(this).data('name');
        $('#filter-product').val(productId);
        $('#product-name').val(productName);
        $('#productSelectModal').modal('hide');
        // Otomatik filtrele
        table.ajax.reload();
    });

    // Cari arama
    $('#current-search').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#current-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Ürün arama
    $('#product-search').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#product-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Detay göster
    $(document).on('click', '.show-detail', function() {
        const invoiceNo = $(this).data('invoice-no');
        const invoiceDate = $(this).data('invoice-date');
        const currentName = $(this).data('current-name');
        const transactionType = $(this).data('transaction-type');
        const productName = $(this).data('product-name');
        const total = $(this).data('total');

        $('#detail-invoice-no').text(invoiceNo);
        $('#detail-invoice-date').text(invoiceDate);
        $('#detail-current-name').text(currentName);
        $('#detail-transaction-type').text(transactionType);
        $('#detail-product-name').text(productName);
        $('#detail-total-amount').text(total);

        $('#detailModalTemplate').modal('show');
    });

    // Sayı formatla
    function formatNumber(value) {
        return parseFloat(value).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }

    // Para birimi formatla
    function formatCurrency(value) {
        return parseFloat(value).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' ₺';
    }
});
</script>
@endsection
