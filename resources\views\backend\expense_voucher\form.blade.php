@extends('layout.layout')

@php
    $title = $container->title ?? 'Mas<PERSON>f <PERSON>ler<PERSON>';
    $subTitle = ($item->id ? 'Düzenle' : 'Ekle') . ' - ' . $title;
@endphp

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('backend.' . $container->page . '_save', $item->id ?? null) }}" method="POST"
                enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="voucher_no" class="form-label">Fiş Numarası</label>
                            <input type="text" class="form-control" id="voucher_no" name="voucher_no"
                                value="{{ old('voucher_no', $item->voucher_no) }}" readonly
                                placeholder="Otomatik oluşturulacak">
                            <small class="text-muted">Fiş numarası otomatik olarak oluşturulacaktır.</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="voucher_date" class="form-label">Fiş Tarihi <span
                                    class="text-danger">*</span></label>
                            <input type="date" class="form-control @error('voucher_date') is-invalid @enderror"
                                id="voucher_date" name="voucher_date"
                                value="{{ old('voucher_date', $item->voucher_date?->format('Y-m-d')) }}">
                            @error('voucher_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="current_id" class="form-label">Cari Hesap <span class="text-danger">*</span></label>
                            <select class="form-select @error('current_id') is-invalid @enderror" id="current_id"
                                name="current_id">
                                <option value="">Cari Hesap Seçiniz</option>
                                @foreach ($currents as $current)
                                    <option value="{{ $current->id }}"
                                        {{ old('current_id', $item->current_id) == $current->id ? 'selected' : '' }}>
                                        {{ $current->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('current_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="expense_type_id" class="form-label">Masraf Türü <span
                                    class="text-danger">*</span></label>
                            <select class="form-select @error('expense_type_id') is-invalid @enderror" id="expense_type_id"
                                name="expense_type_id">
                                <option value="">Masraf Türü Seçiniz</option>
                                @foreach ($expenseTypes as $expenseType)
                                    <option value="{{ $expenseType->id }}"
                                        {{ old('expense_type_id', $item->expense_type_id) == $expenseType->id ? 'selected' : '' }}>
                                        {{ $expenseType->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('expense_type_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="payment_type_id" class="form-label">Ödeme Türü <span
                                    class="text-danger">*</span></label>
                            <select class="form-select @error('payment_type_id') is-invalid @enderror" id="payment_type_id"
                                name="payment_type_id">
                                <option value="">Ödeme Türü Seçiniz</option>
                                @foreach ($paymentTypes as $paymentType)
                                    <option value="{{ $paymentType->id }}"
                                        {{ old('payment_type_id', $item->payment_type_id) == $paymentType->id ? 'selected' : '' }}>
                                        {{ $paymentType->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('payment_type_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">Durum</label>
                            <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                <option value="pending" {{ old('status', $item->status) == 'pending' ? 'selected' : '' }}>
                                    Beklemede</option>
                                <option value="approved"
                                    {{ old('status', $item->status) == 'approved' ? 'selected' : '' }}>Onaylandı</option>
                                <option value="rejected"
                                    {{ old('status', $item->status) == 'rejected' ? 'selected' : '' }}>Reddedildi</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">Açıklama</label>
                    <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description"
                        rows="3">{{ old('description', $item->description) }}</textarea>
                    @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="mb-3">
                    <label for="attachment" class="form-label">Ek Dosya</label>
                    <input type="file" class="form-control @error('attachment') is-invalid @enderror" id="attachment"
                        name="attachment" accept=".jpg,.jpeg,.png,.pdf">
                    @if ($item->attachment)
                        <small class="text-muted">Mevcut dosya: {{ basename($item->attachment) }}</small>
                    @endif
                    @error('attachment')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="subtotal" class="form-label">Ara Toplam <span class="text-danger">*</span></label>
                            <input type="number" step="0.01"
                                class="form-control @error('subtotal') is-invalid @enderror" id="subtotal"
                                name="subtotal" value="{{ old('subtotal', $item->subtotal) }}">
                            @error('subtotal')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="vat_total" class="form-label">KDV Tutarı</label>
                            <input type="number" step="0.01"
                                class="form-control @error('vat_total') is-invalid @enderror" id="vat_total"
                                name="vat_total" value="{{ old('vat_total', $item->vat_total) }}">
                            @error('vat_total')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="grand_total" class="form-label">Genel Toplam <span
                                    class="text-danger">*</span></label>
                            <input type="number" step="0.01"
                                class="form-control @error('grand_total') is-invalid @enderror" id="grand_total"
                                name="grand_total" value="{{ old('grand_total', $item->grand_total) }}">
                            @error('grand_total')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="auto_accounting" name="auto_accounting"
                        value="1" {{ old('auto_accounting', $item->auto_accounting) ? 'checked' : '' }}>
                    <label class="form-check-label" for="auto_accounting">
                        Otomatik Muhasebeleştirme
                    </label>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('backend' . $container->page . '_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Geri Dön
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Kaydet
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
