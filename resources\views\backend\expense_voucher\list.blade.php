@extends('layout.layout')

@php
    $title = $container->title ?? 'Masraf <PERSON>';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . $container->page . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">Fiş No</th>
                            <th scope="col" class="text-center">Fiş Tarihi</th>
                            <th scope="col" class="text-center">Cari</th>
                            <th scope="col" class="text-center"><PERSON><PERSON><PERSON><PERSON></th>
                            <th scope="col" class="text-center">Ödeme Türü</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            console.log('Expense Vouchers - Starting...');

            BaseCRUD.selector = "[datatable]";

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        var cfilter = {
                            branch: $('[filter-name="branch"]').val()
                        };
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    }
                },
                columns: [{
                        data: 'voucher_no'
                    },
                    {
                        data: 'voucher_date'
                    },
                    {
                        data: 'current_name'
                    },
                    {
                        data: 'expense_type_name'
                    },
                    {
                        data: 'payment_type_name'
                    },
                    {
                        data: 'grand_total'
                    },
                    {
                        data: 'status_name'
                    },
                    {
                        data: 'actions'
                    }
                ],
                order: [
                    [1, 'desc']
                ],
                pageLength: 15,
            });

            $('[filter-name]').change(function() {
                table.ajax.reload();
            });

            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");
        });
    </script>
@endsection
