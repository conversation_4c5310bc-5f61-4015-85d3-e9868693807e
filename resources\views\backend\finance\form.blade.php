@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON>le');
@endphp

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">
                    {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                </h5>
            </div>

            <div class="card-body">
                <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}" method="POST">
                    @csrf
                    <div class="row gy-3">
                        <div class="col-6">
                            <label class="form-label">Belge No</label>
                            <div class="icon-field">
                                <span class="icon">
                                    <iconify-icon icon="f7:person"></iconify-icon>
                                </span>
                                <input type="text" class="form-control" placeholder="Lütfen belge numrasını giriniz"
                                       name="document_no"
                                       value="{{ old('document_no', $item->document_no ?? '') }}">
                                <x-form-error field="document_no" />
                            </div>
                        </div>
                        {{-- Cari Seçimi --}}
                        <div class="col-md-6">
                            <div class="form-group mb-2">
                                <label class="form-label">Cari Seçimi</label>
                                <select class="form-control select2" name="current_id" required>
                                    <option value="">Cari Seçin</option>
                                    @foreach ($current as $cr)
                                        <option value="{{ $cr->id }}"
                                            {{ old('current_id', $item->current_id) == $cr->id ? 'selected' : '' }}>
                                            {{ $cr->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('current_id')
                                    <span class="badge badge-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-2">
                                <label class="form-label">İşlem Tipi</label>
                                <select class="form-control select2" name="document_type_id" required>
                                    <option value="">İşlem Tipi Seçin</option>
                                    @foreach ($document_type as $docType) <!-- ARTI: Değişken isimlendirmesi -->
                                        <option value="{{ $docType->id }}"
                                            {{ old('document_type_id', $item->document_type_id) == $docType->id ? 'selected' : '' }}>
                                            {{ $docType->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('document_type_id') <!-- ARTI: Doğru alan ismi -->
                                    <span class="badge badge-danger">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-6">
                            <label class="form-label">İşlem Tutarı</label>
                            <div class="icon-field">
                                <span class="icon">
                                    <iconify-icon icon="mdi:currency-try"></iconify-icon>
                                </span>
                                <input type="text" class="form-control" id="amount" name="amount"
                                       value="{{ old('amount', $item->amount ?? '') }}"
                                       placeholder="00.00">
                                <x-form-error field="amount" />
                            </div>
                        </div>
                        <div class="row gy-3">
                            <div class="col-6">
                                <label class="form-label">Açıklama</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Açıklama girebilirsiniz..."
                                           name="description"
                                           value="{{ old('description', $item->description ?? '') }}">
                                    <x-form-error field="description" />
                                </div>
                            </div>
                        <div class="col-6">
                            <label class="form-label">Durum</label>
                            <div class="icon-field">
                                <span class="icon">
                                    <iconify-icon icon="carbon:badge"></iconify-icon>
                                </span>
                                <select class="form-control form-select" name="is_active">
                                    <option value="1" {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif</option>
                                    <option value="0" {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif</option>
                                </select>
                                <x-form-error field="is_active" />
                            </div>
                        </div>

                        {{-- Kaydet Butonu --}}
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary-600">Kaydet</button>
                        </div>
                    </div><!-- row -->
                </form>
            </div><!-- card-body -->
        </div><!-- card -->
    </div><!-- col-md-12 -->
</div><!-- row -->
@endsection
