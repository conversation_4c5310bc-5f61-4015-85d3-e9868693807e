@extends('layout.layout')

@php
    // Örnek: $container->title = 'Finans'; $container->page = 'finance';
    $title = $container->title ?? '<PERSON><PERSON> Fişleri';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . ($container->page ?? 'finance') . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center"><PERSON><PERSON></th>
                            <th scope="col" class="text-center">İşlem Tipi</th>
                            <th scope="col" class="text-center">İşlem Tarihi</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . ($container->page ?? 'finance') . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        // Örneğin, filtre varsa buraya ekleyebilirsiniz
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    }
                },
                columns: [
                    // Cari Ünvanı
                    {
                        data: 'cari_unvani',
                        name: 'finances.current_id', // Bu sütun bir ilişkiden geldiği için ya "finance.current_id" ya da orderable false
                        className: 'text-center',
                        orderable: false, // Gerçek kolondan sıralama istemiyorsanız false yapabilirsiniz
                        searchable: false
                    },
                    // İşlem Tipi
                    {
                        data: 'islem_tipi',
                        name: 'finances.document_type_id', // Aynı mantık, doğrudan DB kolonu değil => orderable false olabilir
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    },
                    // İşlem Tarihi (finance.created_at)
                    {
                        data: 'islem_tarihi',
                        name: 'finances.created_at', // Bu kolonu sıralamak isterseniz
                        className: 'text-center'
                    },
                    // Durum (finance.is_active)
                    {
                        data: 'durum',
                        name: 'finances.is_active', // 0/1 değerinden sıralama veya arama istenebilir
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    },
                    // İşlemler (Düzenle / Sil)
                    {
                        data: 'actions',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                ],
                // Varsayılan sıralamayı "İşlem Tarihi" (3. kolon index=2) desc yapabilirsiniz:
                order: [
                    [2, 'desc']
                ],
                pageLength: 15,
            });

            // Filtre değiştiğinde tabloyu yenilemek
            $('[filter-name]').change(function() {
                table.ajax.reload();
            });

            // Silme işlemi (BaseCRUD.delete)
            BaseCRUD.delete("{{ route('backend.' . ($container->page ?? 'finance') . '_delete') }}");
        });
    </script>
@endsection
