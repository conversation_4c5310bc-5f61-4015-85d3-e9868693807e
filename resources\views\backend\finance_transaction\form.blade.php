@extends('layout.layout')

@php
    // Başlıklar (Örnek)
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
@endphp

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">
                    {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                </h5>
            </div>

            <div class="card-body">
                <!-- DİKKAT: Dosya yükleme yapabilmek için "enctype" ekliyoruz -->
                <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                      method="POST"
                      enctype="multipart/form-data">
                    @csrf

                    <div class="row gy-3">
                        {{-- Tarih --}}
                        <div class="col-md-6">
                            <label class="form-label">İşlem Tarihi</label>
                            <input type="datetime-local" class="form-control" name="transaction_date"
                                   value="{{ old('transaction_date', $item->transaction_date ?? '') }}">
                            <x-form-error field="transaction_date" />
                        </div>

                        {{-- İşlem Tipi --}}
                        <div class="col-md-6">
                            <label class="form-label">İşlem Tipi</label>
                            <select class="form-control select2" name="transaction_type" required>
                                <option value="">Seçiniz</option>
                                <option value="cash" {{ old('transaction_type', $item->transaction_type) == 'cash' ? 'selected' : '' }}>Nakit</option>
                                <option value="bank" {{ old('transaction_type', $item->transaction_type) == 'bank' ? 'selected' : '' }}>Banka</option>
                                <option value="check" {{ old('transaction_type', $item->transaction_type) == 'check' ? 'selected' : '' }}>Çek</option>
                                <option value="promissory_note" {{ old('transaction_type', $item->transaction_type) == 'promissory_note' ? 'selected' : '' }}>Senet</option>
                            </select>
                            <x-form-error field="transaction_type" />
                        </div>

                        {{-- Cari --}}
                        <div class="col-md-6">
                            <label class="form-label">Cari Seçimi</label>
                            <select class="form-control select2" name="current_id" required>
                                <option value="">Cari Seç</option>
                                @foreach ($current as $cr)
                                    <option value="{{ $cr->id }}" {{ old('current_id', $item->current_id) == $cr->id ? 'selected' : '' }}>
                                        {{ $cr->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-form-error field="current_id" />
                        </div>

                        {{-- Döviz ve Kur --}}
                        <div class="col-md-6">
                            <label class="form-label">Döviz</label>
                            <select class="form-control select2" name="currency_id">
                                <option value="">Seçiniz</option>
                                @foreach ($currency as $cur)
                                    <option value="{{ $cur->id }}" {{ old('currency_id', $item->currency_id) == $cur->id ? 'selected' : '' }}>
                                        {{ $cur->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-form-error field="currency_id" />
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Döviz Kuru</label>
                            <input type="text" class="form-control" name="exchange_rate"
                                   value="{{ old('exchange_rate', $item->exchange_rate ?? '1') }}" placeholder="1.0000">
                            <x-form-error field="exchange_rate" />
                        </div>

                        {{-- Belge No --}}
                        <div class="col-md-6">
                            <label class="form-label">Belge No</label>
                            <input type="text" class="form-control" name="doc_no"
                                   value="{{ old('doc_no', $item->doc_no ?? '') }}" placeholder="Otomatik üretilebilir">
                            <x-form-error field="doc_no" />
                        </div>

                        {{-- Tutar --}}
                        <div class="col-md-6">
                            <label class="form-label">İşlem Tutarı</label>
                            <input type="text" class="form-control" name="amount"
                                   value="{{ old('amount', $item->amount ?? '') }}" placeholder="0.00">
                            <x-form-error field="amount" />
                        </div>

                        {{-- Açıklama --}}
                        <div class="col-md-6">
                            <label class="form-label">Açıklama</label>
                            <input type="text" class="form-control" name="description"
                                   value="{{ old('description', $item->description ?? '') }}" placeholder="Açıklama giriniz">
                            <x-form-error field="description" />
                        </div>

                        {{-- Mutabakat --}}
                        <div class="col-md-6">
                            <label class="form-label">Mutabakat Durumu</label>
                            <select class="form-control" name="is_reconciled">
                                <option value="0" {{ old('is_reconciled', $item->is_reconciled ?? 0) == 0 ? 'selected' : '' }}>Yapılmadı</option>
                                <option value="1" {{ old('is_reconciled', $item->is_reconciled ?? 0) == 1 ? 'selected' : '' }}>Yapıldı</option>
                            </select>
                            <x-form-error field="is_reconciled" />
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Mutabakat Tarihi</label>
                            <input type="datetime-local" class="form-control" name="reconciliation_date"
                                   value="{{ old('reconciliation_date', $item->reconciliation_date ?? '') }}">
                            <x-form-error field="reconciliation_date" />
                        </div>

                        {{-- Banka Bilgileri --}}
                        <div class="col-md-6">
                            <label class="form-label">Banka Adı</label>
                            <input type="text" class="form-control" name="bank_name"
                                   value="{{ old('bank_name', $item->bank_name ?? '') }}">
                            <x-form-error field="bank_name" />
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Banka Hesap / IBAN</label>
                            <input type="text" class="form-control" name="bank_account"
                                   value="{{ old('bank_account', $item->bank_account ?? '') }}">
                            <x-form-error field="bank_account" />
                        </div>

                        {{-- Çek/Senet --}}
                        <div class="col-md-4">
                            <label class="form-label">Çek/Senet No</label>
                            <input type="text" class="form-control" name="check_number"
                                   value="{{ old('check_number', $item->check_number ?? '') }}">
                            <x-form-error field="check_number" />
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">Onaylayan Yetkili</label>
                            <input type="text" class="form-control" name="drawer_name"
                                   value="{{ old('drawer_name', $item->drawer_name ?? '') }}">
                            <x-form-error field="drawer_name" />
                        </div>

                        <div class="col-md-4">
                            <label class="form-label">Vade Tarihi</label>
                            <input type="date" class="form-control" name="due_date"
                                   value="{{ old('due_date', $item->due_date ?? '') }}">
                            <x-form-error field="due_date" />
                        </div>

                        {{-- Dosya (Makbuz / Dekont) --}}
                        <div class="col-md-6">
                            <label class="form-label">Makbuz / Dekont Dosyası</label>
                            <!-- Sadece PDF, PNG, DOC, DOCX dosyalarına izin vermek için accept kullanıyoruz -->
                            <input type="file"
                                   class="form-control"
                                   name="receipt_file"
                                   accept=".pdf,.png,.doc,.docx">
                            <x-form-error field="receipt_file" />
                        </div>

                        {{-- Durum --}}
                        <div class="col-md-6">
                            <label class="form-label">Durum</label>
                            <select class="form-control" name="is_active">
                                <option value="1" {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif</option>
                                <option value="0" {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif</option>
                            </select>
                            <x-form-error field="is_active" />
                        </div>

                        {{-- Kaydet --}}
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary-600">Kaydet</button>
                        </div>
                    </div> {{-- .row --}}
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
