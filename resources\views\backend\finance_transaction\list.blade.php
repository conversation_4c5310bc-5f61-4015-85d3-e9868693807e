@extends('layout.layout')

@php
    // Örnek: $container->title = 'Finans'; $container->page = 'finance';
    $title = $container->title ?? 'Finans';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <a href="{{ route('backend.' . ($container->page ?? 'finance') . '_form') }}"
                class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                Ekle
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center"><PERSON><PERSON></th>
                            <th scope="col" class="text-center"><PERSON>ş<PERSON> Tarihi</th>
                            <th scope="col" class="text-center">İşlem Tutarı</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . ($container->page ?? 'finance') . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        // Örneğin, filtre alanlarınız varsa buraya ekleyebilirsiniz
                        // var cfilter = {
                        //     branch: $('[filter-name="branch"]').val()
                        // };
                        // return $.extend({}, d, { "cfilter": cfilter });
                        return d;
                    }
                },
                columns: [{
                        data: 'cari_unvani',
                        name: 'current_id', // veya boş bırak: ''
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'islem_tarihi',
                        name: 'transaction_date', // doğru kolon adı!
                        className: 'text-center',
                        orderable: true,
                        searchable: false
                    },
                    {
                        data: 'islem_tutari',
                        name: 'amount', // sıralanabilir
                        className: 'text-center'
                    },
                    {
                        data: 'actions',
                        orderable: false,
                        searchable: false,
                        className: 'text-center'
                    },
                ],
                order: [
                    [1, 'desc']
                ], // created_at değil, islem_tarihi olan kolonun indexi!

            });

            // Filtre değişimi -> tabloyu yenile
            $('[filter-name]').change(function() {
                table.ajax.reload();
            });

            // Silme işlemi
            BaseCRUD.delete("{{ route('backend.' . ($container->page ?? 'finance') . '_delete') }}");
        });
    </script>
@endsection
