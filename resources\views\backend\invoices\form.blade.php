@extends('layout.layout')

@php
    $title = $container->title ?? 'Satış Faturaları';
    $subTitle = $title . ' ' . ($item->id ? 'Düzenle' : 'Ekle');
@endphp

@section('content')
<div class="row gy-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0 fs-6">{{ $container->title }} Fatura</h5>
            </div>
            <div class="card-body">
                <form action="{{ route('backend.invoices_save', $item->id ?? null) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <!-- Cari hesap ID'si -->
                    <input type="hidden" name="current_id" id="current_id" value="{{ $item->current_id }}" required>
                    <!-- We'll use JavaScript to handle the invoice number, no hidden field needed -->

                    <div class="row gy-3">
                        <!-- Fatura No -->
                        <div class="col-md-4">
                            <label class="form-label">Fatura No</label>
                            <input type="text" class="form-control" id="invoice_no" name="invoice_no" value="{{ $item->invoice_no }}" readonly>
                            <div class="text-info small">Fatura Numarası: {{ $item->invoice_no }}</div>
                            <small class="text-muted">Format: FTR/04/2025/XXXXXXX (otomatik oluşturulur ve her yeni faturada artar)</small>
                        </div>

                        <!-- Fatura Tarihi -->
                        <div class="col-md-4">
                            <label class="form-label">Fatura Tarihi</label>
                            <input type="datetime-local" class="form-control" name="invoice_date" value="{{ $item->invoice_date ? date('Y-m-d\TH:i', strtotime($item->invoice_date)) : '' }}">
                        </div>

                        <!-- Vade Tarihi -->
                        <div class="col-md-4">
                            <label class="form-label">Vade Tarihi</label>
                            <input type="datetime-local" class="form-control" name="due_date">
                        </div>

                        <!-- Cari seçimi tablo içine taşındı -->
                    </div>

                    <hr class="my-4">

                    <!-- Ürün Tablosu -->
                    <div class="table-responsive">
                    <table class="table bordered-table mb-0 dataTable" id="invoice-table">
                        <thead>
                            <tr>
                                <th>Cari</th>
                                <th>Ürün</th>
                                <th>Miktar</th>
                                <th>Birim Fiyat</th>
                                <th>
                                    <div class="d-flex align-items-center justify-content-between">
                                        KDV (%)
                                        <div class="form-check form-switch ms-2 mb-0">
                                            <input class="form-check-input global-vat-switch" type="checkbox" role="switch" id="global-vat-included-switch">
                                            <label class="form-check-label small" for="global-vat-included-switch">Dahil</label>
                                        </div>
                                    </div>
                                </th>
                                <th>Toplam</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(isset($item->items) && count($item->items) > 0)
                                @foreach($item->items as $index => $invoiceItem)
                                <tr>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control current-name" readonly placeholder="Cari seçiniz" value="{{ $item->current ? $item->current->name : '' }}">
                                            <input type="hidden" class="current-id" value="{{ $item->current_id }}" required>
                                            <button class="btn btn-outline-secondary btn-select-current" type="button">
                                                <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control product-name" readonly placeholder="Ürün seçiniz" value="{{ $invoiceItem['product_name'] ?? '' }}">
                                            <input type="hidden" name="items[{{ $index }}][product_id]" class="product-id" value="{{ $invoiceItem['product_id'] }}" required>
                                            <button class="btn btn-outline-secondary btn-select-product" type="button">
                                                <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                                            </button>
                                        </div>
                                    </td>
                                    <td><input type="number" name="items[{{ $index }}][quantity]" class="form-control quantity" value="{{ $invoiceItem['quantity'] }}" min="1" required></td>
                                    <td><input type="text" name="items[{{ $index }}][sale_price]" class="form-control sale-price" value="{{ $invoiceItem['sale_price'] }}" required></td>
                                    <td>
                                        <input type="text" name="items[{{ $index }}][default_vat_rate]" class="form-control vat-rate" value="{{ $invoiceItem['default_vat_rate'] ?? 18 }}" required>
                                        <input type="hidden" class="vat-included-switch" value="0" data-row-index="{{ $index }}">
                                        <input type="hidden" name="items[{{ $index }}][vat_amount]" class="vat-amount" value="{{ $invoiceItem['vat_amount'] ?? 0 }}">
                                    </td>
                                    <td><input type="text" name="items[{{ $index }}][total]" class="form-control total" value="{{ $invoiceItem['total'] }}" readonly></td>
                                    <td><button type="button" class="btn btn-danger btn-remove-row">Sil</button></td>
                                </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control current-name" readonly placeholder="Cari seçiniz" value="{{ $item->current ? $item->current->name : '' }}">
                                            <input type="hidden" class="current-id" value="{{ $item->current_id }}" required>
                                            <button class="btn btn-outline-secondary btn-select-current" type="button">
                                                <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                                            </button>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group">
                                            <input type="text" class="form-control product-name" readonly placeholder="Ürün seçiniz">
                                            <input type="hidden" name="items[0][product_id]" class="product-id" value="" required>
                                            <button class="btn btn-outline-secondary btn-select-product" type="button">
                                                <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                                            </button>
                                        </div>
                                    </td>
                                    <td><input type="number" name="items[0][quantity]" class="form-control quantity" value="1" min="1" required></td>
                                    <td><input type="text" name="items[0][sale_price]" class="form-control sale-price" value="0" required></td>
                                    <td>
                                        <input type="text" name="items[0][default_vat_rate]" class="form-control vat-rate" value="18" required>
                                        <input type="hidden" class="vat-included-switch" value="0" data-row-index="0">
                                        <input type="hidden" name="items[0][vat_amount]" class="vat-amount" value="0">
                                    </td>
                                    <td><input type="text" name="items[0][total]" class="form-control total" value="0" readonly></td>
                                    <td><button type="button" class="btn btn-danger btn-remove-row">Sil</button></td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                    </div>

                    <button type="button" id="add-row" class="btn btn-primary btn-sm rounded-pill mb-4">Satır Ekle</button>

                    <div class="row gy-3">
                        <!-- Açıklama -->
                        <div class="col-md-6">
                            <label class="form-label">Açıklama</label>
                            <input type="text" class="form-control" name="description">
                        </div>

                        <!-- Fatura Dosyası seçeneği kaldırıldı -->

                        <!-- Fatura Tipi - Gizli alan -->
                        {{-- <input type="hidden" name="document_type_id" value="1"> --}}

                        <!-- Aktif/Pasif -->
                        <div class="col-md-6">
                            <label class="form-label">Aktif mi?</label>
                            <select class="form-control" name="is_active">
                                <option value="1">Aktif</option>
                                <option value="0">Pasif</option>
                            </select>
                        </div>
                    </div>

                    <hr class="my-4">

                    <!-- KDV Seçeneği Kaldırıldı - Artık her satır için ayrı switch kullanılıyor -->

                    <!-- Döviz Seçeneği -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label class="form-label">Para Birimi</label>
                            <select class="form-control" id="currency-code" name="currency">
                                <option value="TRY" {{ old('currency', $item->currency ?? 'TRY') == 'TRY' ? 'selected' : '' }}>Türk Lirası (₺)</option>
                                <option value="USD" {{ old('currency', $item->currency ?? '') == 'USD' ? 'selected' : '' }}>Amerikan Doları (USD)</option>
                                <option value="EUR" {{ old('currency', $item->currency ?? '') == 'EUR' ? 'selected' : '' }}>Euro (EUR)</option>
                                <option value="GBP" {{ old('currency', $item->currency ?? '') == 'GBP' ? 'selected' : '' }}>İngiliz Sterlini (GBP)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Döviz Kuru</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="exchange-rate" name="exchange_rate" value="{{ old('exchange_rate', $item->exchange_rate ?? '1.0000') }}">
                                <button class="btn btn-outline-secondary" type="button" id="get-exchange-rate">Güncel Kur</button>
                            </div>
                        </div>
                    </div>

                    <!-- Hesaplamalar -->
                    <div class="row">
                        <div class="col-md-3">
                            <label>KDV Hariç Tutar</label>
                            <input type="text" name="net_amount" id="net-amount" class="form-control" readonly>
                        </div>
                        <div class="col-md-3">
                            <label>KDV Tutarı</label>
                            <input type="text" name="tax_amount" id="tax-amount" class="form-control" readonly>
                        </div>
                        <div class="col-md-3">
                            <label>KDV Dahil Toplam</label>
                            <input type="text" name="total_amount" id="general-total" class="form-control" readonly>
                        </div>
                        <div class="col-md-3">
                            <label>Döviz Tutarı</label>
                            <div class="input-group">
                                <input type="text" id="foreign-currency-amount" class="form-control" readonly>
                                <span class="input-group-text" id="currency-symbol">TL</span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Kaydet</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Cari Seçim Modalı -->
<div class="modal fade" id="currentSelectModal" tabindex="-1" aria-labelledby="currentSelectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="currentSelectModalLabel">Cari Seçimi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="current-search" placeholder="Cari ara...">
                </div>
                <div class="table-responsive">
                    <table class="table bordered-table mb-0 dataTable" id="current-table">
                        <thead>
                            <tr>
                                <th>Cari Adı</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($current as $cr)
                            <tr>
                                <td>{{ $cr->name }}</td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-primary select-current" data-id="{{ $cr->id }}" data-name="{{ $cr->name }}">Seç</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<!-- Ürün Seçim Modalı -->
<div class="modal fade" id="productSelectModal" tabindex="-1" aria-labelledby="productSelectModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productSelectModalLabel">Ürün Seçimi</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="product-search" placeholder="Ürün ara...">
                </div>
                <div class="table-responsive">
                    <table class="table bordered-table mb-0 dataTable" id="product-table">
                        <thead>
                            <tr>
                                <th>Ürün Adı</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($products as $pr)
                            <tr>
                                <td>{{ $pr->name }}</td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-primary select-product" data-id="{{ $pr->id }}" data-name="{{ $pr->name }}" data-price="{{ $pr->sale_price }}" data-vat="{{ $pr->default_vat_rate }}">Seç</button>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script>
// Fatura numarasını sunucudan almak için fonksiyon
async function getInvoiceNumber() {
    const invoiceNoField = document.getElementById('invoice_no');
    const hiddenInvoiceNoField = document.querySelector('input[name="invoice_no_hidden"]');

    if (!invoiceNoField) {
        console.error('Fatura numarası alanı formda bulunamadı');
        return false;
    }

    // Eğer fatura numarası zaten varsa, işlem yapma
    if (invoiceNoField.value) {
        console.log('Mevcut fatura numarası:', invoiceNoField.value);
        return true;
    }

    console.log('Fatura numarası boş, sunucudan yeni numara alınıyor...');

    try {
        // AJAX ile sunucudan bir sonraki fatura numarasını al
        // URL'yi tam olarak belirt
        const response = await fetch('/admin/invoice/next-invoice-number');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Sunucudan alınan fatura numarası:', data.invoice_no);

        if (!data.invoice_no) {
            throw new Error('Sunucudan geçerli bir fatura numarası alınamadı');
        }

        // Fatura numarasını güncelle
        invoiceNoField.value = data.invoice_no;

        // Hidden field'i güncelle
        if (hiddenInvoiceNoField) {
            hiddenInvoiceNoField.value = data.invoice_no;
        }

        return true;
    } catch (error) {
        console.error('Fatura numarası alınırken hata oluştu:', error);
        alert('Fatura numarası alınırken bir hata oluştu. Lütfen sayfayı yenileyip tekrar deneyin.');
        return false;
    }
}

// Initialize form when DOM is loaded
window.addEventListener('DOMContentLoaded', async function() {
    // Count existing rows to set the initial rowCount
    const existingRows = document.querySelectorAll('#invoice-table tbody tr').length;
    window.rowCount = existingRows > 0 ? existingRows : 1;

    // Calculate totals on page load
    updateTotals();

    // Fatura numarasını al
    await getInvoiceNumber();
});

// KDV Hariç Fiyattan KDV Dahil Fiyat Hesaplama
function calculatePriceWithVAT(priceWithoutVAT, vatRate) {
    return priceWithoutVAT * (1 + (vatRate / 100));
}

// KDV Dahil Fiyattan KDV Hariç Fiyat Hesaplama
function calculatePriceWithoutVAT(priceWithVAT, vatRate) {
    return priceWithVAT / (1 + (vatRate / 100));
}

// KDV Tutarı Hesaplama
function calculateVATAmount(priceWithoutVAT, vatRate) {
    return priceWithoutVAT * (vatRate / 100);
}

// Döviz sembollerini tanımla
const currencySymbols = {
    'TRY': '₺',
    'USD': '$',
    'EUR': '€',
    'GBP': '£'
};

// Döviz kurunu güncelle
function updateExchangeRate() {
    const currencyCode = document.getElementById('currency-code').value;

    // TRY seçiliyse kur 1.0000 olmalı
    if (currencyCode === 'TRY') {
        document.getElementById('exchange-rate').value = '1.0000';
        document.getElementById('currency-symbol').textContent = currencySymbols[currencyCode];
        updateTotals();
        return;
    }

    // API'den güncel kuru al
    fetch(`/admin/get-exchange-rate/${currencyCode}`)
        .then(response => response.json())
        .then(data => {
            console.log('Döviz kuru yanıtı:', data); // Hata ayıklama
            if (data && data.rate) {
                document.getElementById('exchange-rate').value = data.rate.toFixed(4);
                document.getElementById('currency-symbol').textContent = currencySymbols[currencyCode];
                updateTotals();
            } else {
                console.error('Döviz kuru verisi bulunamadı:', data);
                // Varsayılan değerler
                const defaultRates = {
                    'USD': 33.0,
                    'EUR': 35.0,
                    'GBP': 42.0,
                    'TRY': 1.0
                };
                document.getElementById('exchange-rate').value = defaultRates[currencyCode].toFixed(4);
                document.getElementById('currency-symbol').textContent = currencySymbols[currencyCode];
                updateTotals();
            }
        })
        .catch(error => {
            console.error('Döviz kuru alınırken hata oluştu:', error);
            // Hata durumunda varsayılan değerler
            const defaultRates = {
                'USD': 33.0,
                'EUR': 35.0,
                'GBP': 42.0,
                'TRY': 1.0
            };
            document.getElementById('exchange-rate').value = defaultRates[currencyCode].toFixed(4);
            document.getElementById('currency-symbol').textContent = currencySymbols[currencyCode];
            updateTotals();
        });
}

// TL'den dövize çevir
function convertFromTRY(amount) {
    const exchangeRate = parseFloat(document.getElementById('exchange-rate').value || 1);
    return amount / exchangeRate;
}

function updateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity').value) || 0;
    const salePrice = parseFloat(row.querySelector('.sale-price').value) || 0;
    const vatRate = parseFloat(row.querySelector('.vat-rate').value) || 0;
    const vatIncludedSwitch = row.querySelector('.vat-included-switch').value === '1';

    let rowPriceWithoutVAT, rowPriceWithVAT;

    if (vatIncludedSwitch) {
        // KDV dahil fiyat hesaplama
        rowPriceWithVAT = salePrice;
        rowPriceWithoutVAT = calculatePriceWithoutVAT(salePrice, vatRate);
    } else {
        // KDV hariç fiyat hesaplama
        rowPriceWithoutVAT = salePrice;
        rowPriceWithVAT = calculatePriceWithVAT(salePrice, vatRate);
    }

    const rowTotalWithoutVAT = quantity * rowPriceWithoutVAT;
    const rowTotalWithVAT = quantity * rowPriceWithVAT;
    const rowVAT = rowTotalWithVAT - rowTotalWithoutVAT;

    row.querySelector('.total').value = rowTotalWithVAT.toFixed(2);

    // KDV tutarını hidden input'a kaydet
    const rowIndex = row.querySelector('.vat-included-switch').getAttribute('data-row-index');
    let vatAmountInput = row.querySelector(`input[name="items[${rowIndex}][vat_amount]"]`);

    // Eğer input yoksa oluştur
    if (!vatAmountInput) {
        vatAmountInput = document.createElement('input');
        vatAmountInput.type = 'hidden';
        vatAmountInput.name = `items[${rowIndex}][vat_amount]`;
        vatAmountInput.className = 'vat-amount';
        row.appendChild(vatAmountInput);
    }

    vatAmountInput.value = rowVAT.toFixed(2);

    return {
        withoutVAT: rowTotalWithoutVAT,
        vat: rowVAT,
        withVAT: rowTotalWithVAT
    };
}

function updateTotals() {
    const currencyCode = document.getElementById('currency-code').value;

    let totalWithoutVAT = 0;
    let totalVAT = 0;
    let totalWithVAT = 0;

    document.querySelectorAll('#invoice-table tbody tr').forEach(row => {
        const rowTotals = updateRowTotal(row);
        totalWithoutVAT += rowTotals.withoutVAT;
        totalVAT += rowTotals.vat;
        totalWithVAT += rowTotals.withVAT;
    });

    // TL tutarları güncelle
    document.getElementById('net-amount').value = totalWithoutVAT.toFixed(2);
    document.getElementById('tax-amount').value = totalVAT.toFixed(2);
    document.getElementById('general-total').value = totalWithVAT.toFixed(2);

    // Döviz tutarını güncelle
    const foreignAmount = convertFromTRY(totalWithVAT);
    document.getElementById('foreign-currency-amount').value = foreignAmount.toFixed(2);
}

document.getElementById('add-row').addEventListener('click', () => {
    const tbody = document.querySelector('#invoice-table tbody');
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <div class="input-group">
                <input type="text" class="form-control current-name" readonly placeholder="Cari seçiniz" value="${document.querySelector('.current-name').value}">
                <input type="hidden" class="current-id" value="${document.querySelector('.current-id').value}" required>
                <button class="btn btn-outline-secondary btn-select-current" type="button">
                    <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                </button>
            </div>
        </td>
        <td>
            <div class="input-group">
                <input type="text" class="form-control product-name" readonly placeholder="Ürün seçiniz">
                <input type="hidden" name="items[${window.rowCount}][product_id]" class="product-id" value="" required>
                <button class="btn btn-outline-secondary btn-select-product" type="button">
                    <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                </button>
            </div>
        </td>
        <td><input type="number" name="items[${window.rowCount}][quantity]" class="form-control quantity" value="1" min="1" required></td>
        <td><input type="text" name="items[${window.rowCount}][sale_price]" class="form-control sale-price" value="0" required></td>
        <td>
            <input type="text" name="items[${window.rowCount}][default_vat_rate]" class="form-control vat-rate" value="18" required>
            <input type="hidden" class="vat-included-switch" value="0" data-row-index="${window.rowCount}">
            <input type="hidden" name="items[${window.rowCount}][vat_amount]" class="vat-amount" value="0">
        </td>
        <td><input type="text" name="items[${window.rowCount}][total]" class="form-control total" value="0" readonly></td>
        <td><button type="button" class="btn btn-danger btn-remove-row">Sil</button></td>
    `;
    tbody.appendChild(newRow);
    window.rowCount++;
});

// Cari seçim modalını aç
document.addEventListener('click', function(e) {
    if (e.target.closest('.btn-select-current')) {
        const row = e.target.closest('tr');
        window.currentCurrentRow = row;
        $('#currentSelectModal').modal('show');
    }
});

// Cari seçildiğinde
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('select-current')) {
        const currentId = e.target.getAttribute('data-id');
        const currentName = e.target.getAttribute('data-name');

        // Ana current_id alanını güncelle
        document.getElementById('current_id').value = currentId;

        // Tüm current-id sınıfına sahip alanları güncelle
        document.querySelectorAll('.current-id').forEach(function(input) {
            input.value = currentId;
        });

        // Tüm current-name sınıfına sahip alanları güncelle
        document.querySelectorAll('.current-name').forEach(function(input) {
            input.value = currentName;
        });

        $('#currentSelectModal').modal('hide');
    }
});

// Cari arama
document.addEventListener('keyup', function(e) {
    if (e.target.id === 'current-search') {
        const value = e.target.value.toLowerCase();
        const rows = document.querySelectorAll('#current-table tbody tr');

        rows.forEach(function(row) {
            const text = row.textContent.toLowerCase();
            row.style.display = text.indexOf(value) > -1 ? '' : 'none';
        });
    }
});

// Ürün seçim butonlarına tıklandığında
document.addEventListener('click', function(e) {
    if (e.target.closest('.btn-select-product')) {
        const row = e.target.closest('tr');
        window.currentProductRow = row;
        $('#productSelectModal').modal('show');
    }
});

// Ürün seçildiğinde
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('select-product')) {
        if (!window.currentProductRow) return;

        const productId = e.target.getAttribute('data-id');
        const productName = e.target.getAttribute('data-name');
        const productPrice = e.target.getAttribute('data-price');
        const productVat = e.target.getAttribute('data-vat');

        window.currentProductRow.querySelector('.product-id').value = productId;
        window.currentProductRow.querySelector('.product-name').value = productName;
        window.currentProductRow.querySelector('.sale-price').value = parseFloat(productPrice).toFixed(2);
        window.currentProductRow.querySelector('.vat-rate').value = parseFloat(productVat).toFixed(2);

        $('#productSelectModal').modal('hide');
        updateTotals();
    }
});

// Ürün arama
document.addEventListener('keyup', function(e) {
    if (e.target.id === 'product-search') {
        const value = e.target.value.toLowerCase();
        const rows = document.querySelectorAll('#product-table tbody tr');

        rows.forEach(function(row) {
            const text = row.textContent.toLowerCase();
            row.style.display = text.indexOf(value) > -1 ? '' : 'none';
        });
    }
});

// Global KDV dahil/hariç switch değiştiğinde
document.querySelector('.global-vat-switch').addEventListener('change', function() {
    const isVatIncluded = this.checked;

    // Tüm satırlardaki vat-included-switch değerlerini güncelle
    document.querySelectorAll('.vat-included-switch').forEach(function(switchInput) {
        switchInput.value = isVatIncluded ? '1' : '0';
    });

    // Tüm satırları güncelle
    updateTotals();
});

document.addEventListener('change', function (e) {
    if (e.target.classList.contains('quantity') || e.target.classList.contains('sale-price') || e.target.classList.contains('vat-rate')) {
        updateTotals();
    }

    // Döviz türü değiştiğinde
    if (e.target.id === 'currency-code') {
        updateExchangeRate();
    }

    // Döviz kuru değiştiğinde
    if (e.target.id === 'exchange-rate') {
        updateTotals();
    }
});

// Güncel kur butonuna tıklandığında
document.getElementById('get-exchange-rate').addEventListener('click', function() {
    updateExchangeRate();
});

// Sayfa yüklenirken döviz sembolünü ayarla
document.addEventListener('DOMContentLoaded', function() {
    const currencyCode = document.getElementById('currency-code').value;
    document.getElementById('currency-symbol').textContent = currencySymbols[currencyCode] || currencySymbols['TRY'];
});

document.addEventListener('click', function (e) {
    if (e.target.classList.contains('btn-remove-row')) {
        e.target.closest('tr').remove();
        updateTotals();
    }
});

// Ensure invoice number is set before form submission
document.querySelector('form').addEventListener('submit', async function(e) {
    e.preventDefault(); // Önce formu durdur

    const invoiceNoField = document.getElementById('invoice_no');

    // Fatura numarası yoksa, sunucudan almaya çalış
    if (!invoiceNoField.value) {
        const success = await getInvoiceNumber();
        if (!success) {
            alert('Fatura numarası alınamadı! Lütfen sayfayı yenileyip tekrar deneyin.');
            console.error('Fatura numarası alınamadı, form gönderimi engellendi');
            return false;
        }
    }

    // Son bir kontrol daha yap
    if (!invoiceNoField.value) {
        alert('Fatura numarası boş olamaz! Lütfen sayfayı yenileyip tekrar deneyin.');
        console.error('Fatura numarası hala boş, form gönderimi engellendi');
        return false;
    }

    invoiceNoField.readOnly = false;

    console.log('Veritabanına kaydedilecek fatura numarası:', invoiceNoField.value);

    // Formu gönder
    this.submit();
});
</script>
@endsection
