@extends('layout.layout')

@php
    $title = $container->title ?? 'Faturalar';
    $subTitle = $title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.invoices_export_excel') }}"
                    class="btn btn-success btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    <iconify-icon icon="mdi:microsoft-excel" class="menu-icon me-1"></iconify-icon>
                    Excel
                </a>
                <a href="{{ route('backend.invoices_form') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    Ekle
                </a>
            </div>
        </div>

        <div class="card-body">
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">ID</th>
                            <th scope="col" class="text-center">Fatura No</th>
                            <th scope="col" class="text-center">Cari Adı</th>
                            <th scope="col" class="text-center">Fatura Tarihi</th>
                            <th scope="col" class="text-center">Aktif/Pasif</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        // Fatura yazdırma fonksiyonu
        function printInvoice(id) {
            // Yeni bir pencere aç
            var printWindow = window.open('', '_blank');

            // Yükleniyor mesajı göster
            printWindow.document.write(
                '<!DOCTYPE html><html lang="tr"><head><meta charset="UTF-8"><title>Fatura Yazdırılıyor...</title></head><body>'
                );
            printWindow.document.write('<div style="text-align:center; margin-top:100px;">');
            printWindow.document.write('<h2>Fatura yükleniyor, lütfen bekleyin...</h2>');
            printWindow.document.write(
                '<div style="width:50px; height:50px; border:5px solid #f3f3f3; border-top:5px solid #3498db; border-radius:50%; margin:20px auto; animation:spin 2s linear infinite;"></div>'
                );
            printWindow.document.write(
                '<style>@keyframes spin {0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); }}</style>');
            printWindow.document.write('</div></body></html>');
            printWindow.document.close();

            // AJAX isteği gönder
            $.ajax({
                url: '/admin/invoice/export-pdf/' + id,
                type: 'GET',
                success: function(response) {
                    // Yanıtı yeni pencereye yaz
                    printWindow.document.open();
                    printWindow.document.write(response);
                    printWindow.document.close();
                },
                error: function(xhr, status, error) {
                    // Hata mesajını göster
                    printWindow.document.open();
                    printWindow.document.write(
                        '<!DOCTYPE html><html lang="tr"><head><meta charset="UTF-8"><title>Hata</title></head><body>'
                        );
                    printWindow.document.write('<div style="text-align:center; margin-top:100px; color:red;">');
                    printWindow.document.write('<h2>Fatura yüklenirken bir hata oluştu</h2>');
                    printWindow.document.write('<p>' + (xhr.responseJSON ? xhr.responseJSON.error : error) +
                        '</p>');
                    printWindow.document.write(
                        '<button onclick="window.close()" style="padding: 10px 20px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 20px;">Kapat</button>'
                        );
                    printWindow.document.write('</div></body></html>');
                    printWindow.document.close();
                }
            });
        }

        $(document).ready(function() {
            BaseCRUD.selector = "[datatable]";

            // Modal açıldığında ürün detaylarını ve ödeme bilgilerini doldur
            $(document).on('shown.bs.modal', '[id^="detailModal"]', function() {
                const modalId = $(this).attr('id');
                const rowId = modalId.replace('detailModal', '');
                const currentStatus = $(this).find('.update-status-btn').data('current-status');

                // Durum selectbox'ını güncelle
                const statusSelect = $(`#invoice-status-${rowId}`);
                statusSelect.val(currentStatus);

                // Ödeme bilgilerini doldur
                try {
                    const paymentInfoStr = $(`[data-payment-info="${rowId}"]`).val();
                    if (paymentInfoStr) {
                        const paymentInfo = JSON.parse(paymentInfoStr);
                        $(`#total-amount-${rowId}`).text(paymentInfo.total_amount_formatted);
                        $(`#total-paid-${rowId}`).text(paymentInfo.total_paid_formatted);
                        $(`#remaining-amount-${rowId}`).text(paymentInfo.remaining_amount_formatted);
                    }
                } catch (e) {
                    console.error('Error parsing payment info:', e);
                }
            });

            var table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.invoices_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        var cfilter = {};
                        return $.extend({}, d, {
                            "cfilter": cfilter
                        });
                    }
                },
                columns: [{
                        data: 'id',
                        name: 'id',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_no',
                        name: 'invoice_no',
                        className: 'text-center'
                    },
                    {
                        data: 'current_name',
                        name: 'current_name',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_date',
                        name: 'invoice_date',
                        className: 'text-center'
                    },
                    {
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    },
                    {
                        data: 'status',
                        name: 'status',
                        className: 'text-center',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            // Eğer status null ise boş string döndür
                            if (row.status === null) {
                                return '';
                            }
                            // Controller'dan gelen renkli HTML badge'i kullan
                            return row.status;
                        }
                    },
                    {
                        render: function(data, type, row) {
                            const rowId = row.id || '';
                            return `
                <td class="text-center">
                    <div class="d-flex align-items-center gap-10 justify-content-center">
                        <button type="button" class="bg-primary-focus text-primary-600 bg-hover-primary-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" data-bs-toggle="modal" data-bs-target="#detailModal${rowId}">
                            <iconify-icon icon="mdi:eye" class="menu-icon"></iconify-icon>
                        </button>
                        <a href="{{ route('backend.invoices_form') }}/${rowId}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                            <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                        </a>
                        <a href="javascript:void(0)" onclick="printInvoice('${rowId}')" class="bg-info-focus text-info-600 bg-hover-info-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                            <iconify-icon icon="mdi:file-pdf" class="menu-icon"></iconify-icon>
                        </a>
                        <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${rowId}">
                            <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                        </button>
                    </div>

                    <!-- Detay Modalı -->
                    <div class="modal fade" id="detailModal${rowId}" tabindex="-1" aria-labelledby="detailModalLabel${rowId}" aria-hidden="true">
                        <div class="modal-dialog modal-xl">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="detailModalLabel${rowId}">Fatura Detayı</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
                                </div>
                                <div class="modal-body">
                                    <input type="hidden" id="items-data-${rowId}" value='${row.items_data || '[]'}'>
                                    <input type="hidden" data-payment-info="${rowId}" value='${row.payment_info || '{}'}'>
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <p><strong>Fatura No:</strong> ${row.invoice_no || '-'}</p>
                                            <p><strong>Fatura Tarihi:</strong> ${row.invoice_date || '-'}</p>
                                            <p><strong>Cari Hesap:</strong> ${row.current_name || '-'}</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Net Tutar:</strong> ${row.net_amount || '-'}</p>
                                            <p><strong>KDV Tutarı:</strong> ${row.tax_amount || '-'}</p>
                                            <p><strong>Toplam Tutar:</strong> ${row.total_amount || '-'}</p>
                                            <p><strong>Aktif/Pasif:</strong> ${row.is_active || '-'}</p>
                                            <p><strong>Ödeme Durumu:</strong> ${row.payment_status || '-'}</p>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="card-title">Durum Güncelleme</h6>
                                                    ${(row.status === 2 || row.status === 3) ?
                                                    `<div class="alert alert-warning">
                                                            Bu fatura ${row.status === 2 ? 'kısmi muhasebelendirilmiş' : 'muhasebelendirilmiş'} durumda olduğu için durumu değiştirilemez.
                                                        </div>` :
                                                    `<div class="row">
                                                            <div class="col-md-8">
                                                                <select id="invoice-status-${rowId}" name="invoice_status" class="form-select">
                                                                    <option value="">Durum Seçiniz</option>
                                                                    @foreach (\App\Models\InvoiceStatus::where('is_active', 1)->get() as $status)
                                                                        <option value="{{ $status->id }}" data-id="{{ $status->id }}">{{ $status->name }}</option>
                                                                    @endforeach
                                                                </select>
                                                            </div>
                                                            <div class="col-md-4">
                                                                <button type="button" class="btn btn-primary update-status-btn" data-id="${rowId}">Güncelle</button>
                                                            </div>
                                                        </div>`}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mb-4">
                                        <div class="col-12">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="card-title">Ödeme Bilgileri</h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <p><strong>Fatura Tutarı:</strong> <span id="total-amount-${rowId}"></span></p>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <p><strong>Tahsil Edilen:</strong> <span id="total-paid-${rowId}"></span></p>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <p><strong>Kalan Tutar:</strong> <span id="remaining-amount-${rowId}"></span></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                                </div>
                            </div>
                        </div>
                    </div>


                </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    }
                ],
                order: [
                    [0, 'desc']
                ], // invoice_no'ya göre sırala
                pageLength: 15,
            });

            // Silme işlemi için özel fonksiyon
            $(document).on("click", "[row-delete]", function() {
                var invoiceId = $(this).attr('row-delete');
                var row = $(this).closest('tr');

                console.log("Satış faturası silme işlemi başlatıldı. ID:", invoiceId);

                Swal.fire({
                    icon: "error",
                    title: "",
                    html: "Bu fatura kalıcı olarak silinecektir.<br>İşlemi onaylıyor musunuz?",
                    showCancelButton: true,
                    cancelButtonText: "İptal",
                    confirmButtonText: "Sil",
                }).then(function(action) {
                    if (action.value) {
                        // Silme işlemi başlamadan önce satırı gizle
                        row.css('background-color', '#ffcccc').fadeOut(500);

                        // CSRF token'ı al
                        var token = $('meta[name="csrf-token"]').attr('content');
                        console.log("CSRF Token:", token ? "Mevcut" : "Eksik");

                        $.ajax({
                            url: "{{ route('backend.invoices_delete') }}",
                            type: "POST",
                            data: {
                                _token: token,
                                id: invoiceId
                            },
                            headers: {
                                'X-CSRF-TOKEN': token
                            },
                            success: function(response) {
                                console.log("Silme işlemi yanıtı:", response);

                                if (response.success || response.status) {
                                    // Başarı mesajı göster
                                    toastr.success(response.message ||
                                        'Fatura başarıyla silindi');

                                    // DataTable'ı yenile
                                    var table = $('[datatable]').DataTable();
                                    if (table) {
                                        console.log("DataTable yenileniyor...");
                                        table.ajax.reload(null, false);
                                    } else {
                                        console.log("DataTable bulunamadı!");
                                        // Sayfa yenileme alternatifi
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 1000);
                                    }
                                } else {
                                    // Hata durumunda satırı geri göster
                                    row.css('background-color', '').fadeIn(500);
                                    Swal.fire('', response.message ||
                                        'İşlem sırasında bir hata oluştu', "warning"
                                        );
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Silme işlemi hatası:", error);
                                console.error("Yanıt:", xhr.responseText);

                                // Hata durumunda satırı geri göster
                                row.css('background-color', '').fadeIn(500);
                                Swal.fire('', 'İşlem sırasında bir hata oluştu: ' +
                                    error, "error");
                            }
                        });
                    }
                });
            });

            // Tekil durum güncelleme
            $(document).on('click', '.update-status-btn', function() {
                // Fatura id'sini satırdan al
                const invoiceId = $(this).data('id');
                const currentStatus = $(this).data('current-status');
                console.log('Güncellenecek fatura id:', invoiceId);
                // Muhasebelendirilmiş veya kısmi muhasebelendirilmiş faturaların durumu değiştirilemez
                if (currentStatus == 2 || currentStatus == 3) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Durum Değiştirilemez',
                        text: 'Muhasebelendirilmiş veya kısmi muhasebelendirilmiş faturaların durumu değiştirilemez.',
                        confirmButtonText: 'Tamam'
                    });
                    return;
                }

                // Seçilen durumu al
                const status = $(`#invoice-status-${invoiceId}`).val();
                console.log('Seçilen yeni durum:', status);

                if (!status || status === '') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Lütfen bir durum seçin',
                        confirmButtonText: 'Tamam'
                    });
                    return;
                }

                let statusText = $(`#invoice-status-${invoiceId} option:selected`).text();

                Swal.fire({
                    icon: 'question',
                    title: 'Onay',
                    text: `Bu faturayı ${statusText} durumuna güncellemek istediğinize emin misiniz?`,
                    showCancelButton: true,
                    confirmButtonText: 'Evet',
                    cancelButtonText: 'Hayır'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('backend.invoices_bulk_approve') }}",
                            type: 'POST',
                            data: {
                                ids: [invoiceId], // Sadece bu faturanın ID'si
                                status: status,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                console.log('Durum güncelleme yanıtı:', response);
                                if (response.success) {
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Başarılı',
                                        text: response.message ||
                                            'Fatura durumu başarıyla güncellendi.',
                                        confirmButtonText: 'Tamam'
                                    }).then(() => {
                                        // Modalı kapat
                                        $(`#detailModal${invoiceId}`).modal(
                                            'hide');
                                        // DataTable'ı güncelle
                                        var table = $('[datatable]')
                                    .DataTable();
                                        if (table) {
                                            table.ajax.reload(null, false);
                                        } else {
                                            window.location
                                        .reload(); // Alternatif olarak sayfayı yenile
                                        }
                                    });
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Hata',
                                        text: response.message ||
                                            'Durum güncellenirken bir hata oluştu',
                                        confirmButtonText: 'Tamam'
                                    });
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Durum güncelleme hatası:", error);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Hata',
                                    text: 'Bir hata oluştu: ' + (xhr
                                        .responseJSON ? xhr.responseJSON
                                        .message : error),
                                    confirmButtonText: 'Tamam'
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
@endsection
