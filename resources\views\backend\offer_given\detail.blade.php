@extends('layout.layout')

@section('content')
<style>
    .status-row td {
        border-color: rgba(0, 0, 0, 0.1) !important;
    }
    .form-check-input {
        opacity: 1 !important;
        background-color: #fff !important;
        border-color: #aaa !important;
    }
    tr[style] td {
        background-color: inherit !important;
        color: inherit !important;
    }
    .info-card {
        height: 100%;
    }
    .address-wrapper {
        max-width: 100%;
        word-wrap: break-word;
        display: inline;
    }
</style>
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $subTitle }}
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="{{ route('backend.' . $container->page . '_list') }}"
                            class="btn btn-secondary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center">
                            <iconify-icon icon="lucide:arrow-left" class="me-1"></iconify-icon> Listeye Dön
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card info-card">
                                <div class="card-body">
                                    <h6 class="mb-3">Teklif Bilgileri</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <strong>Teklif No:</strong> {{ $item->offer_number }}
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Teklif Tarihi:</strong> {{ \Carbon\Carbon::parse($item->offer_date)->format('d.m.Y') }}
                                        </div>
                                        {{-- <div class="col-md-6 mb-2">
                                            <strong>Şube:</strong> {{ $item->branch->title ?? '-' }}
                                        </div> --}}
                                        <div class="col-md-6 mb-2">
                                            <strong>Depo:</strong> {{ $item->warehouse->name ?? '-' }}
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Teklif Son Tarihi:</strong> {{ $item->offer_deadline ? \Carbon\Carbon::parse($item->offer_deadline)->format('d.m.Y') : '-' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card info-card">
                                <div class="card-body">
                                    <h6 class="mb-3">Cari Bilgileri</h6>
                                    <div class="row">
                                        {{-- <div class="col-md-6 mb-2">
                                            <strong>Cari Kodu:</strong> {{ $item->current->current_code ?? '-' }}
                                        </div> --}}
                                        <div class="col-md-6 mb-2">
                                            <strong>Cari Adı:</strong> {{ $item->current->name ?? '-' }} {{ $item->current->surname ?? '' }}
                                            @if($item->current && $item->current->deleted_at)
                                                <span class="badge bg-danger">Silinmiş</span>
                                            @endif
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Telefon:</strong> {{ $item->current->phone ?? '-' }}
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <strong>Adres:</strong>
                                            <span class="address-wrapper">@if(!empty($item->shipping_address)){{ $item->shipping_address }}@else{{ $item->current->address  ?? '-' }} {{ $item->current->city->name ?? '-' }} {{ $item->current->country->name ?? '-' }}@endif</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <h6 class="mb-3">Teklif Ürünleri</h6>
                            <div class="table-responsive">
                                <table class="table vertical-striped-table mb-0">
                                    <thead>
                                        <tr>
                                            <th class="text-center" style="width: 40px;">
                                                <div class="form-check style-check d-flex align-items-center">
                                                    <input type="checkbox" class="form-check-input" id="selectAllProducts">
                                                </div>
                                            </th>
                                            <th class="text-center">Sıra No</th>
                                            <th class="text-center">Ürün Kodu</th>
                                            <th class="text-center">Ürün Adı</th>
                                            <th class="text-center">Miktar</th>
                                            <th class="text-center">Birim</th>
                                            <th class="text-center">Birim Fiyat</th>
                                            <th class="text-center">KDV</th>
                                            <th class="text-center">Toplam</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($item->offerProducts as $product)
                                            @php
                                                $rowStyle = '';

                                                switch($product->status) {
                                                    case 0: // Beklemede
                                                        $rowStyle = 'background-color: #FFC02D6B !important; color: #000 !important;';
                                                        break;
                                                    case 1: // Onaylandı
                                                        $rowStyle = 'background-color: #45b36966 !important; color: #000 !important;';
                                                        break;
                                                    case 2: // Reddedildi
                                                        $rowStyle = 'background-color: #ef47704a !important; color: #000 !important;';
                                                        break;
                                                }
                                            @endphp
                                            <tr class="status-row">
                                                <td class="text-center" style="{{ $rowStyle }}">
                                                    <div class="form-check style-check d-flex align-items-center">
                                                        <input type="checkbox" class="form-check-input product-checkbox" value="{{ $product->id }}" {{ $product->status == 1 ? 'disabled' : '' }}>
                                                    </div>
                                                </td>
                                                <td class="text-center" style="{{ $rowStyle }}">{{ $product->item_no ?? '-' }}</td>
                                                <td class="text-center" style="{{ $rowStyle }}">{{ $product->product_code }}</td>
                                                <td class="text-center" style="{{ $rowStyle }}">{{ $product->product_name }}</td>
                                                <td class="text-center" style="{{ $rowStyle }}">{{ $product->quantity }}</td>
                                                <td class="text-center" style="{{ $rowStyle }}">{{ $product->unit }}</td>
                                                <td class="text-center" style="{{ $rowStyle }}">
                                                    {{ number_format($product->unit_price, 2, ',', '.') }} {{ $product->currency_type }}
                                                </td>
                                                <td class="text-center" style="{{ $rowStyle }}">
                                                    %{{ $product->vat_rate }} ({{ $product->vat_status == 0 ? 'Hariç' : 'Dahil' }})
                                                </td>
                                                <td class="text-center" style="{{ $rowStyle }}">
                                                    {{ number_format($product->total_price, 2, ',', '.') }} {{ $product->currency_type }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                    <tbody>
                                        <tr>
                                            <td colspan="8" class="text-end"><strong>Ara Toplam:</strong></td>
                                            <td class="text-center">
                                                {{ number_format($item->total_price, 2, ',', '.') }} {{ $item->exchangeRate->currency_code ?? 'TRY' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="8" class="text-end"><strong>KDV:</strong></td>
                                            <td class="text-center">
                                                {{ number_format($item->vat_amount, 2, ',', '.') }} {{ $item->exchangeRate->currency_code ?? 'TRY' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="8" class="text-end"><strong>Genel Toplam:</strong></td>
                                            <td class="text-center">
                                                {{ number_format($item->total_amount, 2, ',', '.') }} {{ $item->exchangeRate->currency_code ?? 'TRY' }}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="text-end mt-3">
                        <button type="button" class="btn btn-primary" id="updateStatusBtn" disabled>
                            Seçili Ürünlerin Durumunu Güncelle
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="statusUpdateModal" tabindex="-1" aria-labelledby="statusUpdateModalLabel" aria-modal="true" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusUpdateModalLabel">Durum Güncelleme</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="statusSelect" class="form-label">Yeni Durum</label>
                        <div class="d-flex flex-column gap-2">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusPending" value="0">
                                <label class="form-check-label" for="statusPending">
                                    <span class="bg-warning-focus text-warning-600 border border-warning-main px-24 py-4 radius-4 fw-medium text-sm">Beklemede</span>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusApproved" value="1">
                                <label class="form-check-label" for="statusApproved">
                                    <span class="bg-success-focus text-success-600 border border-success-main px-24 py-4 radius-4 fw-medium text-sm">Onaylandı</span>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="status" id="statusRejected" value="2">
                                <label class="form-check-label" for="statusRejected">
                                    <span class="bg-danger-focus text-danger-600 border border-danger-main px-24 py-4 radius-4 fw-medium text-sm">Reddedildi</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="button" class="btn btn-primary" id="saveStatusBtn">Kaydet</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            $('.status-row').each(function() {
                var style = $(this).attr('style');
                $(this).find('td').attr('style', style);
            });
            $('#selectAllProducts').change(function() {
                $('.product-checkbox').prop('checked', $(this).prop('checked'));
                updateButtonState();
            });
            $('.product-checkbox').change(function() {
                updateButtonState();
                var allChecked = $('.product-checkbox:checked').length === $('.product-checkbox').length;
                $('#selectAllProducts').prop('checked', allChecked);
            });

            function updateButtonState() {
                var anyChecked = $('.product-checkbox:checked').length > 0;
                $('#updateStatusBtn').prop('disabled', !anyChecked);
            }
            $('#updateStatusBtn').click(function() {
                var modal = $('#statusUpdateModal');
                modal.modal('show');
                modal.removeAttr('aria-hidden');
                modal.attr('aria-modal', 'true');
            });
            $('#statusUpdateModal').on('hidden.bs.modal', function() {
                $(this).find('button, input, select').blur();
                $(this).attr('aria-hidden', 'true');
                $(this).attr('aria-modal', 'false');
                $('#updateStatusBtn').focus();
            });
            $('#saveStatusBtn').click(function() {
                var selectedStatus = $('input[name="status"]:checked').val();
                if (!selectedStatus) {
                    Swal.fire({
                        title: 'Uyarı!',
                        text: 'Lütfen bir durum seçin',
                        icon: 'warning',
                        confirmButtonText: 'Tamam',
                        willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                        willClose: function() { $('body').attr('aria-hidden', 'false'); }
                    });
                    return;
                }
                sendStatusUpdate(selectedStatus);
            });
            function sendStatusUpdate(selectedStatus) {
                var selectedProducts = [];
                $('.product-checkbox:checked').each(function() {
                    selectedProducts.push($(this).val());
                });
                $.ajax({
                    url: '{{ route("backend.offer_given_update_status") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        product_ids: selectedProducts,
                        status: selectedStatus
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#statusUpdateModal').modal('hide');
                            Swal.fire({
                                title: 'Başarılı!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'Tamam',
                                willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                                willClose: function() { $('body').attr('aria-hidden', 'false'); }
                            }).then((result) => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Hata!',
                                text: 'Durum güncellenirken bir hata oluştu: ' + response.message,
                                icon: 'error',
                                confirmButtonText: 'Tamam',
                                willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                                willClose: function() { $('body').attr('aria-hidden', 'false'); }
                            });
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            title: 'Hata!',
                            text: 'Durum güncellenirken bir hata oluştu',
                            icon: 'error',
                            confirmButtonText: 'Tamam',
                            willOpen: function() { $('body').attr('aria-hidden', 'false'); },
                            willClose: function() { $('body').attr('aria-hidden', 'false'); }
                        });
                        console.error(xhr);
                    }
                });
            }
            $('#statusUpdateModal').on('hidden.bs.modal', function() {
                $(this).find('button, input, select').blur();
                $(this).attr('aria-hidden', 'true');
                $(this).attr('aria-modal', 'false');
                $('#updateStatusBtn').focus();
            });
        });
    </script>
@endsection
