@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">
                        {{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.offer_given_save', ['unique' => $item->id]) }}"
                        method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="offer_type_id" value="2">
                        <input type="hidden" id="js_offer_number" value="{{ $item->offer_number }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Teklif Bilgileri</h6>
                                        <div class="row g-3">
                                            {{-- <div class="col-md-6">
                                                <label class="form-label required">Şube</label>
                                                <select class="form-control form-select" name="branch_id" id="branch_id">
                                                    <option value="">Şube Seçiniz...</option>
                                                    @foreach ($branches as $branch)
                                                        <option value="{{ $branch->id }}"
                                                            title="{{ $branch->title }}"
                                                            {{ old('branch_id', $item->branch_id) == $branch->id ? 'selected' : '' }}>
                                                            {{ Str::limit($branch->title, 30) }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <x-form-error field="branch_id" />
                                            </div> --}}
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label required">Depo</label>
                                                <select class="form-control form-select" name="warehouse_id"
                                                    id="warehouse_id" style="background-color: inherit; color: inherit;">
                                                    <option value="">Depo Seçiniz...</option>
                                                    @foreach ($warehouses as $warehouse)
                                                        <option value="{{ $warehouse->id }}"
                                                            title="{{ $warehouse->name }}"
                                                            {{ old('warehouse_id', $item->warehouse_id) == $warehouse->id ? 'selected' : '' }}>
                                                            {{ Str::limit($warehouse->name, 30) }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <x-form-error field="warehouse_id" />
                                            </div>

                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Teklif Numarası</label>
                                                <input type="text" class="form-control" name="offer_number"
                                                    id="offer_number"
                                                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                                                    value="{{ old('offer_number', $item->id ? $item->offer_number : \App\Models\Offer::generateOfferNumber(1)) }}"
                                                    {{ $item->id ? 'readonly' : '' }} placeholder="Otomatik oluşturulacak"
                                                    readonly>
                                            </div>
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Teklif  Tarihi</label>
                                                    <input class="form-control radius-8 bg-base"
                                                        id="offer_date"
                                                        name="offer_date"
                                                        type="date"
                                                        value="{{ old('offer_date', isset($item->offer_date) && !is_null($item->offer_date) ? \Carbon\Carbon::parse($item->offer_date)->format('Y-m-d') : '') }}">
                                                <x-form-error field="offer_date" />
                                            </div>
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Teklif Son Tarihi</label>
                                                    <input class="form-control radius-8 bg-base"
                                                        id="offer_deadline"
                                                        name="offer_deadline"
                                                        type="date"
                                                        value="{{ old('offer_deadline', isset($item->offer_deadline) && !is_null($item->offer_deadline) ? \Carbon\Carbon::parse($item->offer_deadline)->format('Y-m-d') : '') }}">
                                                <x-form-error field="offer_deadline" />
                                            </div>
                                            <div class="col-12 col-lg-12">
                                                <label class="form-label">Döviz</label>
                                                <div class="input-group">
                                                    <select class="form-control form-select" name="exchange_rate_id"
                                                        id="exchange_rate_id">
                                                        <option value="">Döviz Seçiniz...</option>
                                                        @foreach ($exchangeRate as $rate)
                                                            <option value="{{ $rate->id }}"
                                                                data-currency-code="{{ $rate->code }}"
                                                                data-selling-rate="{{ $rate->selling_rate }}"
                                                                data-symbol="{{ $rate->symbol }}"
                                                                {{ old('exchange_rate_id', $item->exchange_rate_id) == $rate->id ? 'selected' : ($rate->code == 'TRY' && !old('exchange_rate_id') && !$item->exchange_rate_id ? 'selected' : '') }}>
                                                                {{ $rate->code }} ({{ $rate->symbol }})
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <input type="text" class="form-control" id="selling_rate"readonly>
                                                    <span class="input-group-text" id="currency_symbol" style="background-color: inherit; color: inherit;"></span>
                                                    <x-form-error field="exchange_rate_id" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Cari Hesap Bilgileri</h6>
                                        <div class="row g-3">
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Cari Kodu-Unvanı</label>
                                                <select class="form-control form-select" name="current_id" id="current_id">
                                                    <option value="">Cari Seçiniz...</option>
                                                    @foreach ($current as $c)
                                                        <option value="{{ $c->id }}"
                                                            data-current-code="{{ $c->name }}{{ $c->surname }}"
                                                            data-address="{{ $c->address ?? '' }}, {{ $c->district->name ?? '' }}, {{ $c->city->name ?? '' }}, {{ $c->country->name ?? '' }}"
                                                            title="{{ $c->name }}{{ $c->surname }} - {{ $c->current_type_id }}"
                                                            {{ old('current_id', $item->current_id) == $c->id ? 'selected' : '' }}
                                                            {{ $c->deleted_at ? 'style=color:red;font-style:italic;' : '' }}>
                                                            {{ $c->name }}{{ $c->surname }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <x-form-error field="current_id" />
                                            </div>
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Cari Adresi</label>
                                                <input type="text" class="form-control" id="address" readonly>
                                            </div>
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Sevkiyat Adres</label>
                                                <input type="text" class="form-control" name="shipping_address"
                                                id="shipping_address" value="{{ old('shipping_address', $item->shipping_address ?? '') }}">
                                                <x-form-error field="shipping_address" />
                                            </div>
                                            <div class="col-12 col-lg-6">
                                                <label class="form-label">Ödeme Planı</label>
                                                <select class="form-control form-select" name="payment_type_id"
                                                    id="payment_type_id">
                                                    <option value="">Ödeme Planı Seçiniz...</option>
                                                    @foreach ($paymentType as $plan)
                                                        <option value="{{ $plan->id }}"
                                                            data-branch-id="{{ $plan->branch_id ?? '' }}"
                                                            {{-- data-branch-id="{{ $plan->branch_id ?? '' }}" --}}
                                                            {{ old('payment_type_id', $item->payment_type_id) == $plan->id ? 'selected' : '' }}>

                                                            {{ $plan->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <x-form-error field="payment_type_id" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Ürünler</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table  basic-border-table" id="productTable">
                                <thead>
                                    <tr>
                                        <th>Sıra No</th>
                                        <th>Kodu</th>
                                        <th>Açıklama</th>
                                        <th>Miktar</th>
                                        <th>Birim</th>
                                        <th>Birim Fiyat</th>
                                        <th>KDV D/H</th>
                                        <th>Döviz Türü</th>
                                        <th>Tutar</th>
                                        <th>KDV %</th>
                                        <th>Sil</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Ürünler buraya eklenecek -->
                                </tbody>
                            </table>
                        </div>
                        <button type="button" id="addProductBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> Ekle
                        </button>
                        <x-form-error field="products" />
                    </div>
                </div>
                <div class="modal fade" id="stockProductModal" tabindex="-1" aria-labelledby="stockProductModalLabel"
                    aria-modal="false" role="dialog">
                    <div class="modal-dialog modal-fullscreen-lg-down modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="stockProductModalLabel">Stok Ürünleri</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="table-responsive">
                                    <table class="table bordered-table mb-0 dataTable" id="stockTable" width="100%">
                                        <!-- DataTable tarafından doldurulacak -->
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <div class="d-flex justify-content-between w-100">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                                    <button type="button" class="btn btn-primary" id="addSelectedProducts">Seçilen Ürünleri
                                        Ekle</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>Teklif Notu</h6>
                                <div class="row g-2">
                                    <div class="col-12">
                                        <textarea class="form-control" name="notes" rows="5" placeholder="Notunuzu yazın...">{{ old('notes', $item->notes ?? '') }}</textarea>
                                    </div>
                                    <x-form-error field="notes" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>Alt Toplamlar</h6>
                                <div class="row g-2">
                                    <div class="col-4 text-strat">Toplam</div>
                                    <div class="col-8">
                                        <div class="input-group">
                                            <input type="text" class="form-control text-end" id="total_price"
                                                name="total_price"
                                                value="{{ old('total_price', $item->total_price ?? '0,00') }}" readonly>
                                            <span class="input-group-text" id="currency_symbol_total" style="background-color: inherit; color: inherit;">₺</span>
                                        </div>
                                    </div>
                                    <div class="col-4 text-strat">KDV</div>
                                    <div class="col-8">
                                        <div class="input-group">
                                            <input type="text" class="form-control text-end" id="vat_amount"
                                                name="vat_amount"
                                                value="{{ old('vat_amount', $item->vat_amount ?? '0,00') }}" readonly>
                                            <span class="input-group-text" id="currency_symbol_vat" style="background-color: inherit; color: inherit;">₺</span>
                                        </div>
                                    </div>
                                    <div class="col-4 text-strat">Genel Toplam</div>
                                    <div class="col-8">
                                        <div class="input-group">
                                            <input type="text" class="form-control text-end" id="total_amount"
                                                name="total_amount"
                                                value="{{ old('total_amount', $item->total_amount ?? '0,00') }}" readonly>
                                            <span class="input-group-text" id="currency_symbol_total_amount" style="background-color: inherit; color: inherit;">₺</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="col-lg-12 text-end">
                            <button type="submit" class="btn btn-primary">Kaydet</button>
                        </div>
                    </div>
                </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {
            $('#current_id').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var degree = selectedOption.data('degree') || '';
                var address = selectedOption.data('address') || '';
                $('#address').val(address);
            });
            if ($('#current_id').val()) {
                var selectedOption = $('#current_id').find('option:selected');
                var degree = selectedOption.data('degree') || '';
                var address = selectedOption.data('address') || '';
                $('#address').val(address);
            }
        });
    </script>

    <script>
        $(document).ready(function() {
            var hasApprovedProducts = false;
            @if(isset($item) && $item->id && isset($item->offerProducts))
                @foreach($item->offerProducts as $product)
                    @if($product->status == 1)
                        hasApprovedProducts = true;
                    @endif
                @endforeach
            @endif
            if (hasApprovedProducts) {
                $('#branch_id').prop('disabled', true);
                $('#warehouse_id').prop('disabled', true);
                $('#offer_date').prop('disabled', true);
                $('#exchange_rate_id').prop('disabled', true);
                $('#current_id').prop('disabled', true);
                $('#shipping_address').prop('disabled', true);
                $('#payment_type_id').prop('disabled', true);
                $('#offer_deadline').prop('disabled', true);
                $('textarea[name="notes"]').prop('disabled', true);
                $('#addProductBtn').prop('disabled', true).css('opacity', '0.5').css('cursor', 'not-allowed');
                Swal.fire({
                    title: 'Uyarı!',
                    text: 'Bu teklifte onaylanmış ürünler bulunduğu için teklif bilgileri değiştirilemez ve yeni ürün eklenemez.',
                    icon: 'warning',
                    confirmButtonText: 'Tamam'
                });
            }
        });
    </script>

    <script>
        $(document).ready(function() {
            function updateExchangeRateInfo() {
                var selectedOption = $('#exchange_rate_id').find('option:selected');
                if (selectedOption.val()) {
                    var sellingRate = selectedOption.data('selling-rate') || '';
                    var symbol = selectedOption.data('symbol') || '';
                    var formattedRate = parseFloat(sellingRate).toFixed(2).replace('.', ',');
                    $('#selling_rate').val(formattedRate);
                    $('#currency_symbol').text(symbol);
                } else {
                    $('#selling_rate').val('');
                    $('#currency_symbol').text('');
                }
            }
            $('#exchange_rate_id').on('change', function() {
                updateExchangeRateInfo();
            });
            updateExchangeRateInfo();
        });
    </script>

    <script>
        $(document).ready(function() {
            $('#addProductBtn').on('click', function() {
                var selectedWarehouseId = $('#warehouse_id').val();
                if (!selectedWarehouseId) {
                    Swal.fire({
                        title: 'Uyarı!',
                        text: 'Lütfen önce bir depo seçiniz.',
                        icon: 'warning',
                        confirmButtonText: 'Tamam'
                    });
                    return;
                }
                loadStockProducts();
                var modal = $('#stockProductModal');
                modal.modal('show');
                modal.removeAttr('aria-hidden');
                modal.attr('aria-modal', 'true');
            });
            $('#stockProductModal').on('shown.bs.modal', function() {
                $(this).removeAttr('aria-hidden');
                $(this).attr('aria-modal', 'true');
            });
            $('#stockProductModal').on('hidden.bs.modal', function() {
                $(this).find('button, input, select').blur();
                $(this).find('input[type="checkbox"]').prop('checked', false);
                $(this).attr('aria-hidden', 'true');
                $(this).attr('aria-modal', 'false');
            });
            var originalBootstrapModal = $.fn.modal;
            $.fn.modal = function(option) {
                if (option === 'show') {
                    $(this).removeAttr('aria-hidden');
                    $(this).attr('aria-modal', 'true');
                }
                return originalBootstrapModal.apply(this, arguments);
            };
        });
    </script>
    <script>
        var allStockData = [];
        @foreach ($stocks as $stock)
        var availableStock = {{ $stock->quantity - $stock->stockReservations->sum('quantity')}};
        allStockData.push({
            stock_code: "{{ $stock->product->sku }}",
            product_name: "{{ $stock->product->name }}",
            category: "{{ $stock->variant->name  ?? '-' }}",
            // blocked_quantity: "{{ $stock->stockReservations->sum('quantity') }}",
            piece: "{{ $stock->quantity }}",
            available_stock: availableStock,
            unit: "{{ $stock->product->unit->name }}",
            purchase_price: "{{ number_format($stock->product->purchase_price, 2, ',', '.') }}",
            sale_price: "{{ number_format($stock->product->sale_price, 2, ',', '.') }}",
            vat_rate: "{{ $stock->product->default_vat_rate }}",
            warehouse_id: "{{ $stock->warehouse_id }}",
                    checkbox: `<div class="form-check style-check d-flex align-items-center">
                <input type="checkbox" class="form-check-input select-product-checkbox" style="appearance: auto;"
                    data-id="{{ $stock->id }}"
                    data-stock-code="{{ $stock->product->sku }}"
                    data-product-name="{{ isset($stock->variant) && $stock->variant && $stock->variant->name && $stock->variant->name != '-' ? $stock->variant->name : $stock->product->name }}"
                    data-unit="{{ $stock->product->unit->name}}"
                    data-sale-price="{{ $stock->product->sale_price }}"
                    data-vat-rate="{{ $stock->product->default_vat_rate }}">
              </div>`
        });
        @endforeach

        var stockDataTable;
        function loadStockProducts() {
            if ($.fn.DataTable.isDataTable('#stockTable')) {
                stockDataTable.destroy();
            }
            var selectedWarehouseId = $('#warehouse_id').val();
            var filteredData = allStockData.filter(function(item) {
                return item.warehouse_id == selectedWarehouseId;
            });
            stockDataTable = $('#stockTable').DataTable({
                data: filteredData,
                columns: [
                    {
                        data: 'checkbox',
                        title: `<div class="form-check style-check d-flex align-items-center">
                                  <input type="checkbox" class="form-check-input" id="selectAllProducts" style="appearance: auto;">
                                </div>`,
                        orderable: false,
                        className: ''
                    },
                    { data: 'stock_code', title: 'Stok Kodu &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'product_name', title: 'Ürün Adı &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'category', title: 'Kategori &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'piece', title: 'Toplam Stok &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    // { data: 'blocked_quantity', title: 'Bloke Miktar &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'available_stock', title: 'Kullanılabilir Stok &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'unit', title: 'Birim &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'purchase_price', title: 'Alış Fiyatı &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'sale_price', title: 'Satış Fiyatı &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                    { data: 'vat_rate', title: 'KDV Oranı (%) &nbsp;&nbsp;&nbsp;', className: 'text-center' },
                ],
                language: {
                    url: "//cdn.datatables.net/plug-ins/1.11.5/i18n/tr.json",
                    paginate: {
                        first: '«',
                        previous: '‹',
                        next: '›',
                        last: '»'
                    }
                },
                pageLength: 10,
                responsive: true,
                scrollX: true,
                dom: '<"dt-layout-row"<"dt-layout-cell dt-start"l><"dt-layout-cell dt-end"f>><"dt-layout-row dt-layout-table"<"dt-layout-cell"tr>><"dt-layout-row"<"dt-layout-cell dt-start"i><"dt-layout-cell dt-end"p>>',
                columnDefs: [
                    { targets: 0, orderable: false, className: '' }
                ],
                headerCallback: function(thead) {
                    $(thead).find('th').eq(0).removeClass().addClass('sortable');
                },
                createdRow: function(row, data, dataIndex) {
                    if (dataIndex === 0) {
                        $(row).find('th').removeClass().addClass('sortable');
                    }
                },
                initComplete: function() {
                    $('#selectAllProducts').on('click', function() {
                        $('.select-product-checkbox').prop('checked', $(this).prop('checked'));
                    });
                    $('#stockProductModal').on('shown.bs.modal', function() {
                        $($.fn.dataTable.tables(true)).DataTable().columns.adjust();
                    });
                }
            });
        }
    </script>
    <script>
        $(document).ready(function() {
            $('#addSelectedProducts').on('click', function() {
                var selectedCheckboxes = $('.select-product-checkbox:checked');
                if (selectedCheckboxes.length === 0) {
                    Swal.fire({
                        title: 'Uyarı!',
                        text: 'Lütfen en az bir ürün seçiniz.',
                        icon: 'warning',
                        confirmButtonText: 'Tamam'
                    });
                    return;
                }
                selectedCheckboxes.each(function() {
                    var checkbox = $(this);
                    var id = checkbox.data('id');
                    var stockCode = checkbox.data('stock-code');
                    var productName = checkbox.data('product-name');
                    var unit = checkbox.data('unit');
                    var salePrice = checkbox.data('sale-price');
                    var vatRate = checkbox.data('vat-rate'); // KDV oranını al
                    addProductToTable(id, stockCode, productName, unit, salePrice , vatRate);
                });
                $('#selectAllProducts').prop('checked', false);
                $('.select-product-checkbox').prop('checked', false);
                $('#addSelectedProducts').blur();
                $('#stockProductModal').modal('hide');
            });
            function addProductToTable(id, stockCode, productName, unit, salePrice , vatRate) {
                var vatIncluded = 'H';
                vatRate = typeof vatRate !== 'undefined' ? vatRate : 20; // Eğer gelmezse 20 olsun
                var existingProduct = $(`#productTable tbody tr[data-id="${id}"]`);
                if (existingProduct.length > 0) {
                    var quantityInput = existingProduct.find('.product-quantity');
                    var currentQuantity = parseInt(quantityInput.val());
                    quantityInput.val(currentQuantity + 1);
                    updateAmount(existingProduct);
                    return;
                }
                var offerNumber = $('#js_offer_number').val() || '';
                var productCount = $('#productTable tbody tr').length + 1;
                var itemNo = offerNumber + '-' + String(productCount).padStart(2, '0');

                var newRow = `
                    <tr data-id="${id}">
                        <td>${itemNo}</td>
                        <td>${stockCode}</td>
                        <td>${productName}</td>
                        <td>
                            <input type="number" class="form-control product-quantity" name="products[${id}][quantity]" value="1" min="1" style="width: 80px;">
                        </td>
                        <td>${unit}</td>
                        <td>
                            <input type="text" class="form-control product-price" name="products[${id}][price]" value="${parseFloat(salePrice).toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}" style="width: 150px;">
                        </td>
                        <td>
                            <select class="form-control form-control-sm" name="products[${id}][vat_status]" style="min-width: 83px;">
                                <option value="1">Dahil</option>
                                <option value="0" selected>Hariç</option>
                            </select>
                        </td>
                        <td>
                            <select class="form-control product-currency" name="products[${id}][currency]" style="width: 100px;">
                                @foreach ($exchangeRate as $rate)
                                <option value="{{ $rate->id }}" data-selling-rate="{{ $rate->selling_rate }}"" {{ $rate->code == 'TRY' ? 'selected' : '' }}>{{ $rate->code }}</option>
                                @endforeach
                            </select>
                            <input type="hidden" name="products[${id}][exchange_rate]" class="product-exchange-rate" value="1">
                            <input type="hidden" name="products[${id}][item_no]" value="${itemNo}">
                        </td>
                        <td>
                            <input type="text" class="form-control product-amount" value="${parseFloat(salePrice).toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}" readonly style="width: 158px;">
                            <input type="hidden" name="products[${id}][amount]" value="${salePrice}">
                            <input type="hidden" name="products[${id}][vat_amount]" value="0">
                            <input type="hidden" name="products[${id}][total_amount]" value="${salePrice}">
                        </td>
                        <td>
                            <input type="number" class="form-control form-control-sm" name="products[${id}][vat_rate]" value="${vatRate}" step="1.00" required style="width: 95px;">
                        </td>
                        <td>
                            <button type="button" class="btn btn-danger btn-sm remove-product">
                                <span>&#10006;</span>
                            </button>
                        </td>
                    </tr>
                `;
                $('#productTable tbody').append(newRow);
                $('.remove-product').off('click').on('click', function() {
                    $(this).closest('tr').remove();
                    updateTotals();
                });
                $('.product-quantity, .product-price, input[name$="[vat_rate]"], select[name$="[vat_status]"]').off('change').on('change',
                    function() {
                        updateAmount($(this).closest('tr'));
                    });
                $('.product-currency').off('change').on('change', function() {
                    var row = $(this).closest('tr');
                    updateAmount(row);
                    updateTotals();
                });
                var newRowElement = $('#productTable tbody tr[data-id="' + id + '"]');
                updateAmount(newRowElement);
            }
            function updateAmount(row) {
                var quantity = parseInt(row.find('.product-quantity').val()) || 0;
                var priceStr = row.find('.product-price').val().replace(/\./g, '').replace(',', '.');
                var price = parseFloat(priceStr) || 0;
                var currencySelect = row.find('.product-currency');
                var selectedOption = currencySelect.find('option:selected');
                var currencyCode = selectedOption.text(); // Döviz kodu (TRY, USD, EUR vb.)
                var exchangeRate = parseFloat(selectedOption.data('selling-rate')) || 1;
                var vatRate = parseFloat(row.find('input[name$="[vat_rate]"]').val()) || 0;
                var vatStatus = parseInt(row.find('select[name$="[vat_status]"]').val()) === 1;
                row.find('.product-exchange-rate').val(exchangeRate);
                if (quantity > 0 && price > 0) {
                    calculateAllProducts();
                }
            }
            function updateTotals() {
                calculateAllProducts();
            }
            function calculateAllProducts() {
                var totalPrice = 0;
                var totalVatAmount = 0;
                var totalAmount = 0;
                $('#productTable tbody tr').each(function() {
                    var row = $(this);
                    var quantity = parseFloat(row.find('.product-quantity').val()) || 0;
                    var priceStr = row.find('.product-price').val().replace(/\./g, '').replace(',', '.');
                    var price = parseFloat(priceStr) || 0;
                    var currencySelect = row.find('.product-currency');
                    var selectedOption = currencySelect.find('option:selected');
                    var exchangeRate = parseFloat(selectedOption.data('selling-rate')) || 1;
                    var vatRate = parseFloat(row.find('input[name$="[vat_rate]"]').val()) || 0;
                    var vatStatus = parseInt(row.find('select[name$="[vat_status]"]').val()) === 1;
                    var amount = 0;
                    if (selectedOption.text() === 'TRY') {
                        amount = quantity * price;
                    } else {
                        amount = quantity * (price / exchangeRate);
                    }
                    var vatAmount = 0;
                    if (vatStatus) {
                        vatAmount = amount - (amount / (1 + (vatRate / 100)));
                    } else {
                        vatAmount = amount * (vatRate / 100);
                    }
                    var totalLineAmount = vatStatus ? amount : amount + vatAmount;
                    row.find('.product-amount').val(amount.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                    row.find('input[name$="[amount]"]').val(amount);
                    row.find('input[name$="[vat_amount]"]').val(vatAmount);
                    row.find('input[name$="[total_amount]"]').val(totalLineAmount);
                    totalPrice += amount;
                    totalVatAmount += vatAmount;
                    totalAmount += totalLineAmount;
                });
                $('#total_price').val(totalPrice.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                $('#vat_amount').val(totalVatAmount.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                $('#total_amount').val(totalAmount.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
            }
            $(document).ready(function() {
                @if(isset($item) && $item->id && isset($item->offerProducts) && $item->offerProducts->count() > 0)
                    @foreach($item->offerProducts as $product)
                        var id = {{ $product->stock_id }};
                        var stockCode = "{{ $product->product_code }}";
                        var productName = "{{ $product->product_name }}";
                        var unit = "{{ $product->unit }}";
                        var quantity = {{ $product->quantity }};
                        var price = {{ $product->unit_price }};
                        var vatRate = {{ $product->vat_rate }};
                        var vatStatus = {{ $product->vat_status }};
                        var exchangeRateId = {{ $product->exchange_rate_id }};
                        var amount = {{ $product->total_price }};
                        var status = {{ $product->status }}; // Ürün durumu
                        var itemNo = "{{ $product->item_no }}";
                        var newRow = `
                            <tr data-id="${id}">
                                <td>${itemNo || '-'}</td>
                                <td>${stockCode}</td>
                                <td>${productName}</td>
                                <td>
                                    <input type="number" class="form-control product-quantity" name="products[${id}][quantity]" value="${quantity}" min="1" style="width: 80px;"}>
                                </td>
                                <td>${unit}</td>
                                <td>
                                    <input type="text" class="form-control product-price" name="products[${id}][price]" value="${price.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}" style="width: 150px;"}>
                                </td>
                                <td>
                                    <select class="form-control form-control-sm" name="products[${id}][vat_status]" style="min-width: 83px;">
                                        <option value="1" ${vatStatus == 1 ? 'selected' : ''}>Dahil</option>
                                        <option value="0" ${vatStatus == 0 ? 'selected' : ''}>Hariç</option>
                                    </select>
                                </td>
                                <td>
                                    <select class="form-control product-currency" name="products[${id}][currency]" style="width: 100px;">
                                        @foreach ($exchangeRate as $rate)
                                        <option value="{{ $rate->id }}" data-selling-rate="{{ $rate->selling_rate }}" data-symbol="{{ $rate->symbol }}" ${exchangeRateId == {{ $rate->id }} ? 'selected' : ''}>{{ $rate->currency_code }}</option>
                                        @endforeach
                                    </select>
                                    <input type="hidden" name="products[${id}][exchange_rate]" class="product-exchange-rate" value="1">
                                </td>
                                <td>
                                    <input type="text" class="form-control product-amount" value="${amount.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}" readonly style="width: 158px;">
                                    <input type="hidden" name="products[${id}][amount]" value="${amount}">
                                    <input type="hidden" name="products[${id}][vat_amount]" value="0">
                                    <input type="hidden" name="products[${id}][total_amount]" value="${amount}">
                                    <input type="hidden" name="products[${id}][status]" value="${status}">
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" name="products[${id}][vat_rate]" value="${vatRate}" step="0.01" required style="width: 80px;">
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger btn-sm remove-product">
                                        <span>&#10006;</span>
                                    </button>
                                </td>
                            </tr>
                        `;
                        $('#productTable tbody').append(newRow);
                    @endforeach
                    $('.remove-product').off('click').on('click', function() {
                        $(this).closest('tr').remove();
                        updateTotals();
                    });
                    $('.product-quantity, .product-price, .product-currency, input[name$="[vat_rate]"], select[name$="[vat_status]"]').off('change').on('change',
                        function() {
                            updateAmount($(this).closest('tr'));
                        });
                @endif
                updateTotals();
            });
            $(document).on('click', '#selectAllProducts', function() {
                $('.select-product-checkbox').prop('checked', $(this).prop('checked'));
            });
            $(document).on('click', '.remove-product', function() {
                $(this).closest('tr').remove();
                updateTotals();
            });
            $('form').on('submit', function(e) {
                $('#productTable tbody tr').each(function() {
                    updateAmount($(this));
                });
                try {
                    var productData = {};
                    $('#productTable tbody tr').each(function() {
                        var row = $(this);
                        var stockId = row.data('id');
                        var itemNo = row.find('td:first').text();
                        var stockCode = row.find('td').eq(1).text();
                        var productName = row.find('td').eq(2).text();
                        var unit = row.find('td').eq(4).text();
                        productData[stockId] = {
                            'stock_id': stockId,
                            'item_no': itemNo,
                            'quantity': parseInt(row.find('.product-quantity').val()) || 1,
                            'price': parseFloat(row.find('.product-price').val().replace(/\./g, '').replace(',', '.')) || 0,
                            'currency': row.find('.product-currency').val(),
                            'exchange_rate_id': row.find('.product-currency').val(), // DÜZELTİLDİ
                            'exchange_rate': parseFloat(row.find('.product-exchange-rate').val()) || 1,
                            'vat_rate': parseFloat(row.find('input[name$="[vat_rate]"]').val()) || 20,
                            'vat_status': parseInt(row.find('select[name$="[vat_status]"]').val()) || 0,
                            'vat_included': parseInt(row.find('select[name$="[vat_status]"]').val()) || 0,
                            'amount': parseFloat(row.find('input[name$="[amount]"]').val()) || 0,
                            'vat_amount': parseFloat(row.find('input[name$="[vat_amount]"]').val()) || 0,
                            'total_amount': parseFloat(row.find('input[name$="[total_amount]"]').val()) || 0,
                            'product_code': stockCode,
                            'product_name': productName,
                            'unit': unit
                        };
                    });
                    $('input[name^="products"]').remove();
                    $.each(productData, function(stockId, product) {
                        $.each(product, function(key, value) {
                            var input = $('<input>').attr({
                                type: 'hidden',
                                name: 'products[' + stockId + '][' + key + ']',
                                value: value
                            });
                            $('form').append(input);
                        });
                    });
                } catch (error) {
                    console.error('Form submit error:', error);
                    Swal.fire({
                        title: 'Hata!',
                        text: 'Form gönderilirken bir hata oluştu: ' + error.message,
                        icon: 'error',
                        confirmButtonText: 'Tamam'
                    });
                    return false;
                }
            });
        });
    </script>
        <script>
            $(document).ready(function() {
                @if(old('products'))
                    var oldProducts = @json(old('products'));
                    $.each(oldProducts, function(stockId, product) {
                        var id = stockId;
                        var stockCode = product.product_code || '';
                        var productName = product.product_name || '';
                        var unit = product.unit || '';
                        var quantity = product.quantity || 1;
                        var price = product.price || 0;
                        var vatRate = product.vat_rate || 20;
                        var vatStatus = product.vat_status || 0;
                        var exchangeRateId = product.exchange_rate_id || '';
                        var amount = product.amount || 0;
                        var itemNo = product.item_no || '-';
                        var newRow = `
                            <tr data-id="${id}">
                                <td>${itemNo}</td>
                                <td>${stockCode}</td>
                                <td>${productName}</td>
                                <td>
                                    <input type="number" class="form-control product-quantity" name="products[${id}][quantity]" value="${quantity}" min="1" style="width: 80px;">
                                </td>
                                <td>${unit}</td>
                                <td>
                                    <input type="text" class="form-control product-price" name="products[${id}][price]" value="${parseFloat(price).toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}" style="width: 150px;">
                                </td>
                                <td>
                                    <select class="form-control form-control-sm" name="products[${id}][vat_status]" style="min-width: 83px;">
                                        <option value="1" ${vatStatus == 1 ? 'selected' : ''}>Dahil</option>
                                        <option value="0" ${vatStatus == 0 ? 'selected' : ''}>Hariç</option>
                                    </select>
                                </td>
                                <td>
                                    <select class="form-control product-currency" name="products[${id}][exchange_rate_id]" style="width: 100px;">
                                        @foreach ($exchangeRate as $rate)
                                        <option value="{{ $rate->id }}" data-selling-rate="{{ $rate->selling_rate }}" {{ (isset($rate->code) && $rate->code == 'TRY') ? 'selected' : '' }}>{{ $rate->code }}</option>
                                        @endforeach
                                    </select>
                                    <input type="hidden" name="products[${id}][exchange_rate]" class="product-exchange-rate" value="1">
                                    <input type="hidden" name="products[${id}][item_no]" value="${itemNo}">
                                </td>
                                <td>
                                    <input type="text" class="form-control product-amount" value="${parseFloat(amount).toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}" readonly style="width: 158px;">
                                    <input type="hidden" name="products[${id}][amount]" value="${amount}">
                                    <input type="hidden" name="products[${id}][vat_amount]" value="${product.vat_amount || 0}">
                                    <input type="hidden" name="products[${id}][total_amount]" value="${product.total_amount || amount}">
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" name="products[${id}][vat_rate]" value="${vatRate}" step="0.01" required style="width: 80px;">
                                </td>
                                <td>
                                    <button type="button" class="btn btn-danger btn-sm remove-product">
                                        <span>&#10006;</span>
                                    </button>
                                </td>
                            </tr>
                        `;
                        $('#productTable tbody').append(newRow);
                    });
                    $('.remove-product').off('click').on('click', function() {
                        $(this).closest('tr').remove();
                        updateTotals();
                    });
                    $('.product-quantity, .product-price, .product-currency, input[name$="[vat_rate]"], select[name$="[vat_status]"]').off('change').on('change',
                        function() {
                            updateAmount($(this).closest('tr'));
                        });
                    updateTotals();
                @endif
            });
    </script>
@endsection
