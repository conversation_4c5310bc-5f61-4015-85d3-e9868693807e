@extends('layout.layout')

@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
@endphp

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.'.$container->page .'_save', $item->id) }}" method="POST">
                        @csrf

                        <!-- <PERSON><PERSON><PERSON> Tipi (hidden) -->
                        <input type="hidden" name="transaction_type" value="2"> <!-- 1: Tahsilat -->

                        <div class="row mb-3">
                            <!-- <PERSON><PERSON><PERSON> Tarihi -->
                            <div class="col-md-4">
                                <label for="transaction_date" class="form-label">İşlem Tarihi</label>
                                <input type="datetime-local" class="form-control" id="transaction_date" name="transaction_date" value="{{ old('transaction_date', $item->transaction_date ? $item->transaction_date->format('Y-m-d\TH:i') : now()->format('Y-m-d\TH:i')) }}" required>
                            </div>

                            <!-- Fatura Tipi -->
                            <div class="col-md-4">
                                <label for="invoice-type" class="form-label">Fatura Tipi</label>
                                <select class="form-control" name="invoice_type" id="invoice-type" required>
                                    <option value="">Fatura Tipi Seçin</option>
                                    <option value="2" {{ old('invoice_type', $item->invoice_type) == 2 ? 'selected' : '' }}>Alış Faturası Faturası</option>
                                </select>
                            </div>

                            <!-- Fatura -->
                            <div class="col-md-4">
                                <label for="invoice-id" class="form-label">Fatura</label>
                                <select class="form-control" name="invoice_id" id="invoice-id" required>
                                    <option value="">Fatura Seçin</option>
                                    @foreach($purchaseInvoices as $invoice)
                                        <option value="{{ $invoice->id }}" {{ old('invoice_id', $item->invoice_id) == $invoice->id ? 'selected' : '' }}>
                                            {{ $invoice->invoice_no }} - {{ $invoice->current ? $invoice->current->name : 'Cari Yok' }} - {{ number_format($invoice->total_amount, 2, ',', '.') }} {{ $invoice->currency }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Cari -->
                            <div class="col-md-4">
                                <label for="current-name" class="form-label">Cari</label>
                                <input type="text" class="form-control" id="current-name" readonly>
                                <input type="hidden" name="current_id" id="current-id" value="{{ old('current_id', $item->current_id) }}" required>
                            </div>

                            <!-- Ödeme Yöntemi -->
                            <div class="col-md-4">
                                <label for="payment_method" class="form-label">Ödeme Yöntemi</label>
                                <select class="form-control" name="payment_method" id="payment_method" required>
                                    <option value="">Ödeme Yöntemi Seçin</option>
                                    @foreach($paymentMethods as $key => $value)
                                        <option value="{{ $key }}" {{ old('payment_method', $item->payment_method) == $key ? 'selected' : '' }}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Tutar -->
                            <div class="col-md-4">
                                <label for="amount" class="form-label">Kalan Tutar</label>
                                <input type="number" class="form-control" name="amount" id="amount" value="{{ old('amount', $item->amount) }}" step="0.01" min="0.01" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Para Birimi -->
                            <div class="col-md-4">
                                <label for="currency-code" class="form-label">Para Birimi</label>
                                <select class="form-control" name="currency" id="currency-code" required>
                                    <option value="TRY" {{ old('currency', $item->currency ?? 'TRY') == 'TRY' ? 'selected' : '' }}>Türk Lirası (₺)</option>
                                    <option value="USD" {{ old('currency', $item->currency) == 'USD' ? 'selected' : '' }}>Amerikan Doları ($)</option>
                                    <option value="EUR" {{ old('currency', $item->currency) == 'EUR' ? 'selected' : '' }}>Euro (€)</option>
                                    <option value="GBP" {{ old('currency', $item->currency) == 'GBP' ? 'selected' : '' }}>İngiliz Sterlini (£)</option>
                                </select>
                            </div>

                            <!-- Kur -->
                            <div class="col-md-4">
                                <label for="exchange-rate" class="form-label">Kur</label>
                                <input type="number" class="form-control" name="exchange_rate" id="exchange-rate" value="{{ old('exchange_rate', $item->exchange_rate ?? 1) }}" step="0.0001" min="0.0001" required>
                            </div>

                            <!-- Belge No -->
                            <div class="col-md-4">
                                <label for="document_no" class="form-label">Belge No</label>
                                <input type="text" class="form-control" name="document_no" id="document_no" value="{{ old('document_no', $item->document_no) }}">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Banka Hesabı -->
                            <div class="col-md-4">
                                <label for="bank_account" class="form-label">Banka Hesabı</label>
                                <input type="text" class="form-control" name="bank_account" id="bank_account" value="{{ old('bank_account', $item->bank_account) }}">
                            </div>

                            <!-- Vade Tarihi -->
                            <div class="col-md-4">
                                <label for="due_date" class="form-label">Vade Tarihi</label>
                                <input type="date" class="form-control" name="due_date" id="due_date" value="{{ old('due_date', $item->due_date ? $item->due_date->format('Y-m-d') : '') }}">
                            </div>

                            <!-- Aktif/Pasif -->
                            <div class="col-4">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <!-- Açıklama -->
                            <div class="col-md-12">
                                <label for="description" class="form-label">Açıklama</label>
                                <textarea class="form-control" name="description" id="description" rows="3">{{ old('description', $item->description) }}</textarea>
                            </div>
                        </div>

                        <!-- Fatura Bilgileri -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">Fatura Bilgileri</h5>
                                    </div>
                                    <div class="card-body" id="invoice-details">
                                        <div class="alert alert-info">
                                            Fatura seçildiğinde detaylar burada görüntülenecektir.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">Kaydet</button>
                                <a href="{{ route('backend.'.$container->page.'_list') }}" class="btn btn-secondary">İptal</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    $(document).ready(function() {
        // Fatura seçildiğinde
        $('#invoice-id').on('change', function() {
            const invoiceId = $(this).val();
            const invoiceType = $('#invoice-type').val();

            if (invoiceId && invoiceType) {
                // Fatura detaylarını getir
                $.ajax({
                    url: "{{ route('backend.'.$container->page.'_detail') }}",
                    type: "GET",
                    data: {
                        invoice_id: invoiceId,
                        invoice_type: invoiceType
                    },
                    success: function(response) {
                        if (response.success) {
                            const invoice = response.invoice;
                            const payments = response.payments;

                            // Cari bilgilerini doldur
                            $('#current-id').val(invoice.current_id);
                            $('#current-name').val(invoice.current_name);

                            // Para birimi ve kur bilgilerini doldur
                            $('#currency-code').val(invoice.currency).trigger('change');

                            // Kalan tutarı amount alanına doldur
                            $('#amount').val(invoice.remaining_amount);

                            // Fatura detaylarını göster
                            let detailsHtml = `
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Fatura No:</strong> ${invoice.invoice_no}</p>
                                        <p><strong>Fatura Tarihi:</strong> ${invoice.invoice_date}</p>
                                        <p><strong>Cari:</strong> ${invoice.current_name}</p>
                                        <p><strong>Durum:</strong> ${invoice.status_name}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Toplam Tutar:</strong> ${invoice.total_amount_formatted}</p>
                                        <p><strong>Ödenen Tutar:</strong> ${invoice.total_paid_formatted}</p>
                                        <p><strong>Kalan Tutar:</strong> ${invoice.remaining_amount_formatted}</p>
                                    </div>
                                </div>
                            `;

                            // Önceki ödemeler varsa göster
                            if (payments.length > 0) {
                                detailsHtml += `
                                    <div class="mt-3">
                                        <h6>Önceki Ödemeler</h6>
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Tarih</th>
                                                    <th>Ödeme Yöntemi</th>
                                                    <th>Tutar</th>
                                                    <th>Açıklama</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                `;

                                payments.forEach(function(payment) {
                                    detailsHtml += `
                                        <tr>
                                            <td>${payment.transaction_date}</td>
                                            <td>${payment.payment_method_name}</td>
                                            <td>${payment.amount_formatted}</td>
                                            <td>${payment.description || '-'}</td>
                                        </tr>
                                    `;
                                });

                                detailsHtml += `
                                            </tbody>
                                        </table>
                                    </div>
                                `;
                            }

                            $('#invoice-details').html(detailsHtml);
                        } else {
                            $('#invoice-details').html(`
                                <div class="alert alert-danger">
                                    ${response.error || 'Fatura detayları getirilirken bir hata oluştu.'}
                                </div>
                            `);
                        }
                    },
                    error: function() {
                        $('#invoice-details').html(`
                            <div class="alert alert-danger">
                                Fatura detayları getirilirken bir hata oluştu.
                            </div>
                        `);
                    }
                });
            } else {
                $('#invoice-details').html(`
                    <div class="alert alert-info">
                        Fatura seçildiğinde detaylar burada görüntülenecektir.
                    </div>
                `);
            }
        });

        // Sayfa yüklendiğinde fatura seçili ise detayları getir
        if ($('#invoice-id').val() && $('#invoice-type').val()) {
            $('#invoice-id').trigger('change');
        }
    });
</script>
@endsection
