@extends('layout.layout')

@php
    $title = $container->title;
    $subTitle = $container->title . ' Listesi';
@endphp

@section('content')
    <div class="card basic-data-table">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{{ $subTitle }}</h5>
            <div class="d-flex gap-2">
                <a href="{{ route('backend.' . $container->page . '_form') }}"
                    class="btn btn-primary btn-sm rounded-pill waves-effect waves-themed d-flex align-items-center btn-equal-width">
                    Ekle
                </a>
            </div>
        </div>
        <div class="card-body">
            <!-- Filtreler -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <label class="form-label">Cari</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="filter-current-name" placeholder="Cari seçiniz"
                            readonly>
                        <input type="hidden" id="filter-current" value="">
                        <button class="btn btn-outline-secondary" type="button" id="btn-select-current">
                            <iconify-icon icon="mdi:magnify" class="menu-icon"></iconify-icon>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Başlangıç Tarihi</label>
                    <input type="date" class="form-control" id="filter-start-date">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Bitiş Tarihi</label>
                    <input type="date" class="form-control" id="filter-end-date">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Fatura Tipi</label>
                    <select class="form-control" id="filter-invoice-type">
                        <option value="">Tüm Faturalar</option>
                        @foreach ($invoiceTypes as $key => $value)
                            <option value="{{ $key }}">{{ $value }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="table-responsive">
                <table datatable class="table bordered-table mb-0" id="dataTable" data-page-length="10">
                    <thead>
                        <tr>
                            <th scope="col" class="text-center">İşlem Tarihi</th>
                            <th scope="col" class="text-center">Cari</th>
                            <th scope="col" class="text-center">Fatura No</th>
                            <th scope="col" class="text-center">Tutar</th>
                            <th scope="col" class="text-center">Durum</th>
                            <th scope="col" class="text-center">İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Veriler AJAX ile yüklenecek -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Cari Seçim Modalı -->
    <div class="modal fade" id="currentSelectModal" tabindex="-1" aria-labelledby="currentSelectModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="currentSelectModalLabel">Cari Seçimi</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <input type="text" class="form-control" id="current-search" placeholder="Cari ara...">
                    </div>
                    <div class="table-responsive">
                        <table class="table bordered-table mb-0" id="current-table">
                            <thead>
                                <tr>
                                    <th>Cari Adı</th>
                                    <th>İşlem</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Tüm Cariler</strong></td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-sm btn-primary select-current" data-id=""
                                            data-name="Tüm Cariler">Seç</button>
                                    </td>
                                </tr>
                                @foreach ($currents as $current)
                                    <tr>
                                        <td>{{ $current->name }}</td>
                                        <td class="text-center">
                                            <button type="button" class="btn btn-sm btn-primary select-current"
                                                data-id="{{ $current->id }}" data-name="{{ $current->name }}">Seç</button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script>
        $(document).ready(function() {

            BaseCRUD.selector = "[datatable]";

            const table = BaseCRUD.ajaxtable({
                ajax: {
                    url: "{{ route('backend.' . $container->page . '_list') }}?datatable=true",
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        return $.extend({}, d, {
                            start_date: $('[filter-name="filter-start-date"]').val(),
                            end_date: $('[filter-name="filter-end-date"]').val(),
                            type_id: $('[filter-name="filter-current"]').val(),
                        });
                    }
                },
                columns: [{
                        data: 'transaction_date',
                        name: 'transaction_date',
                        className: 'text-center'
                    },
                    {
                        data: 'current.name',
                        name: 'current.name',
                        className: 'text-center'
                    },
                    {
                        data: 'invoice_no',
                        name: 'invoice_no',
                        className: 'text-center'
                    },
                    {
                        data: 'amount_formatted',
                        name: 'amount_formatted',
                        className: 'text-center'
                    },
                    {
                        render: function(data, type, row) {
                            return data == 1 ? '<span class="badge bg-success">Aktif</span>' :
                                '<span class="badge bg-danger">Pasif</span>';
                        },
                        data: 'is_active',
                        name: 'is_active',
                        className: 'text-center',
                        orderable: false,
                        searchable: false
                    },
                    {
                        render: function(data, type, row) {
                            return `
                        <td class="text-center">
                            <div class="d-flex align-items-center gap-10 justify-content-center">
                                <a href="{{ route('backend.' . $container->page . '_form') }}/${row.id}" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle">
                                    <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                                </a>
                                <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="${row.id}">
                                    <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                                </button>
                            </div>
                        </td>`;
                        },
                        data: null,
                        orderable: false,
                        searchable: false,
                        className: 'text-center act-col',
                    },
                ],
                order: [
                    [0, 'desc']
                ],
                pageLength: 15,
            });

            // Cari, işlem tipi veya tarih değiştiğinde anlık filtreleme yap
            $('#filter-current, #filter-invoice-type, #filter-start-date, #filter-end-date').on('change',
        function() {
                table.ajax.reload();
            });

            // Cari seçim modalını aç
            $('#btn-select-current').on('click', function() {
                $('#currentSelectModal').modal('show');
            });

            // Cari seçildiğinde
            $('.select-current').on('click', function() {
                const currentId = $(this).data('id');
                const currentName = $(this).data('name');

                $('#filter-current').val(currentId).trigger('change');
                $('#filter-current-name').val(currentName);

                $('#currentSelectModal').modal('hide');
            });

            // Cari arama
            $('#current-search').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('#current-table tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            BaseCRUD.delete("{{ route('backend.' . $container->page . '_delete') }}");

            $('[filter-name]').change(function() {
                $("[datatable] tbody").empty();
                $("[datatable]").DataTable().ajax.reload();
            });
        });
    </script>
@endsection
