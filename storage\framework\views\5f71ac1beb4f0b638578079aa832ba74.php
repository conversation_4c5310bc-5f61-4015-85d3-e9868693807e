<aside class="sidebar">
    <button type="button" class="sidebar-close-btn">
        <iconify-icon icon="radix-icons:cross-2"></iconify-icon>
    </button>
    <div>
        <a href="<?php echo e(route('backend.index')); ?>" class="sidebar-logo">
            <img src="<?php echo e(asset('assets/images/logo/ic_kodlio.png')); ?>" alt="site logo" class="light-logo">
            <img src="<?php echo e(asset('assets/images/logo/ic_white.png')); ?>" alt="site logo" class="dark-logo">
            <img src="<?php echo e(asset('assets/images/logo/ic_kodlio.png')); ?>" alt="site logo" class="logo-icon">
        </a>
    </div>
    <div class="sidebar-menu-area">
        <ul class="sidebar-menu" id="sidebar-menu">
            <li>
                <a href="<?php echo e(route('backend.index')); ?>">
                    <iconify-icon icon="solar:home-smile-angle-outline" class="menu-icon"></iconify-icon>
                    <span>Anasayfa</span>
                </a>
            </li>
            <!-- Sistem Yönetimi -->
            <?php if(Helpers::hasPermission(['user', 'role'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:cog" class="menu-icon"></iconify-icon>
                        <span>Sistem Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['user'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.user_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Kullanıcılar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['role'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.role_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i>
                                        <span>Roller</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <!-- Ürün Yönetimi -->
            <?php if(Helpers::hasPermission(['product', 'product_variant', 'product_unit_conversion'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:package" class="menu-icon"></iconify-icon>
                        <span>Ürün Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['product'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.product_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Ürünler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['product_variant'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.product_variant_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Ürün Varyantları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['product_unit_conversion'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.product_unit_conversion_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Birim Dönüşümü</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <!-- Marka ve Kategori Yönetimi -->
            <?php if(Helpers::hasPermission(['brand', 'category'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:tag-multiple" class="menu-icon"></iconify-icon>
                        <span>Marka & Kategori</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['brand'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.brand_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Markalar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['category'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.category_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Kategoriler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <!-- Birim Yönetimi -->
            <?php if(Helpers::hasPermission(['unit', 'unit_type'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:ruler" class="menu-icon"></iconify-icon>
                        <span>Birim Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['unit'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.unit_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Birimler</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['unit_type'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.unit_type_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Birim Türleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <!-- Depo Yönetimi -->
            <?php if(Helpers::hasPermission(['warehouse', 'warehouse_location'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:warehouse" class="menu-icon"></iconify-icon>
                        <span>Depo Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['warehouse'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.warehouse_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Depolar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['warehouse_location'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.warehouse_location_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Depo Lokasyonları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <!-- Stok Yönetimi -->
            <?php if(Helpers::hasPermission(['stock', 'stock_movement', 'stock_reservation', 'physical_count', 'stock_period'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:archive" class="menu-icon"></iconify-icon>
                        <span>Stok Yönetimi</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['stock'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.stock_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Stoklar</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['stock_movement'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.stock_movement_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Stok Hareketleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['stock_reservation'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.stock_reservation_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Stok Rezervasyonları</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['physical_count'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.physical_count_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Fiziksel Sayım</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['stock_period'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.stock_period_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Stok Dönemleri</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

            <?php if(Helpers::hasPermission(['order_received', 'order_given'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="material-symbols:order-approve-outline" class="menu-icon"></iconify-icon>
                        <span>Sipariş</span>
                    </a>
                    <ul class="sidebar-submenu">
                            <?php if(Helpers::hasPermission(['order_received'])): ?>
                                <li>
                                    <a href="<?php echo e(route('backend.order_received_list')); ?>">
                                        <i class="ri-circle-fill circle-icon text-success-main w-auto"></i> <span>Alınan
                                            Sipariş</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if(Helpers::hasPermission(['order_given'])): ?>
                                <li>
                                    <a href="<?php echo e(route('backend.order_given_list')); ?>">
                                        <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i> <span>Verilen
                                            Sipariş</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>
            <?php if(Helpers::hasPermission(['offer_received', 'offer_given'])): ?>
                <li class="dropdown">
                    <a href="javascript:void(0)">
                        <iconify-icon icon="mdi:file-document-outline" class="menu-icon"></iconify-icon>
                        <span>Teklif</span>
                    </a>
                    <ul class="sidebar-submenu">
                        <?php if(Helpers::hasPermission(['offer_received'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.offer_received_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i>  <span>Alınan
                                        Teklif</span>
                                    </a>
                            </li>
                        <?php endif; ?>
                        <?php if(Helpers::hasPermission(['offer_given'])): ?>
                            <li>
                                <a href="<?php echo e(route('backend.offer_given_list')); ?>">
                                    <i class="ri-circle-fill circle-icon text-success-main w-auto"></i> <span>Verilen
                                        Teklif</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </li>
            <?php endif; ?>

             <!-- Cariler Başlığı -->
            <?php if(Helpers::hasPermission(['current']) || Helpers::hasPermission(['balance'])): ?>
            <?php
                $carilerRoutes = [
                    'backend.current_list',
                    'backend.balance_list',
                    'backend.account_vouchers_list',
                    'backend.current_transactions_list',
                    'backend.current_stock_movements_list'
                ];
                $isCarilerActive = in_array(Route::currentRouteName(), $carilerRoutes);
            ?>
            <li class="dropdown <?php echo e($isCarilerActive ? 'dropdown-open' : ''); ?>">
                <a class="nav-link d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <iconify-icon icon="mdi:account-group" class="menu-icon"></iconify-icon>
                        <span>Cariler</span>
                    </div>
                    <i class="fas fa-chevron-down menu-arrow"></i>
                </a>
                <ul class="sidebar-submenu" style="<?php echo e($isCarilerActive ? 'display: block;' : 'display: none;'); ?>">
                    <?php if(Helpers::hasPermission(['current'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.current_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.current_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.current_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Cari Hesaplar</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['balance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.balance_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.balance_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.balance_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Cari Bakiye Tanımlama</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['finance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.account_vouchers_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.account_vouchers_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.account_vouchers_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Cari Hesap Fişleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['finance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.current_transactions_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.current_transactions_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.current_transactions_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Cari Finansal Hareketler</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['finance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.current_stock_movements_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.current_stock_movements_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.current_stock_movements_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Cari Stok Hareketleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </li>
            <?php endif; ?>

            <!-- Finans Başlığı -->
            <?php if(Helpers::hasPermission(['finance']) || Helpers::hasPermission(['finance_transaction']) || Helpers::hasPermission(['invoice']) || Helpers::hasPermission(['currency_type'])): ?>
            <?php
                $finansRoutes = [
                    'backend.finance_transaction_list',
                    'backend.invoices_list',
                    'backend.purchase_invoices_list',
                    'backend.receipt_transaction_list',
                    'backend.payment_transaction_list',
                    'backend.sales-waybills.index',
                    'backend.purchase_waybills_list',
                    'backend.expense_voucher_list'
                ];
                $isFinansActive = in_array(Route::currentRouteName(), $finansRoutes);
            ?>
            <li class="dropdown <?php echo e($isFinansActive ? 'dropdown-open' : ''); ?>">
                <a class="nav-link d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <iconify-icon icon="mdi:finance" class="menu-icon"></iconify-icon>
                        <span>Finans</span>
                    </div>
                    <i class="fas fa-chevron-down menu-arrow"></i>
                </a>
                <ul class="sidebar-submenu" style="<?php echo e($isFinansActive ? 'display: block;' : 'display: none;'); ?>">
                    
                    <?php if(Helpers::hasPermission(['invoice'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.invoices_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.invoices_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.invoices_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Satış Faturaları</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['invoice'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.purchase_invoices_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.purchase_invoices_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.purchase_invoices_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış Faturaları</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['finance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.receipt_transaction_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.receipt_transaction_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.receipt_transaction_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Tahsilat İşlemleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['finance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.payment_transaction_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.payment_transaction_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.payment_transaction_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Ödeme İşlemleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['invoice'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.sales-waybills.index' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.sales-waybills.index')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.sales-waybills.index' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-primary-600 w-auto"></i><span>Satış İrsaliyeleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['invoice'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.purchase_waybills_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.purchase_waybills_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.purchase_waybills_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-warning-main w-auto"></i><span>Alış İrsaliyeleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['finance'])): ?>
                        <li class="<?php echo e(Route::currentRouteName() == 'backend.expense_voucher_list' ? 'active-page' : ''); ?>">
                            <a href="<?php echo e(route('backend.expense_voucher_list')); ?>" class="d-flex align-items-center <?php echo e(Route::currentRouteName() == 'backend.expense_voucher_list' ? 'active-page' : ''); ?>">
                                <i class="ri-circle-fill circle-icon text-success-main w-auto"></i><span>Masraf Fişleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['exchange_rate'])): ?>
                        <li>
                            <a href="<?php echo e(route('backend.exchange_rate_list')); ?>">
                                <i class="ri-circle-fill circle-icon text-info-main w-auto"></i><span>Döviz Kurları</span>
                            </a>
                        </li>
                    <?php endif; ?>
                    <?php if(Helpers::hasPermission(['currency_type'])): ?>
                        <li>
                            <a href="<?php echo e(route('backend.currency_type_list')); ?>">
                                <i class="ri-circle-fill circle-icon text-danger-main w-auto"></i><span>Para Birimleri</span>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </li>
            <?php endif; ?>
            
        </ul>
    </div>
</aside>
<?php /**PATH C:\laragon\www\erp_web\resources\views/components/sidebar.blade.php ENDPATH**/ ?>