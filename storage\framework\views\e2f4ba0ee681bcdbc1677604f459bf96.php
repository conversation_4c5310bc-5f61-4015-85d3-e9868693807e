    <!-- jQuery library js -->
    <script src="<?php echo e(asset('assets/js/lib/jquery-3.7.1.min.js')); ?>"></script>
    <!-- Bootstrap js -->
    <script src="<?php echo e(asset('assets/js/lib/bootstrap.bundle.min.js')); ?>"></script>
    <!-- Apex Chart js -->
    <script src="<?php echo e(asset('assets/js/lib/apexcharts.min.js')); ?>"></script>
    <!-- Data Table js -->
    <script src="<?php echo e(asset('assets/js/lib/dataTables.min.js')); ?>"></script>
    <!-- Iconify Font js -->
    <script src="<?php echo e(asset('assets/js/lib/iconify-icon.min.js')); ?>"></script>
    <!-- jQuery UI js -->
    <script src="<?php echo e(asset('assets/js/lib/jquery-ui.min.js')); ?>"></script>
    <!-- Vector Map js -->
    <script src="<?php echo e(asset('assets/js/lib/jquery-jvectormap-2.0.5.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/lib/jquery-jvectormap-world-mill-en.js')); ?>"></script>
    <!-- Popup js -->
    <script src="<?php echo e(asset('assets/js/lib/magnifc-popup.min.js')); ?>"></script>
    <!-- Slick Slider js -->
    <script src="<?php echo e(asset('assets/js/lib/slick.min.js')); ?>"></script>
    <!-- prism js -->
    <script src="<?php echo e(asset('assets/js/lib/prism.js')); ?>"></script>
    <!-- file upload js -->
    <script src="<?php echo e(asset('assets/js/lib/file-upload.js')); ?>"></script>
    <!-- audioplayer -->
    <script src="<?php echo e(asset('assets/js/lib/audioplayer.js')); ?>"></script>

    <!-- main js -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>

    <script src="<?php echo e(asset('assets/js/basecrud.js')); ?>?v=<?php echo e(env('APP_VERSION')); ?>"></script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> <?php echo isset($script) ? $script : ''; ?>
    <script src="<?php echo e(asset('assets/js/lib/select2/select2.bundle.js')); ?>"></script>
    <script>
        $('.select2').select2();
    </script>
    <script type="text/javascript">
        <?php if(Session::has('success')): ?>
            Swal.fire('', '<?php echo e(session('success')); ?>', 'success');
        <?php elseif(Session::has('error')): ?>
            Swal.fire('', '<?php echo e(session('error')); ?>', 'error');
        <?php elseif(Session::has('warning')): ?>
            Swal.fire('', '<?php echo e(session('warning')); ?>', 'warning');
        <?php elseif(Session::has('info')): ?>
            Swal.fire('', '<?php echo e(session('info')); ?>', 'info');
        <?php endif; ?>
    </script>
    <script>
        <?php if(Session::has('error')): ?>
            toastr.error("<?php echo e(session()->get('error')); ?>")
        <?php endif; ?>
        <?php if(Session::has('message')): ?>
            toastr.success("<?php echo e(session()->get('message')); ?>")
        <?php endif; ?>
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.6/jquery.inputmask.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#phone").inputmask({
                mask: "0(999)999 99 99",
                placeholder: "0(___)___ __ __",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                autoUnmask: true
            });
            $("#tc").inputmask({
                mask: "99999999999",
                placeholder: "__________",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                autoUnmask: true
            });
            $("#iban").inputmask({
                mask: "TR99 9999 9999 9999 9999 9999 99",
                placeholder: "TR__ ____ ____ ____ ____ ____ __",
                showMaskOnHover: false,
                showMaskOnFocus: true,
            });
        });
    </script>
<?php /**PATH C:\laragon\www\erp_web\resources\views/components/script.blade.php ENDPATH**/ ?>