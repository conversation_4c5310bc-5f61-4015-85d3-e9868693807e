<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends BaseModel
{
    use SoftDeletes;
    
    protected $table = 'categories';

    protected $guarded = [];

    public function scopeFilter($query, $request)
    {
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }
        
        if ($request->has('parent_id') && !empty($request->parent_id)) {
            $query->where('parent_id', $request->parent_id);
        }
        return $query;
    }

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }
    
    public function childrenRecursive()
    {
        return $this->children()->with('childrenRecursive');
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'category_id');
    }

    public function brands()
    {
        return $this->belongsToMany(Brand::class, 'brand_categories', 'category_id', 'brand_id')
                    ->wherePivot('is_active', 1)
                    ->withPivot('is_active', 'created_by', 'updated_by', 'deleted_by')
                    ->withTimestamps();
    }

    public function allSubcategoryProducts()
    {
        $categoryIds = $this->getAllSubcategoryIds();
        return Product::whereIn('category_id', $categoryIds)->get();
    }
    
    public function getAllSubcategoryProductsCount()
    {
        $categoryIds = $this->getAllSubcategoryIds();
        return Product::whereIn('category_id', $categoryIds)->count();
    }
    
    public function getAllSubcategoryIds()
    {
        static $cache = [];
        
        if (isset($cache[$this->id])) {
            return $cache[$this->id];
        }
        
        $ids = collect([$this->id]);
        $this->collectSubcategoryIds($this, $ids);
        
        $cache[$this->id] = $ids->toArray();
        
        return $cache[$this->id];
    }
    
    private function collectSubcategoryIds($category, &$ids)
    {
        $children = Category::where('parent_id', $category->id)
            ->select('id', 'parent_id')
            ->get();
            
        foreach ($children as $child) {
            $ids->push($child->id); 
            $this->collectSubcategoryIds($child, $ids);
        }
    }
    
    public function getTotalProductsCountAttribute()
    {
        return $this->allSubcategoryProducts()->count();
    }
    
    public function getProductsCountAttribute()
    {
        if (array_key_exists('products_count', $this->attributes)) {
            return $this->attributes['products_count'];
        }
        
        return $this->products()->count();
    }
    
    public function getChildrenCountAttribute()
    {
        if (array_key_exists('children_count', $this->attributes)) {
            return $this->attributes['children_count'];
        }
        
        return $this->children()->count();
    }
}
