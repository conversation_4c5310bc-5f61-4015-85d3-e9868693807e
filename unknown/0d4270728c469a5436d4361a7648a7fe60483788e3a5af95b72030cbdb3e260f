@extends('layout.layout')
@php
    $title = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON><PERSON>');
    $subTitle = $container->title . (is_null($item->id) ? ' Ekle' : ' <PERSON><PERSON><PERSON>le');
@endphp

@section('content')
    <div class="row gy-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0 fs-6">{{ $container->title }} {{ !is_null($item->id) ? 'Düzenle' : 'Ekle' }}
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('backend.' . $container->page . '_save', ['unique' => $item->id]) }}"
                        method="POST">
                        @csrf
                        <div class="row gy-3">
                            <div class="col-12">
                                <label class="form-label">Ad</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" placeholder="Lütfen adınızı giriniz"
                                        value="{{ old('name') ?? ($item->name ?? '') }}" name="name">
                                    <x-form-error field="name" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Soyad</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="f7:person"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control"
                                        value="{{ old('surname') ?? ($item->surname ?? '') }}" name="surname"
                                        placeholder="Lütfen soyadınızı giriniz">
                                    <x-form-error field="surname" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Email</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="mage:email"></iconify-icon>
                                    </span>
                                    <input type="email" class="form-control"
                                        value="{{ old('email') ?? ($item->email ?? '') }}" name="email"
                                        placeholder="Lütfen e-posta adresi giriniz">
                                    <x-form-error field="email" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Phone</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:phone-calling-linear"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control" id="phone" name="phone"
                                        value="{{ old('phone') ?? ($item->phone ?? '') }}">
                                    <x-form-error field="phone" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Title</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <input type="text" class="form-control"
                                        value="{{ old('title') ?? ($item->title ?? '') }}" name="title"
                                        placeholder="Lütfen ünvan giriniz">
                                    <x-form-error field="title" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Password</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="solar:lock-password-outline"></iconify-icon>
                                    </span>
                                    <input type="password" class="form-control" placeholder="*******" name="password" autocomplete="off">
                                    <x-form-error field="password" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Role</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="role_id">
                                        <option value="">Lütfen rol seçiniz</option>
                                        @foreach ($roles as $role)
                                            <option {{ (old('role_id') ?? $item->role_id) == $role->id ? 'selected' : '' }}
                                                value="{{ $role->id }}">{{ $role->name }}</option>
                                        @endforeach
                                    </select>
                                    <x-form-error field="role_id" />
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Durum</label>
                                <div class="icon-field">
                                    <span class="icon">
                                        <iconify-icon icon="carbon:badge"></iconify-icon>
                                    </span>
                                    <select class="form-control form-select" name="is_active">
                                        <option value="1"
                                            {{ old('is_active', $item->is_active ?? 1) == 1 ? 'selected' : '' }}>Aktif
                                        </option>
                                        <option value="0"
                                            {{ old('is_active', $item->is_active ?? 1) == 0 ? 'selected' : '' }}>Pasif
                                        </option>
                                    </select>
                                    <x-form-error field="is_active" />
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary-600">Kaydet</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
