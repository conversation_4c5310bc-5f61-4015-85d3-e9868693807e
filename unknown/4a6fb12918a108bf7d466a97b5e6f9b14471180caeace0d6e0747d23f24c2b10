<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->integer('product_id');
            $table->string('name', 191)->nullable();
            $table->string('sku', 191)->unique();
            $table->string('description', 255)->nullable();
            $table->decimal('weight', 10, 3)->default(0)->nullable();
            $table->decimal('width', 10, 2)->default(0)->nullable();
            $table->decimal('height', 10, 2)->default(0)->nullable();
            $table->decimal('length', 10, 2)->default(0)->nullable();
            $table->decimal('volume', 10, 4)->default(0)->nullable();
            $table->string('barcode')->nullable()->unique();
            $table->decimal('purchase_price', 10, 2)->default(0)->comment('Temel alış fiyatı');
            $table->decimal('sale_price', 10, 2)->default(0)->comment('Temel satış fiyatı');
            $table->string('purchase_currency_code', 10)->nullable();
            $table->string('sale_currency_code', 10)->nullable();
            $table->integer('default_vat_rate')->default(0);
            $table->integer('critical_stock_level')->default(0);
            $table->tinyInteger('is_active')->default(1);
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->integer('deleted_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
