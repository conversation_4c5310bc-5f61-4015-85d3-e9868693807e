<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use Illuminate\Database\Seeder;

class WarehouseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $warehouses = [
            [
                'name' => 'Ana Depo',
                'code' => 'ANA-01',
                'description' => 'Merkez ana depo',
                'address' => 'İstanbul, Ümraniye, Sanayi <PERSON>, Depo Caddesi No:1',
            ],
            [
                'name' => 'İzmir Depo',
                'code' => 'IZM-01',
                'description' => 'İzmir bölge deposu',
                'address' => 'İzmir, Bornova, Organize Sanayi Bölgesi, 5. Cadde No:10',
            ],
            [
                'name' => 'Ankara Depo',
                'code' => 'ANK-01',
                'description' => 'Ankara bölge deposu',
                'address' => 'Ankara, Sincan, Organize Sanayi Bölgesi, 15. Cadde No:25',
            ],
            [
                'name' => 'Hammadde Deposu',
                'code' => 'HAM-01',
                'description' => 'Hammad<PERSON> ve yarı mamul deposu',
                'address' => 'İstanbul, Ümraniye, Sanayi <PERSON>, Depo Caddesi No:2',
            ],
            [
                'name' => 'İhracat Deposu',
                'code' => 'IHR-01',
                'description' => 'İhracat ürünleri deposu',
                'address' => 'İstanbul, Tuzla, Serbest Bölge, 3. Cadde No:8',
            ],
        ];

        foreach ($warehouses as $warehouse) {
            Warehouse::updateOrCreate(
                ['name' => $warehouse['name']],
                [
                    'name' => $warehouse['name'],
                    'code' => $warehouse['code'],
                    'description' => $warehouse['description'],
                    'address' => $warehouse['address'],
                    'is_active' => 1,
                ]
            );
        }
    }
}
