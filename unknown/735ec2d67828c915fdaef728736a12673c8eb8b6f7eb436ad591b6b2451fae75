<?php

namespace App\Http\Controllers\Backend;

use App\Http\Requests\ExpenseVoucherRequest;
use App\Libraries\Helpers;
use App\Models\ExpenseVoucher;
use App\Models\ExpenseVoucherItem;
use App\Models\Current;
use App\Models\ExpenseType;
use App\Models\InvoiceCounter;
use App\Models\PaymentType;
use Illuminate\Http\Request;

class ExpenseVouchersController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = 'Masraf <PERSON>şleri';
        $this->page = 'expense_voucher';
        $this->model = ExpenseVoucher::class;
        $this->relation = ['current', 'expenseType', 'paymentType'];
        $this->upload = 'exponse-vouchers';
        $this->view = (object) [
            'breadcrumb' => [
                'Masraf <PERSON>şleri' => route('backend.expense_voucher_list'),
            ],
        ];

        view()->share('currents', Current::where('is_active', 1)->get());
        view()->share('expenseTypes', ExpenseType::where('is_active', 1)->orderBy('sort_order')->get());
        view()->share('paymentTypes', PaymentType::where('is_active', 1)->orderBy('sort_order')->get());

        parent::__construct();
    }



    public function datatableHook($obj)
    {
        return $obj
            ->editColumn('voucher_date', function ($item) {
                return $item->voucher_date ? $item->voucher_date->format('d.m.Y') : '-';
            })
            ->addColumn('current_name', function ($item) {
                return $item->current ? $item->current->name : '-';
            })
            ->addColumn('expense_type_name', function ($item) {
                return $item->expenseType ? $item->expenseType->name : '-';
            })
            ->addColumn('payment_type_name', function ($item) {
                return $item->paymentType ? $item->paymentType->name : '-';
            })
            ->addColumn('status_name', function ($item) {
                $statusClasses = [
                    'pending' => 'bg-warning-subtle text-warning',
                    'approved' => 'bg-success-subtle text-success',
                    'rejected' => 'bg-danger-subtle text-danger'
                ];
                $statusNames = [
                    'pending' => 'Beklemede',
                    'approved' => 'Onaylandı',
                    'rejected' => 'Reddedildi'
                ];

                $class = $statusClasses[$item->status] ?? 'bg-secondary-subtle text-secondary';
                $name = $statusNames[$item->status] ?? 'Bilinmiyor';
                return '<span class="badge ' . $class . '">' . $name . '</span>';
            })
            ->editColumn('grand_total', function ($item) {
                return number_format(floatval($item->grand_total), 2, ',', '.') . ' ₺';
            })
            ->addColumn('actions', function ($item) {
                $editUrl = route('backend.expense_voucher_form', $item->id);

                return '
                    <div class="d-flex align-items-center gap-10 justify-content-center">
                        <a href="' . $editUrl . '" class="bg-success-focus text-success-600 bg-hover-success-200 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" title="Düzenle">
                            <iconify-icon icon="lucide:edit" class="menu-icon"></iconify-icon>
                        </a>
                        <button type="button" class="remove-item-btn bg-danger-focus bg-hover-danger-200 text-danger-600 fw-medium w-40-px h-40-px d-flex justify-content-center align-items-center rounded-circle" row-delete="' . $item->id . '" title="Sil">
                            <iconify-icon icon="fluent:delete-24-regular" class="menu-icon"></iconify-icon>
                        </button>
                    </div>
                ';
            })
            ->rawColumns(['status_name', 'actions']);
    }

    public function formHook($item)
    {
        if (!$item->exists) {
            $item->voucher_no = $this->generateVoucherNumber();
            $item->voucher_date = now()->format('Y-m-d');
            $item->status = 'pending';
            $item->is_active = 1;
            $item->auto_accounting = true;
            $item->vat_total = 0;
            $item->subtotal = 0;
            $item->grand_total = 0;
        }

        return $item;
    }

    private function generateVoucherNumber()
    {
        // Bu metot artık Helper tarafından yönetiliyor
        return \App\Libraries\Helpers::generateNextSequenceNumber('MSF');
    }

    public function saveHook(Request $request)
    {
        $params = $request->all();

        if (empty($params['voucher_no'])) {
            $params['voucher_no'] = \App\Libraries\Helpers::generateNextSequenceNumber('MSF');
        }

        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            if (number_format($file->getSize() / 1048576, 1) > 11)
                return redirect()->back()->with('error', 'Dosya formatı 10MB büyük olamaz.')->withInput();
            if (strtolower($file->getClientOriginalExtension()) == "php" || strtolower($file->getClientOriginalExtension()) == "js" || strtolower($file->getClientOriginalExtension()) == "py")
                return redirect()->back()->with('error', 'Dosya yüklenemedi.')->withInput();
            $attachment = md5(rand(1, 999999) . date('ymdhis')) . '.' . strtolower($file->getClientOriginalExtension());
            $file->move("upload/$this->upload", $attachment);
            $params['attachment'] = $attachment;
        }

        return $params;
    }



    public function deleteHook($item)
    {
        ExpenseVoucherItem::where('expense_voucher_id', $item->id)->delete();
        return true;
    }
}
