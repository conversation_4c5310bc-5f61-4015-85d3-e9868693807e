<?php

namespace Database\Seeders;

use App\Models\UnitType;
use Illuminate\Database\Seeder;

class UnitTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $unitTypes = [
            [
                'name' => 'Ağırlık',
                'description' => 'Ağırlık ölçü birimleri',
            ],
            [
                'name' => 'Uzunluk',
                'description' => 'Uzunluk ölçü birimleri',
            ],
            [
                'name' => 'Hacim',
                'description' => 'Hacim ölçü birimleri',
            ],
            [
                'name' => 'Sıvı',
                'description' => 'Sıvı ölçü birimleri',
            ],
            [
                'name' => 'Alan',
                'description' => 'Alan ölçü birimleri',
            ],
            [
                'name' => 'Adet',
                'description' => 'Sayılabilir birimler',
            ],
            [
                'name' => 'Beden',
                'description' => 'Giyim bedenleri',
            ],
            [
                'name' => 'Ayakkabı Numarası',
                'description' => '<PERSON>yakkabı numaraları',
            ],
        ];

        foreach ($unitTypes as $unitType) {
            UnitType::updateOrCreate(
                ['name' => $unitType['name']],
                [
                    'name' => $unitType['name'],
                    'description' => $unitType['description'],
                    'is_active' => 1,
                ]
            );
        }
    }
}
