<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Order extends  BaseModel
{
    use SoftDeletes;

    protected $table = 'orders';

    protected $guarded = [];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->useLogName($this->getTable())
            ->setDescriptionForEvent(fn(string $eventName) => "{$eventName}")
            ->logOnlyDirty();
    }
    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function exchangeRate()
    {
        return $this->belongsTo(ExchangeRate::class);
    }

    public function current()
    {
        return $this->belongsTo(Current::class)->withTrashed();
    }

    public function paymentType()
    {
        return $this->belongsTo(PaymentType::class);
    }

    public function orderType()
    {
        return $this->belongsTo(OrderType::class);
    }

    public static function generateOrderNumber($orderTypeId = null)
    {
        $datePrefix = Carbon::now()->format('Ymd'); // 20250429
        $maxSequence = 0;
        $orders = self::withTrashed()
            ->where('order_number', 'like', $datePrefix . '%')
            ->get(['order_number']);

        foreach ($orders as $order) {
            $sequence = (int)substr($order->order_number, -2);
            if ($sequence > $maxSequence) {
                $maxSequence = $sequence;
            }
        }

        $nextSequence = str_pad($maxSequence + 1, 5, '0', STR_PAD_LEFT);

        return $datePrefix . $nextSequence;
    }

    public function orderProducts()
    {
        return $this->hasMany(OrderProduct::class);
    }

    public function processOrderProducts(array $products): void
    {
        $existingProducts = OrderProduct::withTrashed()
            ->where('order_id', $this->id)
            ->get()
            ->keyBy('stock_id');

        $incomingStockIds = [];
        foreach ($products as $line) {
            $stockId = $line['stock_id'];
            $incomingStockIds[] = $stockId;
            $stock = Stock::find($stockId);
            $exchangeRate = ExchangeRate::find($line['exchange_rate_id']);
            $currencyType = $exchangeRate ? ($exchangeRate->currency_code ?? $exchangeRate->code) : 'TRY';
            $orderNumber = $this->order_number;
            $itemNo = $line['item_no'] ?? ($existingProducts[$stockId]->item_no ?? OrderProduct::generateItemNo($orderNumber));
            $orderProductData = [
                'order_id' => $this->id,
                'stock_id' => $stockId,
                'product_code' => $stock->product->sku,
                'product_name' => $stock->variant->name ?? $stock->product->name,
                'quantity' => $line['quantity'],
                'unit' => $stock->product->unit->name,
                'unit_price' => $line['price'],
                'exchange_rate_id' => $line['exchange_rate_id'],
                'exchange_rate' => $line['exchange_rate'],
                'currency_type' => $currencyType,
                'vat_rate' => $line['vat_rate'],
                'vat_status' => $line['vat_included'],
                'total_price' => $line['amount'],
                'item_no' => $itemNo,
                'is_active' => 1
            ];
            if (isset($existingProducts[$stockId])) {
                $existingProduct = $existingProducts[$stockId];
                $existingProduct->fill($orderProductData);
                $existingProduct->deleted_at = null;
                $existingProduct->save();
            } else {
                OrderProduct::create($orderProductData);
            }
        }
        $toDelete = $existingProducts->keys()->diff($incomingStockIds);
        foreach ($toDelete as $stockId) {
            $product = $existingProducts[$stockId];
            if (!$product->deleted_at) {
                $product->delete();
            }
        }
    }

    public static function boot()
    {
        parent::boot();

        static::deleting(function ($order) {
            foreach ($order->orderProducts()->withTrashed()->get() as $product) {
                if (is_null($product->deleted_at)) {
                    $product->delete();
                }
            }
        });
    }
    public function scopeFilter($query, $filter)
    {
        // Sadece order_date'e göre filtreleme, saat aralığı ile
        if (!is_null($filter->start_date) && !empty($filter->start_date) && !is_null($filter->end_date) && !empty($filter->end_date)) {
            $query->whereBetween('order_date', [
                Carbon::parse($filter->start_date . ' 00:00:00')->format('Y-m-d H:i:s'),
                Carbon::parse($filter->end_date . ' 23:59:59')->format('Y-m-d H:i:s'),
            ]);
        } elseif (!is_null($filter->start_date) && !empty($filter->start_date)) {
            $query->whereDate('order_date', Carbon::parse($filter->start_date)->toDateString());
        }

        if (isset($filter->current_id) && !empty($filter->current_id)) {
            $query->whereHas('current', function ($q) use ($filter) {
                $q->where('id', $filter->current_id);
            });
        }
        return $query;
    }
}
