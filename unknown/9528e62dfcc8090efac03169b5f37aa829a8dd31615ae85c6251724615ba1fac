<?php

namespace Database\Seeders;

use App\Models\StockMovementReason;
use App\Models\StockMovementType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StockMovementReasonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Önce StockMovementType'ların var olduğundan emin olalım
        $this->call(StockMovementTypeSeeder::class);

        // Hareket tiplerini alalım
        $girisType = StockMovementType::where('name', 'Giriş')->first();
        $cikisType = StockMovementType::where('name', 'Çıkış')->first();
        $transferType = StockMovementType::where('name', 'Transfer')->first();

        if (!$girisType || !$cikisType || !$transferType) {
            throw new \Exception('StockMovementType kayıtları bulunamadı!');
        }

        $reasons = [
            // G<PERSON><PERSON> nedenleri
            ['name' => 'Alım', 'description' => 'Mal alımı', 'movement_type_id' => $girisType->id],
            ['name' => 'İade Giriş', 'description' => 'Mal iadesi - Giriş', 'movement_type_id' => $girisType->id],
            ['name' => 'Transfer Giriş', 'description' => 'Depolar arası transfer - Giriş', 'movement_type_id' => $transferType->id],
            ['name' => 'Sayım Fazlası', 'description' => 'Sayım sonucu fazla', 'movement_type_id' => $girisType->id],
            ['name' => 'Numune Giriş', 'description' => 'Numune - Giriş', 'movement_type_id' => $girisType->id],

            // Çıkış nedenleri
            ['name' => 'Satış', 'description' => 'Mal satışı', 'movement_type_id' => $cikisType->id],
            ['name' => 'İade Çıkış', 'description' => 'Mal iadesi - Çıkış', 'movement_type_id' => $cikisType->id],
            ['name' => 'Transfer Çıkış', 'description' => 'Depolar arası transfer - Çıkış', 'movement_type_id' => $transferType->id],
            ['name' => 'Sayım Eksiği', 'description' => 'Sayım sonucu eksik', 'movement_type_id' => $cikisType->id],
            ['name' => 'Numune Çıkış', 'description' => 'Numune - Çıkış', 'movement_type_id' => $cikisType->id],
            ['name' => 'Fire', 'description' => 'Fire kayıp', 'movement_type_id' => $cikisType->id],
        ];

        foreach ($reasons as $reason) {
            StockMovementReason::updateOrCreate(
                [
                    'name' => $reason['name'],
                    'movement_type_id' => $reason['movement_type_id']
                ],
                $reason
            );
        }
    }
}
