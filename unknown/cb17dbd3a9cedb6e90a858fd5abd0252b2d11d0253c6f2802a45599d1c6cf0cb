<?php

namespace App\Http\Controllers\Backend;

use App\Models\Unit;
use App\Models\UnitType;
use Illuminate\Http\Request;

class UnitController extends BaseController
{
    use BasePattern;

    public function __construct()
    {
        $this->title = '<PERSON>irimler';
        $this->page = 'unit';
        $this->model = new Unit();
        $this->relation = ['unitType'];

        $this->view = (object)array(
            'breadcrumb' => array(
                'Birimler' => route('backend.unit_list'),
            ),
        );

        view()->share('unitTypes', UnitType::active()->get());
        
        parent::__construct();
    }
}
