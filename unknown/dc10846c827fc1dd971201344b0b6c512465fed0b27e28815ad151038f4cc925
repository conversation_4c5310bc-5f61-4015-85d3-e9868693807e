<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;

class StockBatch extends BaseModel
{
    use SoftDeletes;

    protected $table = 'stock_batches';

    protected $guarded = [];

    protected $casts = ['expiry_date' => 'datetime'];

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function variant()
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    public function current()
    {
        return $this->belongsTo(Current::class, 'current_id');
    }

    public function stocks()
    {
        return $this->hasMany(Stock::class, 'stock_batch_id');
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function serialNumbers()
    {
        return $this->hasMany(StockSerialNumber::class, 'stock_batch_id');
    }

    public function movementItems()
    {
        return $this->hasMany(StockMovementItem::class, 'stock_batch_id');
    }
}
